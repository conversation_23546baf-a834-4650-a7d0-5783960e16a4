<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gpf.fliggy.apps</artifactId>
        <groupId>com.taobao.gpf</groupId>
        <version>1.0.13</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gpf.fliggy.app-play-abroad</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.taobao.gpf</groupId>
            <artifactId>gpf.fliggy.domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelitems-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.gpf</groupId>
            <artifactId>gpf.fliggy.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelitems-platform-common</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alitrip.travel</groupId>
            <artifactId>travel-systemvendor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.vpp</groupId>
            <artifactId>vpp-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>

</project> 