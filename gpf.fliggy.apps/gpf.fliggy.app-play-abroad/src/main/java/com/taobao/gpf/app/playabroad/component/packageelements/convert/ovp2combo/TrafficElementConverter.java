package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.packageelements.TrafficPackageElementTypeEnum;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.element.TrafficElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class TrafficElementConverter extends AbstractElementConverter implements IElementConverter {
    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.TRAFFIC.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.URBAN_TRAFFIC.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        TrafficElement trafficElement = (TrafficElement) pElement;
        Product product = super.fromOvpElement(trafficElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();
        // 类型
        elementFeatures.add(new ElementFeature("ELEMENT_TYPE", Optional.ofNullable(TrafficPackageElementTypeEnum.getByCode(trafficElement.getTrafficType()))
                .map(TrafficPackageElementTypeEnum::getDesc).orElse(null)));
        // 标题
        elementFeatures.add(new ElementFeature("NAME", trafficElement.getName()));
        // 说明
        elementFeatures.add(new ElementFeature("DESC", trafficElement.getDesc()));
        // 商家编码
        elementFeatures.add(new ElementFeature("OUTER_ID", trafficElement.getOuterCode()));
        // POI_RELATED
        elementFeatures.add(new ElementFeature("POI_RELATED", "否"));

        elementFeatures.add(new ElementFeature(KEY_DETAIL_DESC, trafficElement.getAdditionalRemarks()));
        combo.getComboFeatures().put("ticketService", "");
        return product;
    }
}
