package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LocalTouristGuideElementGroupVO extends BasePlayElementGroupVO {

    /**
     * 语言
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.LanguageEnum
     */
    private List<String> language;

    /**
     * 数量
     */
    private Integer numberStock;

    /**
     * 数量规格类型
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum
     */
    private Integer type;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * 讲解类型
     * @see com.taobao.gpf.app.playabroad.constant.element.LocalTouristGuideBizTypeEnum
     */
    private String bizType;

    /**
     * 是否含
     */
    private Boolean hasTicket;

    /**
     * 讲师ID
     */
    private String relationLecturerId;

    /**
     * 讲师昵称
     */
    private String lecturerNickName;

    /**
     * 讲师名字
     */
    private String lecturerRealName;

}
