package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ElementBO<T extends BaseElementValueMapBO> {

    /**
     * 元素id
     */
    private Long id;

    /**
     * 元素类型
     */
    private Integer elementType;

    /**
     * 映射id
     */
    private Integer mappingId;

    /**
     * 元素值
     */
    private T valueMap;

}
