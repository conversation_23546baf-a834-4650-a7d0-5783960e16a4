package com.taobao.gpf.app.playabroad.component.systemdirect;

import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.FliggyIntegerSingleCheckFieldSchemaStrategy;
import com.taobao.top.schema.field.SingleCheckField;
import com.taobao.top.schema.rule.TipRule;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
public class SingleCheckSystemDirectFieldSchemaStrategy  extends FliggyIntegerSingleCheckFieldSchemaStrategy<BasicCompDO<Integer>> {

    @Override
    public String getName() {
        return "singleCheckSystemDirectFieldSchemaStrategy";
    }

    @Override
    protected void customRenderField(SingleCheckField field, BasicCompDO<Integer> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        super.customRenderField(field, compDO, param, compConfig, context);


    }
}
