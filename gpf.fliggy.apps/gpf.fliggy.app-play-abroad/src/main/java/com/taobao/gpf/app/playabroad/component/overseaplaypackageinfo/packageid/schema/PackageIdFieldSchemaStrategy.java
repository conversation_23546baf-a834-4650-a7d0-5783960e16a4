package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.packageid.schema;

import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.FliggyLongInputFieldSchemaStrategy;
import com.taobao.top.schema.field.InputField;
import com.taobao.top.schema.rule.TipRule;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
public class PackageIdFieldSchemaStrategy extends FliggyLongInputFieldSchemaStrategy<BasicCompDO<Long>> {

    @Override
    protected void customRenderField(InputField field, BasicCompDO<Long> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext config) {
        super.customRenderField(field, compDO, param, compConfig, config);

        field.add(new TipRule("值如果不存在不用自己生成，如果存在则保留"));
    }

    @Override
    public String getName() {
        return "packageIdFieldSchemaStrategy";
    }
}
