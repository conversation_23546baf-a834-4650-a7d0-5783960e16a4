package com.taobao.gpf.app.playabroad.component.agentinfo;

import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.FliggyStringInputFieldSchemaStrategy;
import com.taobao.top.schema.field.InputField;
import com.taobao.top.schema.rule.Rule;
import com.taobao.top.schema.rule.TipRule;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 境外玩乐业务身份下代订服务说明组件的schema策略类
 */
@Slf4j
public class PlayAbroadAgentInfoSchemaStrategy extends FliggyStringInputFieldSchemaStrategy<BasicCompDO<String>> {

    @Override
    public String getName() {
        return "playAbroadAgentInfoSchemaStrategy";
    }

    @Override
    protected void renderSchemaField(InputField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        super.renderSchemaField(field, compDO, param, compConfig, context);
        
        // 添加字段提示信息
        List<Rule> rules = new ArrayList<>();
        rules.add(new TipRule("请填写提供代订服务的旅行产品中，接待消费者并提供服务的完整主体名称"));
        field.addRules(rules);
    }
} 