package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TicketElementGroupVO extends BasePlayElementGroupVO {

    /**
     * 门票元素列表
     */
    private List<ElementVO<TicketElementValueMapVO>> elementValueMapList;

    /**
     * 可用日期类型
     * {@link com.fliggy.vacation.domain.enums.AvailableDateTypeEnum}
     */
    private Integer availableDateType;

    /**
     * 是否独立价库
     */
    private Boolean doesInDependentPriceStock;

    /**
     * 入园日期
     */
    private List<Integer> admissionDates;
}
