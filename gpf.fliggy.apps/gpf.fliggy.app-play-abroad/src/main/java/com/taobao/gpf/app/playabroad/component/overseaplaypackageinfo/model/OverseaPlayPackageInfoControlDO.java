package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model;

import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.model.pagemodel.RuleModel;
import com.alitrip.travel.travelitems.model.person.TravelPerson;
import com.fliggy.vic.common.shared.play.PlayTheme;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.domain.itemdirect.model.PlayAbroadPackageDirectInfo;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.travel.client.domain.dataobject.ComboSkuInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class OverseaPlayPackageInfoControlDO extends ControlDO {

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐id
     */
    private Long packageId;

    /**
     * 普通商品和二次预约只有一个sku，放在套餐维度好了
     */
    private Long skuId;

    /**
     * 商家编码
     */
    private String outId;

    /**
     * 二次预约或者普通商品，套餐原价，单位:元 
     */
    private String packagePrice;

    /**
     * 二次预约或者普通商品，套餐库存
     */
    private Integer packageStock;

    private List<RuleModel> customRules;

    /**
     * 套餐使用说明
     */
    private String packageDesc;
    
    /**
     * 主元素
     */
    private Integer mainElementType;

    /**
     * 搭配元素
     */
    private List<Integer> otherElementTypes;
    
    /**
     * 套餐元素信息
     */
    private PackageElementsControlDO packageElementsControlDO = new PackageElementsControlDO();

    /**
     * 是否一口价
     */
    private Boolean fixedPrice = true;

    /**
     * 套餐内日历价库
     */
    private List<PlaySkuPriceStockControlDO> playCanlendePriceStockControlDO = new ArrayList<>();

    //用于处理日历商品skuid，前端不展示
    private ComboSkuInfo comboSkuInfo;

    private PlayThemeBO playThemeBO;

    /**
     * 套餐解析到vpp的玩法模型，只做暂存
     */
    private List<PlayTheme> playThemeList;

    /**
     * 出行人数模板id
     */
    private Long travelPersonTemplateId;

    /**
     * 套餐级别的直连配置，二次预约或者普通商品
     */
    private PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo;

    /**
     * 动态出行人信息，如果是直连且含有动态出行人的话
     */
    private TravelPerson travelPerson;

    /**
     * 费用包含
     */
    private String feeInclude;

    /**
     * 费用不含
     */
    private String feeExclude;

    /**
     * 活动时长，主元素为特色活动时该字段有效
     */
    private Integer activityHour;
}
