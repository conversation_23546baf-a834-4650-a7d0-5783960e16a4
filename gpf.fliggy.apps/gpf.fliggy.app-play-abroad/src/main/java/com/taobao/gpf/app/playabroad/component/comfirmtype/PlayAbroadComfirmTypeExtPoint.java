package com.taobao.gpf.app.playabroad.component.comfirmtype;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import lombok.extern.slf4j.Slf4j;

import static com.taobao.gpf.domain.constant.CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID;

/**
 * 境外玩乐确认类型组件扩展点
 */
@Slf4j
public class PlayAbroadComfirmTypeExtPoint implements IInjectExtension {

    @PrepareForRender(key = "play-abroad-set-comfirmType-required", desc = "设置确认类型组件必填")
    public void setRequired(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (compDO.getPagemodel() instanceof BasicPageModel) {
            ((BasicPageModel) compDO.getPagemodel()).setRequired(true);
        }
    }

    @PrepareForRender(key = "playAbroad-secondConfirm-descTip-addRules")
    public void ImmediatelyComfirmDescAddrules(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (categoryDO.getCategoryId() != PLAY_ABROAD_CATEGORY_ID){
            return;
        }
        InfoModel infoModel = new InfoModel();
        infoModel.setHelp(Lists.newArrayList("二次确认订单在支付后需再次确认买家是否可出行，确认失败自动退款;当订单直连方式为系统直连时，必须选择而二次确认"));
        compDO.getPagemodel().setInfo(infoModel);
    }
} 