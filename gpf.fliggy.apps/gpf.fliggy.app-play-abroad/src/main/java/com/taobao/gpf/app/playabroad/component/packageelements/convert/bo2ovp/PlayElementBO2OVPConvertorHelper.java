package com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp;

import com.fliggy.vpp.client.dto.response.line.element.*;
import com.fliggy.vpp.client.dto.response.line.hotel.Hotel;
import com.fliggy.vpp.client.dto.response.line.poi.PoiDTO;
import com.fliggy.vpp.client.dto.response.line.ticket.ScenicSpot;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.constant.element.CateringTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementTrafficEnum;
import com.taobao.gpf.app.playabroad.constant.element.SpaTypeEnum;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true), imports = {ElementTrafficEnum.class, CateringTypeEnum.class, SpaTypeEnum.class})
public interface PlayElementBO2OVPConvertorHelper {

    @Mapping(target = "admissionDates", qualifiedByName = "parseAdmissionDates")
    @Mapping(target = "scenicSpotChargeItem", source = "scenicSpotChargeItemBO")
    @Mapping(target = "specification.quantity", source = "numberStock")
    TicketElement toTicketElement(TicketElementValueMapBO sourceData);

    //    @Mapping(target = "hotel", source = "hotelBO")
    @Mapping(target = "hotel", source = "sourceData", qualifiedByName = "toHotel")
    @Mapping(target = "room", source = "roomBO")
    HotelElement toHotelElement(HotelElementValueMapBO sourceData);

    @Mapping(target = "trafficType", expression = "java(ElementTrafficEnum.getByDesc(sourceData.getElementType()).getType())")
    TrafficElement toTrafficElement(TrafficValueMapBO sourceData);

    PhoneCardElement toPhoneCardElement(PhoneCardValueMapBO sourceData);

    @Mapping(target = "poi", source = "sourceData", qualifiedByName = "toPoiDTO")
    @Mapping(target = "specification.quantity", source = "numberStock")
    @Mapping(target = "cateringBusinessType", source = "businessType")
    CateringElement toCateringElement(CateringValueMapBO sourceData);

    // 写死其他
    @Mapping(target = "activityType", defaultValue = "16")
    @Mapping(target = "poi", source = "sourceData", qualifiedByName = "toPoiDTO")
    @Mapping(target = "specification.quantity", source = "numberStock")
    SpecialActivityElement toSpecialActivityElement(SpecialActivityValueMapBO sourceData);

    @Mapping(target = "poi", source = "sourceData", qualifiedByName = "toPoiDTO")
    @Mapping(target = "specification.quantity", source = "numberStock")
    SpaElement toSpaElement(SpaValueMapBO sourceData);

    PhotographyElement toPhotographyElement(PhotographyValueMapBO sourceData);

    @Mapping(target = "poi", source = "sourceData", qualifiedByName = "toPoiDTO")
    @Mapping(target = "additionalRemarks", source = "sourceData.additionalRemarks")
    @Mapping(target = "specification.quantity", source = "numberStock")
    @Mapping(target = "specification.description", source = "type")
    DivingElement toDivingElement(DivingElementValueMapBO sourceData);

    @Mapping(target = "additionalRemarks", source = "valueMap.additionalRemarks")
    @Mapping(target = "specification.quantity", source = "valueMap.numberStock")
    @Mapping(target = "specification.description", source = "valueMap.type")
    @Mapping(target = "bizType", source = "valueMap.bizType")
    @Mapping(target = "hasTicket", source = "valueMap.hasTicket")
    @Mapping(target = "relationLecturerId", source = "valueMap.relationLecturerId")
    LocalTouristGuideElement localTouristGuideElement(ElementBO<LocalTouristGuideValueMapBO> sourceData);

    @Mapping(target = "poi", source = "poiId", qualifiedByName = "toPoiDTO")
    ScenicSpot toScenicSpot(ScenicSpotBO sourceData);

    @Named("parseAdmissionDates")
    static List<Integer> parseAdmissionDates(List<Integer> admissionDates) {
        if (Objects.isNull(admissionDates)) {
            return new ArrayList<>();
        }
        return admissionDates.stream().filter(Objects::nonNull).map(date -> date + 1).collect(Collectors.toList());
    }

    @Named("toPoiDTO")
    static PoiDTO toPoiDTO(String poiId) {
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(poiId);
        return poiDTO;
    }

    @Named("toHotel")
    static Hotel toPoiDTO(HotelElementValueMapBO sourceData) {
        Hotel hotel = new Hotel();
        hotel.setHotelId(sourceData.getHotelBO().getHotelId());
        hotel.setHotelName(sourceData.getHotelBO().getHotelName());
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(sourceData.getPoiId());
        poiDTO.setPoiName(sourceData.getName());
        hotel.setPoi(poiDTO);
        return hotel;
    }

    @Named("toPoiDTO")
    static PoiDTO toPoiDTO(CateringValueMapBO sourceData) {
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(sourceData.getPoiId());
        poiDTO.setPoiName(sourceData.getName());
        return poiDTO;
    }

    @Named("toPoiDTO")
    static PoiDTO toPoiDTO(SpecialActivityValueMapBO sourceData) {
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(sourceData.getPoiId());
        poiDTO.setPoiName(sourceData.getName());
        return poiDTO;
    }

    @Named("toPoiDTO")
    static PoiDTO toPoiDTO(SpaValueMapBO sourceData) {
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(sourceData.getPoiId());
        poiDTO.setPoiName(sourceData.getName());
        return poiDTO;
    }

    @Named("toPoiDTO")
    static PoiDTO toPoiDTO(DivingElementValueMapBO sourceData) {
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(String.valueOf(sourceData.getValue()));
        poiDTO.setPoiName(sourceData.getLabel());
        return poiDTO;
    }
}
