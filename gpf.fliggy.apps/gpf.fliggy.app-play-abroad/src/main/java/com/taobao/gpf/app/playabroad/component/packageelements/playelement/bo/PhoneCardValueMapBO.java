package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.constant.element.CellularNetworkTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.PhoneCardTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneCardValueMapBO extends BaseElementValueMapBO {

    /**
     * 网络类型
     *
     * @see CellularNetworkTypeEnum
     */
    private Integer cardType;

    /**
     * 卡类型
     *
     * @see PhoneCardTypeEnum
     */
    private Integer cellularNetworkType;
}
