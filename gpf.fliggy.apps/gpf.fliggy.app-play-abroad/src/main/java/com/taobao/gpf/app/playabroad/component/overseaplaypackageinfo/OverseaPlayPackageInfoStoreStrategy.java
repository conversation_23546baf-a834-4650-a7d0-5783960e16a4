package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.extpoint.store.ParentCompStoreStrategy;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 境外玩乐套餐存储策略
 */
@Slf4j
@Component
public class OverseaPlayPackageInfoStoreStrategy extends ParentCompStoreStrategy<ParentCompDO<AbstractCompDO>, TravelItemStoreDO> {

    @Autowired
    private OverseaPlayBO2ComboConvertor overseaPlayBO2ComboConvertor;

    @Autowired
    private Combo2OverseaPlayBOConvert combo2OverseaPlayBOConvert;

    @Override
    public String getName() {
        return "overseaPlayAbroadPackageInfoStoreStrategy";
    }

    @Override
    public void parseStore(ParentCompDO<AbstractCompDO> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        OverseaPlayPackageInfoListControlDO overseaPlayPackageInfoListControlDO = combo2OverseaPlayBOConvert.convertStoreDO2ControlDO(storeDO, param);
        compDO.setControlDO(overseaPlayPackageInfoListControlDO);
    }


    @Override
    public void transferStore(ParentCompDO<AbstractCompDO> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        BasicCompDO<Integer> dependCompDO = param.getDependCompDO(CompConstants.SELL_TYPE_COMP_NAME);
        if (dependCompDO == null || dependCompDO.getValue() == null) {
            return;
        }
        overseaPlayBO2ComboConvertor.convertControlDO2StoreDO(compDO.getControlDO(), storeDO, param);
    }
} 