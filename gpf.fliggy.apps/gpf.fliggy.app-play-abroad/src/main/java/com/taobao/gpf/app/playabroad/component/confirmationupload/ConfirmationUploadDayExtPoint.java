package com.taobao.gpf.app.playabroad.component.confirmationupload;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.compdo.TabbedCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.component.selltype.SellType;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/3 11:08
 **/
@Slf4j
public class ConfirmationUploadDayExtPoint implements IInjectExtension {


    @Check(key = "abroadPlay-confirmationUploadDay-check")
    public CheckResult check(CompExtParam param, TabbedCompDO<List<TextValueModel>> compDO, @Category StdCategoryDO category) {
        CheckResult result = new CheckResult();

        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID){
            return result;
        }

        BasicCompDO<Integer> sellType = param.getDependCompDO(CompConstants.SELL_TYPE_COMP_NAME);

        //日历商品才有确认书
        if (sellType == null || sellType.getValue() != SellType.Normal.getValue()) {
            return result;
        }

        AbstractCompDO navCompDO = compDO.getNavCompDO();
        String navValue = (String) navCompDO.getValue();
        if (navValue == null){
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("confirmationUploadDay",
                    "请选择确认书上传规则"));
            return result;
        }

        if ("customUpload".equals(navValue)){
            if(navCompDO instanceof ParentCompDO){
                List<BasicCompDO> children = ((ParentCompDO)navCompDO).getChildren();
                if(CollectionUtils.isNotEmpty(children)){
                    if(Objects.isNull(children.get(0).getValue())){
                        result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("confirmationUploadDay",
                                "请设置确认书上传自定义天数"));
                        return result;
                    }
                }
            }
        }
        return result;
    }


}
