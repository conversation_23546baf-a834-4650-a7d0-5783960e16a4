package com.taobao.gpf.app.playabroad.component.mainelement;

import com.alibaba.gpf.sdk.model.pagemodel.PageValueModel;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
public class ConfirmMainElementPageModel extends PageValueModel {

    private String confirm;

    @Override
    public JsonNode toJsonNode() {
        ObjectNode valueNode = JacksonUtil.createObjectNode();
        valueNode.putPOJO("value", this.value);
        if (StringUtils.isNotBlank(this.text)) {
            valueNode.put("text", this.text);
        }

        /*if (StringUtils.isNotBlank(this.confirm)) {
            valueNode.put("confirm", this.confirm);
        }*/

        if (this.disabled) {
            valueNode.put("disabled", this.disabled);
            valueNode.put("readonly", true);
        }

        if (this.info != null) {
            valueNode.putPOJO("info", this.info.toJsonNode());
        }

        return valueNode;
    }
}
