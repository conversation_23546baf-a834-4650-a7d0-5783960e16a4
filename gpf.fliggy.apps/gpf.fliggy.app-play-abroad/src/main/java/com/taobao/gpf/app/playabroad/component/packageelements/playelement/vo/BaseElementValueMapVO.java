package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaseElementValueMapVO {

    /**
     * 是否关联POI元素
     */
    @JsonProperty("POI_RELATED")
    private String poiRelated;

    /**
     * POI元素ID
     */
    @JsonProperty("POIID")
    private String poiId;

    /**
     * 元素名称
     */
    @JsonProperty("NAME")
    private String name;

    /**
     * 元素类型
     */
    @JsonProperty("ELEMENT_TYPE")
    private String elementType;

    /**
     * 商家编码
     */
    @JsonProperty("OUTER_ID")
    private String outerId;

    /**
     * 说明
     */
    @JsonProperty("DESC")
    private String desc;
}
