package com.taobao.gpf.app.playabroad.component.refundrule;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.annotation.bind.User;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.alitrip.travel.travelitems.model.TravelItemRefundRule;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.app.playabroad.constant.FliggyPlayAbroadErrorCodeEnum;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.component.refundrule.RefundRuleValueDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.uic.common.domain.BaseUserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/27 19:31
 **/
@Slf4j
public class OverseaPlayRefundRuleExtPoint implements IInjectExtension {

    final static Integer ELECTRONIC_CERTIFICATE = 1;


    @Check(key = "oversea-play-refundrule-check")
    public CheckResult checkHotelSceneryRefundRule(BasicCompDO<RefundRuleValueDO> compDO, @Category StdCategoryDO stdCategory, @User BaseUserDO baseUserDO, CompExtParam param) {

        CheckResult checkResult = new CheckResult();

        if (CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID != FliggyParamUtil.getCategoryId(param)) {
            return checkResult;
        }

        ParentCompDO<AbstractCompDO> extractWayCompDO = param.getDependCompDO(CompConstants.EXTRACT_WAY_COMP_NAME);
        if (extractWayCompDO != null && CollectionUtils.isNotEmpty(extractWayCompDO.getChildren())) {
            boolean etc = extractWayCompDO.getChildComps().stream().anyMatch(t -> "etc".equals(t.getCompName()));
            //电子凭证 退改规则屏蔽
            if (etc) {
                return checkResult;
            }
        }
        RefundRuleValueDO refundRuleValueDO = compDO.getValue();
        List<TravelItemRefundRule> balanceRefundValues = JSON.parseArray(refundRuleValueDO.getRefundRuleJson(),
                TravelItemRefundRule.class);
        if (CollectionUtils.isEmpty(balanceRefundValues)) {
            return checkResult;
        }

        for (TravelItemRefundRule balanceRefundValue : balanceRefundValues) {
            int sellerPercent = balanceRefundValue.getSellerPercent();
            int buyerPercent = balanceRefundValue.getPercent();
            if (sellerPercent < buyerPercent) {
                checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.BOOK_BEFORE_DAYS_NOT_LESS_THAN_BUYER_INDEMNITY.getErrorCode());
                return checkResult;
            }

            if (sellerPercent <= FliggySwitchConfig.SELLER_REFUND_MIN_PERCENT) {
                checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.BOOK_BEFORE_DAYS_NOT_LESS_THAN_RATIO.getErrorCode()
                        .putErrorParam("number",FliggySwitchConfig.SELLER_REFUND_MIN_PERCENT));
                return checkResult;
            }
        }
        return checkResult;
    }



}
