package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.element.TicketElement;
import com.fliggy.vpp.client.dto.response.line.poi.PoiDTO;
import com.fliggy.vpp.client.dto.response.line.ticket.ScenicSpot;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class TicketElementConverter extends AbstractElementConverter implements IElementConverter {
    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.TICKET.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.SCENIC.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        Product product = super.fromOvpElement(pElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        TicketElement ticketElement = (TicketElement) pElement;
        //城市
        if (Objects.nonNull(ticketElement.getDivision())) {
            elementFeatures.add(new ElementFeature("CITY", ticketElement.getDivision().getDivisionId()));
        }
        // 门票ID
        elementFeatures.add(new ElementFeature("TICKET_KIND_VID", String.valueOf(ticketElement.getTicketKindId())));
        if (Objects.nonNull(ticketElement.getScenicSpotChargeItem())) {
            elementFeatures.add(new ElementFeature("SCENIC_PRODUCT_ID", String.valueOf(ticketElement.getScenicSpotChargeItem().getScenicSpotChargeItemId())));
            elementFeatures.add(new ElementFeature("PRODUCT_NAME", String.valueOf(ticketElement.getScenicSpotChargeItem().getScenicSpotChargeItemName())));
            // POI
            elementFeatures.add(new ElementFeature("POIID", ticketElement.getScenicSpotChargeItem().getScenicSpots().stream()
                    .filter(Objects::nonNull).findFirst().map(ScenicSpot::getPoi).map(PoiDTO::getPoiId).orElse(null)));
        }
        //景点名
        elementFeatures.add(new ElementFeature("NAME", ticketElement.getScenicSpotName()));
        elementFeatures.add(new ElementFeature("SCENIC_SPOT_ID", ticketElement.getScenicSpotChargeItem().getScenicSpots().stream()
                .filter(Objects::nonNull).findFirst().map(ScenicSpot::getScenicSpotId).map(String::valueOf).orElse(null)));

        //收费项目
        //门票类型 需要标记票
        elementFeatures.add(new ElementFeature("ELEMENT_TYPE", String.valueOf(ticketElement.getTicketKindName())));
        //商家编码
        elementFeatures.add(new ElementFeature("OUTER_ID", ticketElement.getOuterCode()));
        // TICKET_KIND_PV
//        elementFeatures.add(new ElementFeature("SCENIC_ID", ticketElement.getsc()));

        combo.getComboFeatures().put("ticketService", "");
        return product;
    }
}
