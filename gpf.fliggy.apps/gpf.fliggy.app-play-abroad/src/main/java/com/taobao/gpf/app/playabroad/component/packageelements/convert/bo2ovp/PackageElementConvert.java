package com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp;

import com.fliggy.vic.common.constant.layout.TabFieldTypeEnum;
import com.fliggy.vpp.client.dto.response.line.element.*;
import com.google.common.collect.Lists;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Component
public class PackageElementConvert {

    @Resource
    PlayElementBO2OVPConvertor playElementBO2OVPConvertor;

    public List<ElementGroup> handleSubmitElementGroup(List<BasePlayElementGroupBO> elementGroups) {
        if (CollectionUtils.isEmpty(elementGroups)) {
            return null;
        }

        List<ElementGroup> elementGroupList = elementGroups.stream()
                .map(element -> {
                    ElementGroup elementGroup = new ElementGroup();
                    elementGroup.setElementCategoryEnum(element.getElementCategory());
                    elementGroup.setType(TabFieldTypeEnum.CHECKBOX.name());
                    elementGroup.setMinSelectNumber(1);
                    elementGroup.setMaxSelectNumber(1);
                    elementGroup.setElements(convert2OVPElement(element));
                    return elementGroup;
                }).collect(Collectors.toList());

        //过滤掉没有elements的元素
        List<ElementGroup> elementGroupListAfterFilter = new ArrayList<>();
        for (ElementGroup elementGroup : elementGroupList) {
            if (CollectionUtils.isNotEmpty(elementGroup.getElements())) {
                elementGroup.setMaxSelectNumber(elementGroup.getElements().size());
                elementGroupListAfterFilter.add(elementGroup);
            }
        }
        return elementGroupListAfterFilter;

    }

    private List<PackageElement> convert2OVPElement(BasePlayElementGroupBO elementGroupBO) {

        Integer elementCategory = elementGroupBO.getElementCategory();
        ElementCategoryEnum elementCategoryEnum = ElementCategoryEnum.getByType(elementCategory);
        if (Objects.isNull(elementCategoryEnum)) {
            return null;
        }

        List<PackageElement> elements = Lists.newArrayList();
        switch (elementCategoryEnum) {
            case TICKET:
                List<TicketElement> ticketElements = Optional.ofNullable(((TicketElementGroupBO) elementGroupBO).getTicketElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toTicketElement(value))
                        .collect(Collectors.toList());
                elements.addAll(ticketElements);
                break;
            case HOTEL:
                List<HotelElement> hotelElements = Optional.ofNullable(((HotelElementGroupBO) elementGroupBO).getHotelElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toHotelElement(value))
                        .collect(Collectors.toList());
                elements.addAll(hotelElements);
                break;
            case TRAFFIC:
                List<TrafficElement> trafficElements = Optional.ofNullable(((TrafficElementGroupBO) elementGroupBO).getTrafficElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toTrafficElement(value))
                        .collect(Collectors.toList());
                elements.addAll(trafficElements);
                break;
            case WIFI:
                List<PackageElement> wifiElements = Optional.ofNullable(((WifiElementGroupBO) elementGroupBO).getWifiElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toWifiElement(value))
                        .collect(Collectors.toList());
                elements.addAll(wifiElements);
                break;
            case PHONECARD:
                List<PhoneCardElement> phoneCardElements = Optional.ofNullable(((PhoneCardGroupBO) elementGroupBO).getPhoneCardElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toPhoneCardElement(value))
                        .collect(Collectors.toList());
                elements.addAll(phoneCardElements);
                break;
            case CATERING:
                List<CateringElement> cateringElements = Optional.ofNullable(((CateringElementGroupBO) elementGroupBO).getCaterElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toCateringElement(value, elementGroupBO))
                        .collect(Collectors.toList());
                elements.addAll(cateringElements);
                break;
            case SPECIAL_ACTIVITY:
                List<SpecialActivityElement> specialActivityElements = Optional.ofNullable(((SpecialActivityElementGroupBO) elementGroupBO).getSpecialActivityElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toSpecialActivityElement(value))
                        .collect(Collectors.toList());
                elements.addAll(specialActivityElements);
                break;
            case SPA:
                List<SpaElement> spaElements = Optional.ofNullable(((SpaElementGroupBO) elementGroupBO).getSpaElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toSpaElement(value, elementGroupBO))
                        .collect(Collectors.toList());
                elements.addAll(spaElements);
                break;
            case TOUR:
                List<LocalTouristGuideElement> tourElements = Optional.ofNullable(((LocalTouristGuideElementGroupBO) elementGroupBO).getLocalTouristGuideElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toLocalTouristGuideElement(value))
                        .collect(Collectors.toList());
                elements.addAll(tourElements);
                break;
            case PHOTOGRAPHY:
                List<PhotographyElement> photographyElements = Optional.ofNullable(((PhotographyElementGroupBO) elementGroupBO).getPhotographyElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toPhotographyElement(value, elementGroupBO))
                        .collect(Collectors.toList());
                elements.addAll(photographyElements);
                break;
            case DIVING:
                List<DivingElement> divingElements = Optional.ofNullable(((DivingElementGroupBO) elementGroupBO).getDivingElementList())
                        .orElse(Lists.newArrayList()).stream().map(value -> playElementBO2OVPConvertor.toDivingElement(value, elementGroupBO))
                        .collect(Collectors.toList());
                elements.addAll(divingElements);
                break;
            default:
                break;
        }
        return elements;
    }

}
