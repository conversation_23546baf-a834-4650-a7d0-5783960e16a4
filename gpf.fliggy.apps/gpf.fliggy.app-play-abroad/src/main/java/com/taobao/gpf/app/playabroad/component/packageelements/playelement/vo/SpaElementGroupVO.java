package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpaElementGroupVO extends BasePlayElementGroupVO {

    /**
     * spa元素列表
     */
    private List<ElementVO<SpaValueMapVO>> elementValueMapList;

    /**
     * spa店用途范围
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.SpaUsageScopeEnum
     */
    private Integer spaUsageScope;
}
