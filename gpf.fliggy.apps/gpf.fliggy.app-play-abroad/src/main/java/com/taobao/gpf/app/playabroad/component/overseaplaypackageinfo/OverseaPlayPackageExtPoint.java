package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.common.exception.ErrorCode;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.Enable;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.alibaba.gpf.shared.componet.standarddate.DateRangeValueDO;
import com.fliggy.travel.guide.param.playtype.PlayActivityNode;
import com.fliggy.travel.guide.param.playtype.PlayType;
import com.fliggy.travel.guide.param.playtype.PlayTypeAttributes;
import com.taobao.ddcommon.util.DateUtil;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.constant.FliggyPlayAbroadErrorCodeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementTypeEnum;
import com.taobao.gpf.app.playabroad.model.CalendarPriceStockBO;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.check.BlackWordsCheckService;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodValueModel;
import com.taobao.gpf.domain.component.playmethod.model.PlayProperty;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.repository.PlayTypeHelpServiceRepo;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.GraySwitchUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 境外玩乐套餐扩展点
 */
public class OverseaPlayPackageExtPoint implements IInjectExtension {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Resource
    private BlackWordsCheckService blackWordsCheckService;

    @Resource
    private PlayTypeHelpServiceRepo playTypeHelpServiceRepo;

    private final static Integer MAX_STOCK_NUM = 1000000;


    @Enable(key = "oversea-package-enable")
    public boolean overseaPlayPackageInfoEnable(CompExtParam param, ParentCompDO<AbstractCompDO> compDO, @Category StdCategoryDO category){
        return travelSellConfig.getJingwaiWanletaocanCatId() == category.getCategoryId();
    }

    @Check(key = "check-oversea-play-packageName", desc = "套餐名称校验")
    public CheckResult checkPackageNameRule(CompExtParam param, ParentCompDO<AbstractCompDO> compDO, @Category StdCategoryDO categoryDO) {


        CheckResult checkResult = new CheckResult();
        List<OverseaPlayPackageInfoControlDO> combos = Optional.ofNullable(compDO.getControlDO())
                .map(controlDO -> (OverseaPlayPackageInfoListControlDO) compDO.getControlDO())
                .map(OverseaPlayPackageInfoListControlDO::getPackageInfos)
                .orElse(null);
        // 命中违禁词的套餐名称list
        Set<String> packageNames = new HashSet<>();
        List<String> notAllowedPackageNames = new LinkedList<>();
        List<String> backWords = new LinkedList<>();
        int categoryId = categoryDO.getCategoryId();
        if (combos != null) {
            int index = 0;
            for (OverseaPlayPackageInfoControlDO combo : combos) {
                String packageName = combo.getPackageName();
                index++;
                if (StringUtils.isBlank(packageName)) {
                    checkResult.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("overseaPlayPackageInfo",
                            "套餐" + index + "名称不能为空"));
                    return checkResult;
                }
                boolean add = packageNames.add(packageName);
                if (!add) {
                    checkResult.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("overseaPlayPackageInfo",
                            "套餐名称不能重复"));
                    return checkResult;
                }
                if (blackWordsCheckService.containsAnyBlackWord(packageName, (long) categoryId, backWords)) {
                    notAllowedPackageNames.add(packageName);
                }
            }
        }

        if (notAllowedPackageNames.size() > 0) {
            checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.CHK_PACKAGE_NAME_NOT_ALLOWED_ERROR.getErrorCode()
                    .putErrorParam("packageName", StringUtils.join(notAllowedPackageNames, "、"))
                    .putErrorParam("text", StringUtils.join(backWords, "、"))
            );
        }


        return checkResult;
    }


    @Check(key = "play-abroad-packageElements-check", desc = "玩乐发品校验：套餐元素校验")
    public CheckResult checkPackageElements(CompExtParam param, ParentCompDO<AbstractCompDO> compDO, @Category StdCategoryDO categoryDO) {

        CheckResult checkResult = new CheckResult();
        Long sellerId = FliggyParamUtil.getUserDO(param).getUserId();
        List<OverseaPlayPackageInfoControlDO> combos = Optional.ofNullable(compDO.getControlDO())
                .map(controlDO -> (OverseaPlayPackageInfoListControlDO)compDO.getControlDO())
                .map(OverseaPlayPackageInfoListControlDO::getPackageInfos)
                .orElse(null);
        if(CollectionUtils.isEmpty(combos)){
            return checkResult;
        }

        List<PlayActivityNode> specialActivityType = playTypeHelpServiceRepo.getSpecialActivityType();
        Set<String> nameSet = initializeNameSet(specialActivityType);
        Set<Integer> nightNumber = new HashSet<>();
        Set<Integer> mainElementList = new HashSet<>();
        Set<Integer> matchElementList = new HashSet<>();
        for (OverseaPlayPackageInfoControlDO overseaPlayPackageInfoControlDO : combos) {
            PackageElementsControlDO packageElementsControlDO = overseaPlayPackageInfoControlDO.getPackageElementsControlDO();
            List<BasePlayElementGroupBO> elementInfoList = packageElementsControlDO.getElementInfoList();
            if (CollectionUtils.isEmpty(elementInfoList)) {
                continue;
            }

            //判断套餐元素是否存在主元素
            Integer mainElementType = overseaPlayPackageInfoControlDO.getMainElementType();
            if (Objects.isNull(mainElementType)) {
                checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.CHK_PACKAGE_MUST_HAVE_MAIN_ELEMENT.getErrorCode());
                return checkResult;
            }

            int nightNum = 0;
            for (BasePlayElementGroupBO basePlayElementGroupBO : elementInfoList) {

                //统计主元素、搭配元素
                if (Objects.equals(basePlayElementGroupBO.getElementType(), ElementTypeEnum.MAIN.getType())) {
                    mainElementList.add(basePlayElementGroupBO.getElementType());
                }else {
                    matchElementList.add(basePlayElementGroupBO.getElementType());
                }


                /*特色活动元素类型必填校验*/
                if (Objects.equals(basePlayElementGroupBO.getElementCategory(), ElementCategoryEnum.SPECIAL_ACTIVITY.getType())) {
                    for (ElementBO<SpecialActivityValueMapBO> specialActivityValueMap : ((SpecialActivityElementGroupBO) basePlayElementGroupBO).getSpecialActivityElementList()) {
                        if (Objects.nonNull(specialActivityValueMap.getValueMap())) {
                            if (Objects.isNull(specialActivityValueMap.getValueMap().getElementType())) {
                                checkResult.addErrorCode(FliggyErrorEnum.SPECIAL_ACTIVITY_ELEMENT_DATA_TYPE_NOT_NULL.getErrorCode());
                            } else {
                                if (!GraySwitchUtils.isOnFor(FliggySwitchConfig.PLAY_ACTIVITY_GRAY_SWITCH_CONFIG, sellerId)) {
                                    continue;
                                }
                                boolean isValidSpecialActivityElement = nameSet.contains(specialActivityValueMap.getValueMap().getElementType());
                                if (!isValidSpecialActivityElement) {
                                    ErrorCode errorCode = FliggyErrorEnum.SPECIAL_ACTIVITY_ELEMENT_INVALID.getErrorCode()
                                            .putErrorParam("element", specialActivityValueMap.getValueMap().getName());
                                    checkResult.addErrorCode(errorCode);
                                }
                            }
                        }
                    }
                }

                //酒店元素为主类型 间夜数统计
                if (Objects.equals(basePlayElementGroupBO.getElementCategory(), ElementCategoryEnum.HOTEL.getType()) &&
                        Objects.equals(basePlayElementGroupBO.getElementType(), ElementTypeEnum.MAIN.getType())) {
                    HotelElementGroupBO hotelElementGroupBO = (HotelElementGroupBO) basePlayElementGroupBO;
                    for (ElementBO<HotelElementValueMapBO> hotelElementValueMapBOElementBO : hotelElementGroupBO.getHotelElementList()) {
                        nightNum += hotelElementValueMapBOElementBO.getValueMap().getNightNumber();
                    }
                }
            }

            nightNumber.add(nightNum);

        }
        if (nightNumber.size() > 1) {
            checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.MAIN_ELEMENT_HOTEL_NIGHT_NUMBER.getErrorCode());
            return checkResult;
        }

        if (Objects.equals(mainElementList.size(), 0)) {
            checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.CHK_PACKAGE_MUST_HAVE_MAIN_ELEMENT.getErrorCode());
            return checkResult;
        }
        if (mainElementList.size() > FliggySwitchConfig.ABROAD_PLAY_MAIN_ELEMENT_NUM) {
            checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.MAIN_ELEMENT_ONLY_ONE.getErrorCode()
                    .putErrorParam("num", FliggySwitchConfig.ABROAD_PLAY_MAIN_ELEMENT_NUM));
            return checkResult;
        }

        for (Integer elementCategory : mainElementList) {
            if (matchElementList.contains(elementCategory)) {
                checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.MAIN_ELEMENT_NOT_SUPPLY_ELEMENT.getErrorCode());
                return checkResult;
            }
        }
        return checkResult;
    }



    @Check(key = "oversea-package-info-check", desc = "玩乐发品校验：套餐校验")
    public CheckResult checkPackageInfo(CompExtParam param, ParentCompDO<AbstractCompDO> compDO, @Category StdCategoryDO categoryDO) {

        CheckResult checkResult = new CheckResult();
        List<OverseaPlayPackageInfoControlDO> combos = Optional.ofNullable(compDO.getControlDO())
                .map(controlDO -> (OverseaPlayPackageInfoListControlDO)compDO.getControlDO())
                .map(OverseaPlayPackageInfoListControlDO::getPackageInfos)
                .orElse(null);
        if(CollectionUtils.isEmpty(combos)){
            return checkResult;
        }

        // 套餐数量校验
        Integer abroadPlayPackageNumRestrict = FliggySwitchConfig.ABROAD_PLAY_PACKAGE_NUM_RESTRICT;
        if (abroadPlayPackageNumRestrict != null && combos.size() > abroadPlayPackageNumRestrict) {
            checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.PACKAGE_NUMBER_ERROR_MAX_TWELVE.getErrorCode()
                    .putErrorParam("num", abroadPlayPackageNumRestrict));
            return checkResult;
        }
        BasicCompDO<Integer> sellType = param.getDependCompDO(CompConstants.SELL_TYPE_COMP_NAME);
        if (sellType == null){
            return checkResult;
        }

        BasicCompDO<DateRangeValueDO> dependCompDO = param.getDependCompDO(CompConstants.SECOND_START_END_COMBO_DATE);
        if (dependCompDO == null || dependCompDO.getValue() == null || dependCompDO.getValue().getStartDate() == null || dependCompDO.getValue().getEntDate() == null) {
            return checkResult;
        }

        Date checkInStartDate = dependCompDO.getValue().getStartDate();
        Date checkInEndDate = dependCompDO.getValue().getEntDate();
        for (OverseaPlayPackageInfoControlDO combo : combos){
            //todo 独立价库且有酒店元素时，套餐名称校验
            //日历商品
            if (Objects.equals(sellType.getValue(), 1)){
                List<PlaySkuPriceStockControlDO> playCanlendePriceStockControlDO = combo.getPlayCanlendePriceStockControlDO();
                if (CollectionUtils.isEmpty(playCanlendePriceStockControlDO)){
                    checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_PRICE_STOCK_NULL.getErrorCode());
                    return checkResult;
                }

                for (PlaySkuPriceStockControlDO playSkuPriceStockControlDO : playCanlendePriceStockControlDO) {
                    if (CollectionUtils.isEmpty(playSkuPriceStockControlDO.getCalendarPriceStock())){
                        checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_CALENDER_PRICE_STOCK_NULL.getErrorCode());
                        return checkResult;
                    }

                    for (CalendarPriceStockBO calendarPriceStockBO : playSkuPriceStockControlDO.getCalendarPriceStock()) {

                        Date priceStockDate = convertString2Date(calendarPriceStockBO.getDate());
                        Date currentDate = convertString2Date(DateUtil.getToday(DateUtil.YYYY_MM_DD));
                        // 如果价库为过期价库，则跳过该价库校验
                        if (priceStockDate.before(currentDate)) {
                            continue;
                        }

                        if (Objects.nonNull(calendarPriceStockBO.getStock())) {
                            if (calendarPriceStockBO.getStock() > MAX_STOCK_NUM) {
                                ErrorCode errorCode = FliggyPlayAbroadErrorCodeEnum.PACKAGE_NUMBER_ERROR_MAX_TWELVE.getErrorCode();
                                checkResult.addErrorCode(errorCode);
                                return checkResult;
                            }
                        }

                        if (priceStockDate.before(checkInStartDate) || priceStockDate.after(checkInEndDate)) {
                            checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.PACKAGE_CALENDER_PRICE_STOCK_NOT_IN_TIME_RANGE.getErrorCode());
                            return checkResult;
                        }
                    }


                }

            }
            //二次预约商品
            else if (Objects.equals(sellType.getValue() ,2)){
                String packagePrice = combo.getPackagePrice();
                Integer packageStock = combo.getPackageStock();
                if (StringUtils.isBlank(packagePrice) || Objects.isNull(packageStock)){
                    checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_PRICE_STOCK_NULL.getErrorCode());
                    return checkResult;
                }

                if (packageStock > MAX_STOCK_NUM) {
                    ErrorCode errorCode = FliggyPlayAbroadErrorCodeEnum.PACKAGE_STOCK_OVER_LIMIT.getErrorCode();
                    checkResult.addErrorCode(errorCode);
                    return checkResult;
                }

                if (packageStock <= 0) {
                    checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.PACKAGE_STOCK_MUST_GREATER_THAN_ZERO.getErrorCode());
                    return checkResult;
                }


                if (combo.getTravelPersonTemplateId() == null && combo.getTravelPerson() == null){
                    checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.PACKAGE_MUST_HAVE_PERSON_TEMPLATE.getErrorCode());
                    return checkResult;
                }

            }
            //普通商品
            else if (Objects.equals(sellType.getValue() ,3)){
                String packagePrice = combo.getPackagePrice();
                Integer packageStock = combo.getPackageStock();
                if (StringUtils.isBlank(packagePrice) || Objects.isNull(packageStock)){
                    checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_PRICE_STOCK_NULL.getErrorCode());
                    return checkResult;
                }

                if (packageStock > MAX_STOCK_NUM) {
                    ErrorCode errorCode = FliggyPlayAbroadErrorCodeEnum.PACKAGE_STOCK_OVER_LIMIT.getErrorCode();
                    checkResult.addErrorCode(errorCode);
                    return checkResult;
                }

                if (packageStock <= 0) {
                    checkResult.addErrorCode(FliggyPlayAbroadErrorCodeEnum.PACKAGE_STOCK_MUST_GREATER_THAN_ZERO.getErrorCode());
                    return checkResult;
                }
            }




        }
        return checkResult;
    }





    @Check(key = "oversea-package-play-method-check", desc = "玩乐发品校验：玩法校验")
    public CheckResult checkPlayMethod(CompExtParam param, ParentCompDO<AbstractCompDO> compDO, @Category StdCategoryDO categoryDO) {

        CheckResult checkResult = new CheckResult();
        List<OverseaPlayPackageInfoControlDO> combos = Optional.ofNullable(compDO.getControlDO())
                .map(controlDO -> (OverseaPlayPackageInfoListControlDO)compDO.getControlDO())
                .map(OverseaPlayPackageInfoListControlDO::getPackageInfos)
                .orElse(null);
        if(CollectionUtils.isEmpty(combos)){
            return checkResult;
        }

        for (OverseaPlayPackageInfoControlDO overseaPlayPackageInfoControlDO : combos) {
            String comboName = overseaPlayPackageInfoControlDO.getPackageName();
            PlayThemeBO playThemeBO = overseaPlayPackageInfoControlDO.getPlayThemeBO();
            if (Objects.isNull(playThemeBO)) {
                checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_MUST_HAVE_PLAY_TEMPLATE.getErrorCode());
            }else{
                List<List<Long>> playThemePropList = playThemeBO.getPlayThemeProps();
                if (CollectionUtils.isNotEmpty(playThemePropList) && playThemePropList.size() > 2) {
                    checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_HAVE_2_PLAY_METHOD.getErrorCode());
                    return checkResult;
                }
                if (CollectionUtils.isNotEmpty(playThemePropList)) {
                    // 选择的玩法均为子玩法
                    Boolean areAllPlayTypesChild = playThemePropList.stream()
                            .filter(Objects::nonNull)
                            .allMatch(this::isChildPlayType);
                    if (BooleanUtils.isFalse(areAllPlayTypesChild)) {
                        checkResult.addErrorCode(FliggyErrorEnum.PACKAGE_MUST_CHOOSE_CHILD_PLAY_TEMPLATE.getErrorCode());
                    }
                }

                //玩法/玩法属性校验
                List<PlayMethodValueModel> playThemeValue = playThemeBO.getPlayThemeValue();
                for (PlayMethodValueModel value : playThemeValue) {
                    PlayType playType = playTypeHelpServiceRepo.getPlayType(Long.valueOf(value.getKey()));
                    if (playType == null) {
                        continue;
                    }
                    for (PlayProperty property : value.getProperties()) {
                        Optional<PlayTypeAttributes> attribute = playType.getAttributes()
                                .stream()
                                .filter(Objects::nonNull)
                                .filter(a -> Objects.equals(a.getId(), property.getId()))
                                .findFirst();

                        if (!attribute.isPresent()) {
                            checkResult.addErrorCode(new ErrorCode("PLAY_METHOD_INVALID", StrUtil.format("套餐 {} 玩法属性 {} 已失效，请重新选择", comboName, property.getName())));
                            continue;
                        }
                        PlayTypeAttributes playTypeAttributes = attribute.get();
                        if (Objects.equals(playTypeAttributes.getType(), 1) || Objects.equals(playTypeAttributes.getType(), 2)) {
                            // 校验玩法属性是否支持其他
                            if (Objects.equals(1, property.getIsOther())) {
                                if (Objects.equals(0, playTypeAttributes.getCanHaveCustomOption())) {
                                    checkResult.addErrorCode(new ErrorCode("PLAY_METHOD_INVALID", StrUtil.format("套餐 {} 玩法属性 {} 已失效（不支持其他），请重新选择", comboName, property.getName())));
                                }
                            } else {
                                if (StringUtils.isBlank(property.getValue())) {
                                    continue;
                                }
                                // 校验玩法属性是否更改
                                List<String> playMethodTypeValues = Optional.ofNullable(playTypeAttributes.getValue())
                                        .map(s -> s.split(","))
                                        .map(Arrays::asList)
                                        .orElse(new ArrayList<>());
                                boolean attributeValid = playMethodTypeValues.stream().anyMatch(v -> Objects.equals(v, property.getValue()));
                                if (!attributeValid) {
                                    checkResult.addErrorCode(new ErrorCode("PLAY_METHOD_INVALID", StrUtil.format("套餐 {} 玩法属性值 {} 已失效，请重新选择", comboName, property.getValue())));
                                }
                            }
                        }

                    }
                }


            }
        }

        return checkResult;
    }



    private Boolean isChildPlayType(List<Long> playTypeIdList) {
        if (CollectionUtils.isEmpty(playTypeIdList)) {
            return false;
        }
        Long lastPlayTypeId = playTypeIdList.get(playTypeIdList.size() - 1);
        Set<Long> mainPlayTypeSet = getMainPlayTypeSet();
        return CollectionUtils.isNotEmpty(mainPlayTypeSet) && !mainPlayTypeSet.contains(lastPlayTypeId);
    }

    private Set<Long> getMainPlayTypeSet() {
        List<PlayType> playTypeList = playTypeHelpServiceRepo.getPlayTypeTree();
        if (CollectionUtils.isEmpty(playTypeList)) {
            return new HashSet<>();
        }
        return playTypeList.stream()
                .filter(Objects::nonNull)
                .filter(playType -> CollectionUtils.isNotEmpty(playType.getChildren()))
                .map(PlayType::getId)
                .collect(java.util.stream.Collectors.toSet());
    }


    private Set<String> initializeNameSet(List<PlayActivityNode> specialActivityTypeTree) {
        Set<String> nameSet = new HashSet<>();
        collectNames(specialActivityTypeTree, nameSet);
        return nameSet;
    }

    public void collectNames(List<PlayActivityNode> nodes, Set<String> nameSet) {
        if (nodes == null) {
            return;
        }
        for (PlayActivityNode node : nodes) {
            nameSet.add(node.getName());
            collectNames(node.getChildren(), nameSet);
        }
    }

    private static Date convertString2Date(String date) {
        try {
            return DateUtils.parseDate(date, DateUtil.YYYY_MM_DD);
        } catch (Exception e) {
            throw new IllegalArgumentException("输入日期错误");
        }
    }
}