package com.taobao.gpf.app.playabroad.component.crowdpeople;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.adapt.ICompParseJsonStrategy;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 境外玩乐业务身份下travelCrowdProp组件的JSON解析策略
 */
@Slf4j
@Component
public class PlayAbroadTravelCrowdPropJsonStrategy implements ICompParseJsonStrategy<BasicCompDO<Long>> {

    @Resource
    private TravelSellConfig travelSellConfig;
    
    @Override
    public String getName() {
        return "playAbroadTravelCrowdPropJsonStrategy";
    }

    @Override
    public void parseJson(BasicCompDO<Long> compDO, JsonNode jsonNode, CompExtParam param) {
        if (jsonNode == null || jsonNode.isNull()) {
            return;
        }
        
        if (jsonNode.isObject()) {
            JsonNode valueNode = jsonNode.get("value");
            if (valueNode != null && !valueNode.isNull()) {
                try {
                    compDO.setValue(Long.parseLong(valueNode.asText()));
                } catch (NumberFormatException e) {
                    log.error("Parse travelCrowdProp value error, value: {}", valueNode.asText(), e);
                }
            }
        } else if (jsonNode.isTextual() && StringUtils.isNotBlank(jsonNode.asText())) {
            try {
                compDO.setValue(Long.parseLong(jsonNode.asText()));
            } catch (NumberFormatException e) {
                log.error("Parse travelCrowdProp value error, value: {}", jsonNode.asText(), e);
            }
        }
    }

    @Override
    public JsonNode renderJson(BasicCompDO<Long> compDO, CompExtParam param) {
        ObjectNode result = JacksonUtil.createObjectNode();
        
        // 设置组件类型、标签等基本信息
        result.put("type", "enum");
        result.put("label", "出游人群");
        
        // 如果有值，设置value字段
        if (compDO.getValue() != null) {
            result.put("value", String.valueOf(compDO.getValue()));
            
            // 查找并设置文本值
            String text = getValueText(compDO.getValue(), FliggyParamUtil.getCategoryId(param));
            if (StringUtils.isNotBlank(text)) {
                result.put("text", text);
            }
        }
        
        // 设置数据源
        ArrayNode dataSource = getDataSource(FliggyParamUtil.getCategoryId(param));
        if (dataSource != null) {
            result.set("dataSource", dataSource);
        }
        
        return result;
    }
    
    /**
     * 获取出游人群选项值的显示文本
     */
    private String getValueText(Long value, long categoryId) {
        try {
            Map<Long, String> valueMap = travelSellConfig.getTravelForestCache().getAllPropsVidToVnameMap()
                .get(categoryId).get(travelSellConfig.getSuitablePeopleId());
            
            if (valueMap != null && !valueMap.isEmpty()) {
                return valueMap.get(value);
            }
        } catch (Exception e) {
            log.error("Get travelCrowdProp value text error, categoryId: {}, value: {}", categoryId, value, e);
        }
        
        return null;
    }
    
    /**
     * 获取出游人群选项数据源
     */
    private ArrayNode getDataSource(long categoryId) {
        ArrayNode dataSource = JacksonUtil.createArrayNode();
        
        try {
            Map<Long, String> valueMap = travelSellConfig.getTravelForestCache().getAllPropsVidToVnameMap()
                .get(categoryId).get(travelSellConfig.getSuitablePeopleId());
            
            if (valueMap != null && !valueMap.isEmpty()) {
                for (Map.Entry<Long, String> entry : valueMap.entrySet()) {
                    ObjectNode option = JacksonUtil.createObjectNode();
                    option.put("value", String.valueOf(entry.getKey()));
                    option.put("text", entry.getValue());
                    dataSource.add(option);
                }
            }
        } catch (Exception e) {
            log.error("Get travelCrowdProp dataSource error, categoryId: {}", categoryId, e);
            return null;
        }
        
        return dataSource;
    }
} 