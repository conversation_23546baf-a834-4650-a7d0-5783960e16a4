package com.taobao.gpf.app.playabroad.component.mainelement;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.singlecheckfield.IntegerSingleCheckFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.constant.schema.SchemaConstant;
import com.taobao.top.schema.field.SingleCheckField;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * 境外玩乐业务身份下mainElements组件的schema策略类
 */
@Slf4j
public class PlayAbroadMainElementsSchemaStrategy extends IntegerSingleCheckFieldSchemaStrategy<BasicCompDO<Integer>> {

    @Override
    public String getName() {
        return "playAbroadMainElementsSchemaStrategy";
    }

    @Override
    protected void customRenderField(SingleCheckField field, BasicCompDO<Integer> compDO, CompExtParam param, 
                                    AdapterCompConfig compConfig, SchemaParseContext context) {
        super.customRenderField(field, compDO, param, compConfig, context);

    }

    @Override
    protected void setCompValue(BasicCompDO<Integer> compDO, SingleCheckField field, CompExtParam param, 
                              AdapterCompConfig compConfig, SchemaParseContext context) {
        if (field.getValue() != null && StringUtils.isNotBlank(field.getValue().getValue()) 
            && NumberUtils.isCreatable(field.getValue().getValue())) {
            Integer intValue = NumberUtils.toInt(field.getValue().getValue());
            compDO.setValue(intValue);
            
            // 将值放入上下文
            ObjectNode fieldValueNode = JacksonUtil.createObjectNode();
            fieldValueNode.put("value", intValue);
            context.getFeature().put(SchemaConstant.COMPVALUE_KEY, fieldValueNode);
        }
    }

    @Override
    protected void renderSchemaFieldValue(SingleCheckField field, BasicCompDO<Integer> compDO, 
                                        CompExtParam param, AdapterCompConfig compConfig, 
                                        SchemaParseContext context) {
        JsonNode compValue = (JsonNode) context.getFeature().get(SchemaConstant.COMPVALUE_KEY);
        if (compValue != null && !compValue.path("value").isMissingNode() 
            && NumberUtils.isCreatable(compValue.path("value").asText())) {
            field.setValue(compValue.path("value").asText());
        } else if (compDO.getValue() != null) {
            field.setValue(compDO.getValue().toString());
        }
    }
} 