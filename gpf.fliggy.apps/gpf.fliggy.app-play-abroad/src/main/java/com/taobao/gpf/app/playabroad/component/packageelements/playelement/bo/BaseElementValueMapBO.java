package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.model.DivisionInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseElementValueMapBO {

    /**
     * 是否关联POI元素
     */
    private String poiRelated;

    /**
     * POI元素ID
     */
    private String poiId;

    /**
     * 元素名称
     */
    private String name;

    /**
     * 元素类型
     */
    private String elementType;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * 说明
     */
    private String desc;

    /**
     * 补充说明
     */
    private String additionalRemarks;

    /**
     * 库存
     */
    private Integer numberStock;

    /**
     * 地区
     */
    private DivisionInfo division;
}
