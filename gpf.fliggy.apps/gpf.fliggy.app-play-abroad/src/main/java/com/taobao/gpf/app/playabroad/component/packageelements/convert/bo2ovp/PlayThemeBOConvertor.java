package com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp;

import com.alibaba.gpf.sdk.util.LogUtil;
import com.fliggy.vic.common.shared.play.PlayTheme;
import com.fliggy.vic.common.shared.play.PlayThemeAttribute;
import com.fliggy.vic.common.shared.play.PlayThemeAttributeValue;
import com.fliggy.vpp.client.dto.response.line.industry.PlayPackageInfoDTO;
import com.google.common.collect.Lists;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.app.playabroad.model.VPublishPlayThemeBO;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodValueModel;
import com.taobao.gpf.domain.component.playmethod.model.PlayProperty;
import com.taobao.gpf.domain.component.playmethod.model.VPublishPlayMethodValueModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 玩法库信息转换
 */
@Component
public class PlayThemeBOConvertor {

    public List<PlayTheme> handleSubmitPlayThemes(PlayThemeBO playThemeBO) {
        if (Objects.isNull(playThemeBO)) {
            return null;
        }
        List<PlayTheme> productLinePlayThemeList = Lists.newArrayList();
        for (int i = 0; i < playThemeBO.getPlayThemeProps().size(); i++) {
            List<Long> playIdList = playThemeBO.getPlayThemeProps().get(i);
            if (CollectionUtils.isEmpty(playIdList)) {
                continue;
            }
            Long leafPlayId = playIdList.get(playIdList.size() - 1);
            PlayMethodValueModel newLinePlayThemeBO = playThemeBO.getPlayThemeValue().stream()
                    .filter(value -> isEquals(Long.parseLong(value.getKey()), leafPlayId))
                    .findFirst().orElse(null);
            if (Objects.isNull(newLinePlayThemeBO)) {
                continue;
            }
            if (!NumberUtils.isCreatable(newLinePlayThemeBO.getKey())) {
                LogUtil.sysErrorLog("PlayThemeConvertor.handleSubmitPlayThemes", "玩法库信息转换失败，玩法id非数字:" + newLinePlayThemeBO.getKey());
            }
            PlayTheme playTheme = new PlayTheme();
            playTheme.setPlayThemeId(Long.parseLong(newLinePlayThemeBO.getKey()));
            playTheme.setPlayThemeName(newLinePlayThemeBO.getTab());
            playTheme.setTreePlayThemeIds(playIdList);
            productLinePlayThemeList.add(playTheme);
            if (CollectionUtils.isEmpty(newLinePlayThemeBO.getProperties())) {
                continue;
            }
            List<PlayThemeAttribute> attributeList = newLinePlayThemeBO.getProperties().stream().map(attributeBO -> {
                PlayThemeAttribute playThemeAttribute = new PlayThemeAttribute();
                playThemeAttribute.setAttributeId(attributeBO.getId());
                playThemeAttribute.setAttributeName(attributeBO.getName());
                playThemeAttribute.setAttributeType(attributeBO.getType());
                String value = attributeBO.getValue();

                Set<PlayThemeAttributeValue> valueList = new HashSet<>();
                if (Objects.nonNull(value)) {
                    for (String subValue : StringUtils.split(value, ",")) {
                        PlayThemeAttributeValue playThemeAttributeValue = new PlayThemeAttributeValue();
                        valueList.add(playThemeAttributeValue);
                        boolean other = Objects.equals(subValue, "other");
                        playThemeAttributeValue.setDoesOtherValue(other);
                        playThemeAttributeValue.setValue(other ? ObjectUtils.firstNonNull(attributeBO.getOtherVal(), StringUtils.EMPTY) : subValue);
                    }
                }
                playThemeAttribute.setAttributeValues(Lists.newArrayList(valueList));
                return playThemeAttribute;
            }).collect(Collectors.toList());
            playTheme.setPlayThemeAttributes(attributeList);
        }
        return productLinePlayThemeList;
    }

    public PlayThemeBO handleRenderPlayTheme(PlayPackageInfoDTO playPackageInfoDTO) {
        if (Objects.isNull(playPackageInfoDTO)) {
            return null;
        }

        return handleRenderPlayTheme(playPackageInfoDTO.getPlayThemes());
    }

    public PlayThemeBO handleRenderPlayTheme(List<PlayTheme> productLinePlayThemes) {
        if (Objects.isNull(productLinePlayThemes)) {
            return null;
        }

        if (CollectionUtils.isEmpty(productLinePlayThemes)) {
            return null;
        }
        PlayThemeBO playThemeBO = new PlayThemeBO();
        List<List<Long>> playThemeProps = Lists.newArrayList();
        List<PlayMethodValueModel> linePlayThemes = Lists.newArrayList();
        for (PlayTheme productLinePlayTheme : productLinePlayThemes) {
            playThemeProps.add(productLinePlayTheme.getTreePlayThemeIds());
            PlayMethodValueModel newLinePlayThemeBO = new PlayMethodValueModel();
            newLinePlayThemeBO.setKey(productLinePlayTheme.getPlayThemeId() == null ? null : productLinePlayTheme.getPlayThemeId().toString());
            newLinePlayThemeBO.setTab(productLinePlayTheme.getPlayThemeName());

            if (Objects.nonNull(productLinePlayTheme.getPlayThemeAttributes())) {
                List<PlayProperty> boList = productLinePlayTheme.getPlayThemeAttributes().stream().map(attribute -> {
                    PlayProperty bo = new PlayProperty();
                    bo.setId(attribute.getAttributeId());
                    bo.setName(attribute.getAttributeName());
                    bo.setType(attribute.getAttributeType());
                    Set<String> values = org.testng.collections.Sets.newHashSet();
                    Set<String> otherValues = org.testng.collections.Sets.newHashSet();
                    //对属性value进行分类
                    for (PlayThemeAttributeValue attributeValue : attribute.getAttributeValues()) {
                        if (attributeValue.getDoesOtherValue()) {
                            values.add("other");
                            otherValues.addAll(Arrays.asList(StringUtils.split(ObjectUtils.firstNonNull(attributeValue.getValue(), StringUtils.EMPTY), ",")));
                        } else {
                            values.addAll(Arrays.asList(StringUtils.split(ObjectUtils.firstNonNull(attributeValue.getValue(), StringUtils.EMPTY), ",")));
                        }
                    }
                    bo.setValue(StringUtils.join(values, ","));
                    if (CollectionUtils.isNotEmpty(otherValues)) {
                        //只要otherValues不为空，则认为当前属性选中了other，则isOther=true
                        bo.setIsOther(1);
                        bo.setOtherVal(StringUtils.join(otherValues, ","));
                    }
                    return bo;
                }).collect(Collectors.toList());
                newLinePlayThemeBO.setProperties(boList);
            }
            linePlayThemes.add(newLinePlayThemeBO);
        }
        playThemeBO.setPlayThemeProps(playThemeProps);
        playThemeBO.setPlayThemeValue(linePlayThemes);
        return playThemeBO;
    }

    /**
     * 兼容vpublish页面渲染的数据格式，真服了
     * @param productLinePlayThemes
     * @return
     */
    public VPublishPlayThemeBO handleRenderVPublishPlayTheme(List<PlayTheme> productLinePlayThemes) {
        if (Objects.isNull(productLinePlayThemes)) {
            return null;
        }

        if (CollectionUtils.isEmpty(productLinePlayThemes)) {
            return null;
        }
        VPublishPlayThemeBO playThemeBO = new VPublishPlayThemeBO();
        List<List<Long>> playThemeProps = Lists.newArrayList();
        List<VPublishPlayMethodValueModel> linePlayThemes = Lists.newArrayList();
        for (PlayTheme productLinePlayTheme : productLinePlayThemes) {
            playThemeProps.add(productLinePlayTheme.getTreePlayThemeIds());
            VPublishPlayMethodValueModel newLinePlayThemeBO = new VPublishPlayMethodValueModel();
            newLinePlayThemeBO.setKey(productLinePlayTheme.getPlayThemeId() == null ? null : productLinePlayTheme.getPlayThemeId().toString());
            newLinePlayThemeBO.setTab(productLinePlayTheme.getPlayThemeName());

            if (Objects.nonNull(productLinePlayTheme.getPlayThemeAttributes())) {
                List<PlayProperty> boList = productLinePlayTheme.getPlayThemeAttributes().stream().map(attribute -> {
                    PlayProperty bo = new PlayProperty();
                    bo.setId(attribute.getAttributeId());
                    bo.setName(attribute.getAttributeName());
                    bo.setType(attribute.getAttributeType());
                    Set<String> values = org.testng.collections.Sets.newHashSet();
                    Set<String> otherValues = org.testng.collections.Sets.newHashSet();
                    //对属性value进行分类
                    for (PlayThemeAttributeValue attributeValue : attribute.getAttributeValues()) {
                        if (attributeValue.getDoesOtherValue()) {
                            values.add("other");
                            otherValues.addAll(Arrays.asList(StringUtils.split(ObjectUtils.firstNonNull(attributeValue.getValue(), StringUtils.EMPTY), ",")));
                        } else {
                            values.addAll(Arrays.asList(StringUtils.split(ObjectUtils.firstNonNull(attributeValue.getValue(), StringUtils.EMPTY), ",")));
                        }
                    }
                    bo.setValue(StringUtils.join(values, ","));
                    if (CollectionUtils.isNotEmpty(otherValues)) {
                        //只要otherValues不为空，则认为当前属性选中了other，则isOther=true
                        bo.setIsOther(1);
                        bo.setOtherVal(StringUtils.join(otherValues, ","));
                    }
                    return bo;
                }).collect(Collectors.toList());
                newLinePlayThemeBO.setAttributes(boList);
            }
            linePlayThemes.add(newLinePlayThemeBO);
        }
        playThemeBO.setPlayThemeProps(playThemeProps);
        playThemeBO.setPlayThemeValue(linePlayThemes);
        return playThemeBO;
    }

    public static boolean isEquals(Number v1, Number v2) {
        if (null != v1 && null != v2) {
            return v1.equals(v2);
        } else {
            return null == v1 && null == v2;
        }
    }
}
