package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/3
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScenicSpotBO {

    /**
     * 门票景点ID
     */
    private Long scenicSpotId;
    /**
     * 门票景点名称
     */
    private String scenicSpotName;
    /**
     * 门票景点POI
     */
    private String poiId;
}
