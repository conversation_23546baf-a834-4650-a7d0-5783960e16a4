package com.taobao.gpf.app.playabroad.component.crowdpeople;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.model.FliggyLineRadioCardPageModel;
import com.taobao.gpf.domain.model.FliggyLineRadioCardValueModel;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class FliggyTravelCrowdCompExtPoint implements IInjectExtension {

    @Resource
    private TravelSellConfig travelSellConfig;
    
    @PrepareForRender(key="add-play-abroad-travel-crowd-datasource",desc = "出游人群datasource")
    public void addDataSource(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO categoryDO){
        FliggyLineRadioCardPageModel pageModel = (FliggyLineRadioCardPageModel)compDO.getPagemodel();
        pageModel.getDataSource().addAll(buildDataSource(categoryDO.getCategoryId()));
    }

    private List<FliggyLineRadioCardValueModel> buildDataSource(long catId) {
        Map<Long, Map<Long, String>> longMapMap = travelSellConfig.getTravelForestCache().getAllPropsVidToVnameMap().get(catId);
        Map<Long, String> longStringMap = longMapMap.get(travelSellConfig.getSuitablePeopleId());
        List<FliggyLineRadioCardValueModel> pageValueModelList = longStringMap.entrySet().stream().map(entry->{
            FliggyLineRadioCardValueModel pageValueModel = new FliggyLineRadioCardValueModel();
            pageValueModel.setText(entry.getValue());
            pageValueModel.setValue(entry.getKey());
            return pageValueModel;
        }).sorted(Comparator.comparing(FliggyLineRadioCardValueModel::getValue).reversed()).collect(Collectors.toList());
        return pageValueModelList;
    }
}
