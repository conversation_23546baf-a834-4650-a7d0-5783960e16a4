package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.line.CateringTypeEnum;
import com.fliggy.vic.common.constant.packageelements.FoodTypeEnum;
import com.fliggy.vpp.client.dto.response.line.element.CateringElement;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.poi.PoiDTO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.CateringBusinessTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.PlayThemeEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import com.taobao.travel.client.domain.dataobject.structured.ProductFeature;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class CateringElementConverter extends AbstractElementConverter implements IElementConverter {

    /**
     * 业务类型
     */
    public static final String BUSINESS_TYPE_ELEMENT_KEY = "BUSINESS_TYPE";

    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.CATERING.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.RESTAURANT.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        CateringElement cateringElement = (CateringElement) pElement;
        Product product = super.fromOvpElement(cateringElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();
        // 餐饮元素
        if (CateringBusinessTypeEnum.CATERING.getType().equals(cateringElement.getCateringBusinessType())) {
            // 类型
            Optional.ofNullable(cateringElement.getCateringType()).map(CateringTypeEnum::getByCode).ifPresent(cateringTypeEnum -> {
                elementFeatures.add(new ElementFeature("ELEMENT_TYPE", cateringTypeEnum.getDesc()));
            });
            Optional.ofNullable(cateringElement.getCateringBusinessType()).map(CateringBusinessTypeEnum::getByCode).ifPresent(cateringBusinessTypeEnum -> {
                elementFeatures.add(new ElementFeature("CATERING_BUSINESS_TYPE", cateringBusinessTypeEnum.getDesc()));
            });
            // 标题
            Optional.ofNullable(pElement.getName()).ifPresent(name -> elementFeatures.add(new ElementFeature("NAME", name)));
            // 说明
            Optional.ofNullable(pElement.getDesc()).ifPresent(desc -> elementFeatures.add(new ElementFeature("DESC", desc)));
            // 商家编码
            Optional.ofNullable(pElement.getOuterCode()).ifPresent(outerCode -> elementFeatures.add(new ElementFeature("OUTER_ID", outerCode)));
        } else {
            // 美食元素
            element.setId((long) ResourceType.FOOD.getType());
            element.setType(ResourceType.FOOD.getType());
            product.setId((long) ResourceType.FOOD.getType());
            // 业务类型，交易需要单独设置
            Optional.ofNullable(cateringElement.getCateringBusinessType()).map(CateringBusinessTypeEnum::getByCode).ifPresent(cateringBusinessTypeEnum -> {
                elementFeatures.add(new ElementFeature("ELEMENT_TYPE", cateringBusinessTypeEnum.getDesc()));
            });
            // 标题
            Optional.ofNullable(cateringElement.getPoi()).map(PoiDTO::getPoiName).ifPresent(name -> elementFeatures.add(new ElementFeature("NAME", name)));
            // 说明, 使用产品维度的
            Optional.ofNullable(cateringElement.getAdditionalRemarks()).ifPresent(desc -> elementFeatures.add(new ElementFeature("DESC", desc)));
        }

        // 设置POI, 餐饮和美食都要设置poi
        elementFeatures.addAll(fromPoiDTO(cateringElement.getPoi()));

        // 美食类型
        if(needBuildProductFeaturesFromPlayThemes(cateringElement)) {
            buildProductFeaturesFromPlayThemes(cateringElement.getPlayThemes(), product.getProductFeatures(), PlayThemeEnum.FOOD_TYPE);
        } else {
            Optional.ofNullable(cateringElement.getFoodType()).map(FoodTypeEnum::getByCode).ifPresent(foodTypeEnum -> {
                product.getProductFeatures().add(new ProductFeature("FOOD_TYPE", foodTypeEnum.getDesc()));
            });
        }
        // 业务类型
        Optional.ofNullable(cateringElement.getCateringBusinessType()).map(CateringBusinessTypeEnum::getByCode).ifPresent(cateringBusinessTypeEnum -> {
            elementFeatures.add(new ElementFeature(BUSINESS_TYPE_ELEMENT_KEY, cateringBusinessTypeEnum.getDesc()));
        });
        return product;
    }
}
