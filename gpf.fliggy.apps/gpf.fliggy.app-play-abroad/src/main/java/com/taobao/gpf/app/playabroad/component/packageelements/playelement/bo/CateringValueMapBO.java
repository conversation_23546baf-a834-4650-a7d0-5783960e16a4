package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CateringValueMapBO extends BaseElementValueMapBO {

    /**
     * 餐饮poi名称
     */
    private String poiName;

    /**
     * html4
     */
    private String html4;

    /**
     * 美食类型
     * {@link com.fliggy.vic.common.constant.line.CateringTypeEnum}
     */
    private Integer cateringType;

    /**
     * 业务类型
     * {@link com.taobao.gpf.app.playabroad.constant.element.CateringBusinessTypeEnum}
     */
    private Integer businessType;
}
