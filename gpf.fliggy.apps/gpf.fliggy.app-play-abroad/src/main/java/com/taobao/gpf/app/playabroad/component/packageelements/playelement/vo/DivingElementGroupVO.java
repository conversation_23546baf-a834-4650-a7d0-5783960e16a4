package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DivingElementGroupVO extends BasePlayElementGroupVO {
    /**
     * 潜水元素
     */
    private List<DivingElementVO> divingElementList;

    /**
     * 潜水次数
     */
    private Integer numberStock;

    /**
     * 数量规格类型
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum
     */
    private Integer type;

}
