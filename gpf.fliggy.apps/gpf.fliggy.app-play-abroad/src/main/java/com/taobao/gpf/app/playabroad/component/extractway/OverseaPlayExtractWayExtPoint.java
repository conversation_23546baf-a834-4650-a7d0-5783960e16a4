package com.taobao.gpf.app.playabroad.component.extractway;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.component.etc.EtcControlDO;
import com.alibaba.gpf.business.domain.component.extractWay.DeliverWayEnum;
import com.alibaba.gpf.business.domain.component.logistics.FreightPayerEnum;
import com.alibaba.gpf.business.domain.component.logistics.LogisticsControlDO;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.component.extractway.logistics.LogisticsValueDO;
import com.taobao.gpf.domain.component.extractway.pickup.PickUpMyselfControlDO;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.repository.etc.EtcServiceRepo;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.macenter.domain.dto.EtcFormDTO;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/5/27 20:22
 **/
public class OverseaPlayExtractWayExtPoint implements IInjectExtension {

    @Resource
    private EtcServiceRepo etcServiceRepo;

    @Check(key = "oversea-play-etc-check")
    public CheckResult checkEtc(BasicCompDO<String> compDO, @Category StdCategoryDO stdCategory, CompExtParam param) {
        CheckResult result = new CheckResult();
        if (CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID != FliggyParamUtil.getCategoryId(param)) {
            return result;
        }

        if (compDO.getControlDO() == null || ((EtcControlDO)compDO.getControlDO()).getWay() != DeliverWayEnum.ETC) {
            return result;
        }

        EtcFormDTO etcDTO = ((EtcControlDO) compDO.getControlDO()).getEtcDTO();
        if (etcDTO == null) {
            result.addErrorCode(FliggyErrorEnum.ELECTRONIC_TRADE_CERTIFICATE_INFO_NOT_NULL.getErrorCode("extractWay"));
            return result;
        }

        if (etcDTO.getExpiryTime() == null) {
            result.addErrorCode(FliggyErrorEnum.ELECTRONIC_TRADE_CERTIFICATE_INFO_NOT_NULL.getErrorCode("extractWay"));
            return result;
        }


        if (!etcDTO.isSelectWriteoff() && !etcDTO.isSelectRefund() && !etcDTO.isSelectFullRefund()) {
            result.addErrorCode(FliggyErrorEnum.ELECTRONIC_TRADE_CERTIFICATE_INFO_NOT_VALID.getErrorCode("extractWay"));
            return result;
        }

        //过期退
        boolean selectRefund = etcDTO.isSelectRefund();
        int refundValue = etcDTO.getRefundValue();
        if (selectRefund && refundValue < 1) {
            result.addErrorCode(FliggyErrorEnum.CHK_TAOBAO_ETC_REFUND_OVERSEA_PLAY.getErrorCode("extractWay"));
            return result;
        }
        //售中退
        if (etcDTO.isSelectFullRefund() && refundValue != 100) {
            result.addErrorCode(FliggyErrorEnum.CHK_TAOBAO_ETC_FULL_REFUND_OVERSEA_PLAY.getErrorCode("extractWay"));
            return result;
        }

        if (etcDTO.getSelectPackageId() == null) {
            result.addErrorCode(FliggyErrorEnum.ELECTRONIC_TRADE_CERTIFICATE_INFO_NOT_VALID_STORE.getErrorCode("extractWay"));
            return result;
        }else{
            Long etcSendId = etcServiceRepo.queryEtcSendId(etcDTO.getSelectPackageId());
            // 发码码商需要为淘宝码商, 码商id 0
            if (null == etcSendId || !etcSendId.equals(0L)) {
                result.addErrorCode(FliggyErrorEnum.CHK_TAOBAO_MNICK_OVERSEA_PLAY.getErrorCode("extractWay"));
                return result;
            }
        }

        return result;
    }


    /**
     * 自取规则校验
     * @param compDO
     * @param stdCategory
     * @param param
     * @return
     */
    @Check(key = "oversea-play-pickup-check")
    public CheckResult check(BasicCompDO<String> compDO, @Category StdCategoryDO stdCategory, CompExtParam param) {
        CheckResult result = new CheckResult();

        if (CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID != FliggyParamUtil.getCategoryId(param)) {
            return result;
        }

        if (compDO.getControlDO() == null || ((PickUpMyselfControlDO)compDO.getControlDO()).getWay() != DeliverWayEnum.PICK_UP_MYSELF) {
            return result;
        }

        PickUpMyselfControlDO pickUpMyselfControlDO = (PickUpMyselfControlDO) compDO.getControlDO();
        if (pickUpMyselfControlDO == null || StringUtils.isBlank(compDO.getValue())) {
            result.addErrorCode(FliggyErrorEnum.PICKUP_MYSELF_ADRESS_NOT_NULL.getErrorCode("extractWay"));
            return result;
        }

        return result;
    }


    @Check(key = "oversea-play-logistics-check")
    public CheckResult checkDeposit(BasicCompDO<LogisticsValueDO> compDO, @Category StdCategoryDO stdCategory, CompExtParam param) {
        CheckResult result = new CheckResult();
        if (CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID != FliggyParamUtil.getCategoryId(param)) {
            return result;
        }

        if (compDO.getControlDO() == null || ((LogisticsControlDO)compDO.getControlDO()).getWay() != DeliverWayEnum.LOGISTICS) {
            return result;
        }

        LogisticsValueDO logisticsValueDO = compDO.getValue();
        if (logisticsValueDO == null) {
            result.addErrorCode(FliggyErrorEnum.LOGISTICS_INFO_NOT_NULL.getErrorCode("extractWay"));
            return result;
        }

        if (FreightPayerEnum.BUYER.getValue().equals(logisticsValueDO.getPayer())) {
            if (logisticsValueDO.getPostageId() == null) {
                result.addErrorCode(FliggyErrorEnum.POSTAGEID_INFO_NOT_NULL.getErrorCode("extractWay"));
                return result;
            }
        }
        return result;
    }
}
