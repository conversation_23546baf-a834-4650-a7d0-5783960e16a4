package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.packageelements.VacationPackageElementCategoryEnum;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WifiElementConverter extends AbstractElementConverter implements IElementConverter{
    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.WIFI.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.WIFI.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        Element element = product.getElement();
        PackageElement wifiElement = new PackageElement();
        wifiElement.setElementCategoryEnum(VacationPackageElementCategoryEnum.WIFI.getCode());
        wifiElement.setName(element.getName());
        wifiElement.setAdditionalRemarks(element.getDescr());
        wifiElement.setDesc(element.getDescr());
        wifiElement.setOuterCode(ElementConverterUtils.convert2OuterCode(element));
        return wifiElement;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        Product product = super.fromOvpElement(pElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        //NAME
        elementFeatures.add(new ElementFeature("NAME", pElement.getName()));
        //城市
        elementFeatures.add(new ElementFeature("DESC", pElement.getDesc()));
        //商家编码
        elementFeatures.add(new ElementFeature("OUTER_ID", pElement.getOuterCode()));
        // POI_RELATED
        elementFeatures.add(new ElementFeature("POI_RELATED", "否"));

        elementFeatures.add(new ElementFeature(KEY_DETAIL_DESC, pElement.getAdditionalRemarks()));

        return product;
    }
}
