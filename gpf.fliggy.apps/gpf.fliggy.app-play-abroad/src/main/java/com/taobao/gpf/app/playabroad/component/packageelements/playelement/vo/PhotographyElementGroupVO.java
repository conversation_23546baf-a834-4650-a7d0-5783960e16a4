package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PhotographyElementGroupVO extends BasePlayElementGroupVO {
    /**
     * 拍摄地点
     */
    private String address;

    /**
     * 拍摄天数
     */
    private Integer days;

    /**
     * 服装数量
     */
    private Integer clothingNumber;

    /**
     * 外部id
     */
    private String outerId;

    /**
     * 拍摄风格
     */
    private String photographyStyle;
}
