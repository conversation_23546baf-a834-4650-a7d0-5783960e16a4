package com.taobao.gpf.app.playabroad.component.crowdpeople;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.travel.client.domain.dataobject.TravelItemPropValueDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Component
public class PlayAbroadCrowdPropStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<Long>> {

    @Resource
    private TravelSellConfig travelSellConfig;
    
    @Override
    public void parseStore(BasicCompDO<Long> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        TravelItemPropValueDO valueDO = storeDO.findExtInfoPropValueDO(travelSellConfig.getSuitablePeopleId());
        if(valueDO != null){
            compDO.setValue(valueDO.getVid());
        }
    }

    @Override
    public void transferStore(BasicCompDO<Long> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        TravelItemPropValueDO valueDO = storeDO.findExtInfoPropValueDO(travelSellConfig.getSuitablePeopleId());
        if(compDO.getValue() == null) {
            if (valueDO != null) {
                removeExtInfoPropertyValue(storeDO, travelSellConfig.getSuitablePeopleId());
            }
            return;
        }
        TravelItemPropValueDO currentValueDO = createCurrentValueDO(FliggyParamUtil.getCategoryId(param), compDO.getValue());
        if(valueDO == null){
            storeDO.appendExtInfoPropertyValue(currentValueDO);
        }else if(valueDO.getVid() != currentValueDO.getVid()){
            valueDO.setVid(currentValueDO.getVid());
            valueDO.setName(currentValueDO.getName());
        }
    }

    private TravelItemPropValueDO createCurrentValueDO(long categoryId, Long value){
        TravelItemPropValueDO valueDO = new TravelItemPropValueDO();
        valueDO.setCid(categoryId);
        valueDO.setPid(travelSellConfig.getSuitablePeopleId());
        valueDO.setVid(value);
        valueDO.setPropName(travelSellConfig.getTravelForestCache().getPropName(categoryId, travelSellConfig.getSuitablePeopleId()));
        valueDO.setName(travelSellConfig.getTravelForestCache().getValueName(categoryId, travelSellConfig.getSuitablePeopleId(), value));
        return valueDO;
    }

    private void removeExtInfoPropertyValue(TravelItemStoreDO storeDO, long pid){
        List<TravelItemPropValueDO> valueDOList = storeDO.getTravelItemExtendInfo().getPropertyValues();
        for(Iterator<TravelItemPropValueDO> iterator = valueDOList.iterator(); iterator.hasNext();){
            TravelItemPropValueDO valueDO = iterator.next();
            if(valueDO.getPid() == pid){
                iterator.remove();
            }
        }
    }

    @Override
    public String getName() {
        return "playAbroadCrowdPropStoreStrategy";
    }
}
