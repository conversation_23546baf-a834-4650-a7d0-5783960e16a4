package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.travel.client.domain.dataobject.structured.Product;

public interface IElementConverter {

    /**
     * 获取vpp元素类型
     * @return vpp元素类型
     */
    Integer getElementCategory();
    /**
     * 转换至ovp元素
     */
    PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product);

    /**
     * 转换至travelItem元素
     */
    Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO);
}
