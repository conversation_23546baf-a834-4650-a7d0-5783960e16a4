package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhotographyValueMapBO extends BaseElementValueMapBO {

    /**
     * 拍摄地点
     */
    private String address;

    /**
     * 拍摄天数
     */
    private Integer days;

    /**
     * 服装数量
     */
    private Integer clothingNumber;

    /**
     * 拍摄风格
     */
    private String photographyStyle;
}
