package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpecialActivityElementGroupBO extends BasePlayElementGroupBO{

    /**
     * 特色活动元素列表
     */
    private List<ElementBO<SpecialActivityValueMapBO>> specialActivityElementList;
}
