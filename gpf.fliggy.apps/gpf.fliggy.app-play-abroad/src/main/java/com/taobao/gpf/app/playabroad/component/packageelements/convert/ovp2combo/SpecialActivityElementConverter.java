package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.packageelements.SpecialActivityTypeEnum;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.element.SpecialActivityElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class SpecialActivityElementConverter extends AbstractElementConverter implements IElementConverter{

    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.SPECIAL_ACTIVITY.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.SPECIAL_ACTIVITY.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        SpecialActivityElement specialActivityElement = (SpecialActivityElement) pElement;
        Product product = super.fromOvpElement(specialActivityElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        elementFeatures.add(new ElementFeature("ELEMENT_TYPE", Optional.ofNullable(SpecialActivityTypeEnum.getByCode(specialActivityElement.getActivityType()))
                .map(SpecialActivityTypeEnum::getDesc).orElse(null)));
        elementFeatures.add(new ElementFeature("NAME", specialActivityElement.getName()));
        elementFeatures.add(new ElementFeature("DESC", specialActivityElement.getDesc()));
        elementFeatures.add(new ElementFeature("OUTER_ID", specialActivityElement.getOuterCode()));
        elementFeatures.addAll(fromPoiDTO(specialActivityElement.getPoi()));
        elementFeatures.add(new ElementFeature(KEY_DETAIL_DESC, specialActivityElement.getAdditionalRemarks()));
        combo.getComboFeatures().put("specialActivityService", "");
        return product;
    }
}
