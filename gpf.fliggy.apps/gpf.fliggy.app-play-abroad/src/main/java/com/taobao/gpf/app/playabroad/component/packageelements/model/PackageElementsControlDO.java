package com.taobao.gpf.app.playabroad.component.packageelements.model;

import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.BasePlayElementGroupBO;
import lombok.Data;

import java.util.List;

/**
 * 套餐搭配元素控制数据对象
 */
@Data
public class PackageElementsControlDO extends ControlDO {
    
    /**
     * 元素信息
     */
    private List<BasePlayElementGroupBO> elementInfoList;

} 