package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.schema;

import com.alibaba.gpf.base.top.sdk.extpoint.ICompParseSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.ExtensionRouterFatory;
import com.google.common.collect.Lists;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.domain.constant.schema.SchemaFieldIdEnum;
import com.taobao.gpf.domain.publish.schemastrategy.FliggyParentMultiComplexFieldSchemaStrategy;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.SchemaExtensionUtil;
import com.taobao.top.schema.field.Field;
import com.taobao.top.schema.field.MultiComplexField;
import com.taobao.top.schema.value.ComplexValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 境外玩乐套餐组件schema策略实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class OverseaPlayPackageInfoFieldSchemaStrategy<ChildCompDO extends AbstractCompDO> extends FliggyParentMultiComplexFieldSchemaStrategy<ChildCompDO> {

    @Override
    public String getName() {
        return "overseaPlayPackageInfoFieldSchemaStrategy";
    }

    @Override
    protected void setCompValue(ParentCompDO<ChildCompDO> compDO, MultiComplexField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        if (CollectionUtils.isEmpty(field.getComplexValues())) {
            return;
        }

        //直接填充父组件的值，不按每个子组件(因为业务需要把子组件汇总到父组件)填充
        if (SchemaExtensionUtil.needSetControlDOValue(compDO, compConfig)) {
            SchemaExtensionUtil.setControlDOValue(compDO, field, param, compConfig, context);

            //这里做一些必填组件的设置，防止required校验
            field.getComplexValues().forEach(complexValue -> {
                compDO.getChildren().stream()
                        .filter(e -> StringUtils.equals(e.getCompName(), "packageName"))
                        .findFirst()
                        .ifPresent(e -> {
                            e.setValue(complexValue.getValue(SchemaFieldIdEnum.PACKAGE_NAME.getId()).getValue());
                        });

                compDO.getChildren().stream()
                        .filter(e -> StringUtils.equals(e.getCompName(), "playMethod"))
                        .findFirst()
                        .ifPresent(e -> {
                            e.setValue(new Object());
                        });
            });
            return;
        }

        OverseaPlayPackageInfoListControlDO listControlDO = new OverseaPlayPackageInfoListControlDO();
        List<OverseaPlayPackageInfoControlDO> packageInfos = Lists.newArrayList();

        field.getComplexValues().forEach(complexValue -> {
            OverseaPlayPackageInfoControlDO packageInfoControlDO = new OverseaPlayPackageInfoControlDO();

            for (AbstractCompDO childCompDO : compDO.getChildren()) {
                AdapterCompConfig childConfig = compConfig.getChildren().stream()
                        .filter(e -> StringUtils.equals(childCompDO.getCompName(), e.getCompname()))
                        .findFirst().orElse(null);

                if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                    continue;
                }

                ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(ICompParseSchemaStrategy.class, childConfig.getStrategy());

                if (schemaStrategy == null) {
                    continue;
                }

                Field childField = complexValue.getValueField(getFieldId(childConfig, compDO));

                if (null == childField) {
                    continue;
                }

                SchemaParseContext cloneContext = context.clone();
                schemaStrategy.parseSchema(childCompDO, childField, param, childConfig, cloneContext);
                setChildCompValue(packageInfoControlDO, cloneContext, childCompDO);
            }

            packageInfos.add(packageInfoControlDO);
        });

        listControlDO.setPackageInfos(packageInfos);
        compDO.setControlDO(listControlDO);
    }

    @Override
    protected void customRenderField(MultiComplexField field, ParentCompDO<ChildCompDO> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        super.customRenderField(field, compDO, param, compConfig, context);
    }

    @Override
    protected void renderSchemaFieldValue(MultiComplexField field, ParentCompDO<ChildCompDO> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        if (null == compDO.getControlDO()) {
            return;
        }

        //直接渲染父组件的值，不按每个子组件(因为业务需要把子组件汇总到父组件)渲染
        if (SchemaExtensionUtil.needRenderValue(compDO, compConfig)) {
            SchemaExtensionUtil.renderFieldValue(compDO, field, param, compConfig, context);
            return;
        }

        OverseaPlayPackageInfoListControlDO listControlDO = (OverseaPlayPackageInfoListControlDO) compDO.getControlDO();

        if (CollectionUtils.isEmpty(listControlDO.getPackageInfos())) {
            return;
        }

        List<ComplexValue> packageValues = Lists.newArrayList();

        for (OverseaPlayPackageInfoControlDO packageInfoControlDO : listControlDO.getPackageInfos()) {
            ComplexValue packageComplexValue = new ComplexValue();

            for (ChildCompDO childCompDO : compDO.getChildren()) {
                AdapterCompConfig childConfig = compConfig.getChildren().stream()
                        .filter(e -> StringUtils.equals(childCompDO.getCompName(), e.getCompname()))
                        .findFirst().orElse(null);

                if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                    continue;
                }

                ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(ICompParseSchemaStrategy.class, childConfig.getStrategy());

                if (schemaStrategy == null) {
                    continue;
                }

                SchemaParseContext cloneConfig = context.clone();
                cloneConfig.setOnlyShowValue();
                renderChildCompValue(cloneConfig, childCompDO, packageInfoControlDO);

                Field childField = schemaStrategy.transferSchema(childCompDO, param, childConfig, cloneConfig);
                packageComplexValue.put(childField);
            }

            packageValues.add(packageComplexValue);
        }

        field.setComplexValues(packageValues);
    }

    /**
     * 根据子组件不同，将值从SchemaParseContext或childCompDO中设置到packageInfoControlDO中
     *
     * @param packageInfoControlDO 套餐控制对象
     * @param cloneContext         上下文
     * @param childCompDO          子组件
     */
    private void setChildCompValue(OverseaPlayPackageInfoControlDO packageInfoControlDO, SchemaParseContext cloneContext, AbstractCompDO childCompDO) {
        if (StringUtils.equals("packageName", childCompDO.getCompName())) {
            packageInfoControlDO.setPackageName((String) childCompDO.getValue());
        } else if (StringUtils.equals("outId", childCompDO.getCompName())) {
            packageInfoControlDO.setOutId((String) childCompDO.getValue());
        } else if (StringUtils.equals("packageDesc", childCompDO.getCompName())) {
            packageInfoControlDO.setPackageDesc((String) childCompDO.getValue());
        }
    }

    /**
     * 根据子组件不同，将值从packageInfoControlDO设置到childCompDO或cloneConfig中
     *
     * @param cloneConfig          上下文
     * @param childCompDO          子组件
     * @param packageInfoControlDO 套餐控制对象
     */
    private void renderChildCompValue(SchemaParseContext cloneConfig, ChildCompDO childCompDO, OverseaPlayPackageInfoControlDO packageInfoControlDO) {
        if (StringUtils.equals("packageName", childCompDO.getCompName())) {
            childCompDO.setValue(packageInfoControlDO.getPackageName());
        } else if (StringUtils.equals("outId", childCompDO.getCompName())) {
            childCompDO.setValue(packageInfoControlDO.getOutId());
        } else if (StringUtils.equals("packageDesc", childCompDO.getCompName())) {
            childCompDO.setValue(packageInfoControlDO.getPackageDesc());
        }
    }
} 