package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.shared.Specification;
import com.fliggy.vic.common.util.StreamUtils;
import com.fliggy.vpp.client.dto.response.line.element.LocalTouristGuideElement;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.LanguageEnum;
import com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class LocalTouristGuideElementConverter extends AbstractElementConverter implements IElementConverter {

    /**
     * 地陪语种
     */
    public static final String LANGUAGES_ELEMENT_KEY = "LANGUAGES";

    /**
     * 讲师 ID
     */
    public static final String EXPERT_ID = "EXPERT_ID";

    /**
     * 是否含票
     */
    public static final String DOES_INCLUDE_SCENIC_SPOT_TICKET = "DOES_INCLUDE_SCENIC_SPOT_TICKET";


    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.TOUR.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.LOCAL_GUIDE.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        Product product = super.fromOvpElement(pElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();
        // change to language
        LocalTouristGuideElement localTouristGuideElement = (LocalTouristGuideElement) pElement;
        List<Integer> languageList = localTouristGuideElement.getLanguages();
        // 兜底
        if (CollectionUtils.isEmpty(languageList) && Objects.nonNull(localTouristGuideElement.getLanguage())) {
            languageList = new ArrayList<>();
            languageList.add(localTouristGuideElement.getLanguage());
        }
        String languages = StreamUtils.asStream(languageList)
                .filter(Objects::nonNull)
                .map(LanguageEnum::getByType)
                .filter(Objects::nonNull)
                .map(LanguageEnum::getDesc)
                .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(languages)) {
            elementFeatures.add(new ElementFeature(LANGUAGES_ELEMENT_KEY, languages));
            elementFeatures.add(new ElementFeature("NAME", languages));
        }
        // 是否含票
        Optional.ofNullable(localTouristGuideElement.getHasTicket())
                .ifPresent(t -> elementFeatures.add(new ElementFeature(DOES_INCLUDE_SCENIC_SPOT_TICKET, String.valueOf(t))));
        // 关联讲师 id
        Optional.ofNullable(localTouristGuideElement.getRelationLecturerId())
                .ifPresent(t -> elementFeatures.add(new ElementFeature(EXPERT_ID, t)));
        //outer_id
        Optional.ofNullable(localTouristGuideElement.getOuterAttributes())
                .map(outerAttributes -> outerAttributes.get("outerId"))
                .filter(StringUtils::isNotBlank)
                .ifPresent(outerCode -> {
                    elementFeatures.add(new ElementFeature("OUTER_ID", outerCode));
                    element.setOuterId(outerCode);
                });
        // 数量规格
        Optional.ofNullable(localTouristGuideElement.getSpecification())
                .map(Specification::getDescription)
                .filter(StringUtils::isNumeric)
                .map(Integer::parseInt)
                .map(QuantitySpecificationEnum::getByType)
                .map(QuantitySpecificationEnum::getLabel)
                .ifPresent(desc -> elementFeatures.add(new ElementFeature("SPECIFICATION", desc)));

        // 业务类型
        Optional.ofNullable(localTouristGuideElement.getBizType())
                .ifPresent(type -> elementFeatures.add(new ElementFeature("ELEMENT_TYPE", type)));

        setCustomProductIdAndElementId(product);

        return product;
    }
}
