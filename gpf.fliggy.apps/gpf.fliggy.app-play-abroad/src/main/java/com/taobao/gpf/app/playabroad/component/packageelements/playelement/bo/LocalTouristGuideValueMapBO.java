package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.constant.element.LocalTouristGuideBizTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocalTouristGuideValueMapBO extends BaseElementValueMapBO {

    /**
     * 语言
     * <p>
     * @see com.taobao.gpf.app.playabroad.constant.element.LanguageEnum
     */
    private List<String> language;

    /**
     * 数量规格类型
     *
     * @see QuantitySpecificationEnum
     */
    private String type;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * 讲解类型
     * @see LocalTouristGuideBizTypeEnum
     */
    private String bizType;

    /**
     * 是否含
     */
    private Boolean hasTicket;

    /**
     * 讲师ID
     */
    private String relationLecturerId;

    /**
     * 讲师昵称
     */
    private String lecturerNickName;

    /**
     * 讲师名字
     */
    private String lecturerRealName;
}
