package com.taobao.gpf.app.playabroad.component.servicepromise.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.multicheckfield.MultiCheckFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.taobao.gpf.domain.model.pagemodel.FliggyTagGroupValueModel;
import com.taobao.top.schema.field.MultiCheckField;
import com.taobao.top.schema.value.Value;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 境外玩乐服务承诺组件schema策略实现类
 *
 * <AUTHOR>
 */
public class PlayAbroadServicePromiseSchemaStrategy extends MultiCheckFieldSchemaStrategy<BasicCompDO<List<FliggyTagGroupValueModel>>> {

    @Override
    public String getName() {
        return "playAbroadServicePromiseSchemaStrategy";
    }

    @Override
    protected void setCompValue(BasicCompDO<List<FliggyTagGroupValueModel>> compDO, MultiCheckField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        if (CollectionUtils.isEmpty(field.getValues())) {
            return;
        }
        // 将schema中的多选值转换成组件的值列表
        List<FliggyTagGroupValueModel> valueModelList = new ArrayList<>();
        for (Value value : field.getValues()) {
            if (value != null && value.getValue() != null) {
                try {
                    int intValue = Integer.parseInt(value.getValue());
                    FliggyTagGroupValueModel valueModel = new FliggyTagGroupValueModel(intValue, null);
                    valueModelList.add(valueModel);
                } catch (NumberFormatException e) {
                    // 忽略无法解析为整数的值
                    LogUtil.sysErrorLog("playAbroadServicePromiseSchemaStrategy.setCompValue", e, e.getMessage());
                    throw new RuntimeException(e);
                }
            }
        }
        compDO.setValue(valueModelList);
    }

    @Override
    protected void renderSchemaFieldValue(@NonNull MultiCheckField field, BasicCompDO<List<FliggyTagGroupValueModel>> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        if (CollectionUtils.isEmpty(compDO.getValue())) {
            return;
        }
        // 将组件的值列表转换成schema中的多选值
        List<Value> valueList = compDO.getValue().stream()
                .filter(model -> model.getValue() != null)
                .map(model -> new Value(model.getValue().toString()))
                .collect(Collectors.toList());
        field.setValues(valueList);
    }

    @Override
    protected void customRenderField(MultiCheckField field, BasicCompDO<List<FliggyTagGroupValueModel>> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext config) {
        super.customRenderField(field, compDO, param, compConfig, config);

    }
} 