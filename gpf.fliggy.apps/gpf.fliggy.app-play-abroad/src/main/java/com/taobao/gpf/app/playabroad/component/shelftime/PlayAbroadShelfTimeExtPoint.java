package com.taobao.gpf.app.playabroad.component.shelftime;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import lombok.extern.slf4j.Slf4j;

/**
 * 境外玩乐上架时间组件扩展点
 */
@Slf4j
public class PlayAbroadShelfTimeExtPoint implements IInjectExtension {

    @PrepareForRender(key = "play-abroad-set-shelfTime-required", desc = "设置上架时间组件必填")
    public void setRequired(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (compDO.getPagemodel() instanceof BasicPageModel) {
            ((BasicPageModel) compDO.getPagemodel()).setRequired(true);
        }
        
    }
} 