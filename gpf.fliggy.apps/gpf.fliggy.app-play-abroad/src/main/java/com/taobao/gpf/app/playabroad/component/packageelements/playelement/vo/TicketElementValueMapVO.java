package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TicketElementValueMapVO extends BaseElementValueMapVO {

    /**
     * 产品id
     */
    @JsonProperty("SCENIC_PRODUCT_ID")
    private String scenicProductId;

    /**
     * 票种id
     */
    @JsonProperty("TICKET_KIND_VID")
    private String ticketKindVid;

    /**
     * 区域名称
     */
    @JsonProperty("AREA_NAME")
    private String areaName;

    /**
     * 区域pv
     */
    @JsonProperty("AREA_PV")
    private String areaPv;

    /**
     * 景点id
     */
    @JsonProperty("SCENIC_ID")
    private String scenicId;

    /**
     * 产品名称
     */
    @JsonProperty("PRODUCT_NAME")
    private String productName;

    /**
     * 场次名称
     */
    @JsonProperty("EPISODE_NAME")
    private String episodeName;

    /**
     * 城市
     */
    @JsonProperty("CITY")
    private String city;

    /**
     * 区域id
     */
    private String divisionId;

    /**
     * 场次pv
     */
    @JsonProperty("EPISODE_PV")
    private String episodePv;

    /**
     * 票种pv
     */
    @JsonProperty("TICKET_KIND_PV")
    private String ticketKindPv;

    /**
     * 库存
     */
    private String numberStock;
}
