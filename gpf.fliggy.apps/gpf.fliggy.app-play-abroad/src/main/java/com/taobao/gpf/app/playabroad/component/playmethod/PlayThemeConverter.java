package com.taobao.gpf.app.playabroad.component.playmethod;

import com.alibaba.fastjson.JSON;
import com.alitrip.travel.travelitems.model.ext.TravelItemExtendField;
import com.fliggy.vic.common.shared.play.PlayTheme;
import com.fliggy.vic.common.shared.play.PlayThemeAttribute;
import com.fliggy.vic.common.shared.play.PlayThemeAttributeValue;
import com.fliggy.vic.common.util.StreamUtils;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp.PlayThemeBOConvertor;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodStoreModel;
import com.taobao.gpf.domain.component.playmethod.model.PlayProperty;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class PlayThemeConverter {

    public static final String PLAY_PROP = "playProp";

    @Autowired
    private PlayThemeBOConvertor playThemeBOConvertor;

    public void fromPlayThemes(OverseaPlayPackageInfoListControlDO controlDO, TravelItemStoreDO travelItem) {
        List<PlayMethodStoreModel> playMethodStoreModelList = StreamUtils.asStream(controlDO.getPackageInfos())
                .filter(Objects::nonNull)
                .map(playPackageInfoControlDO -> playThemeBOConvertor.handleSubmitPlayThemes(playPackageInfoControlDO.getPlayThemeBO()))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(this::fromPlayTheme)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 将全部套餐的玩法添加到商品对扩展字段
        TravelItemExtendField travelItemExtendField = new TravelItemExtendField();
        travelItemExtendField.setName(PLAY_PROP);
        travelItemExtendField.setKey(PLAY_PROP);
        travelItemExtendField.setValue(JSON.toJSONString(playMethodStoreModelList));
        travelItem.getTravelItemExtendInfo().getExtendInfo().put(PLAY_PROP, travelItemExtendField);
    }

    /**
     * 设置玩法
     */
    private PlayMethodStoreModel fromPlayTheme(PlayTheme playTheme) {
        if (Objects.isNull(playTheme)) {
            return null;
        }
        PlayMethodStoreModel playMethodStoreModel = new PlayMethodStoreModel();
        playMethodStoreModel.setId(String.valueOf(playTheme.getPlayThemeId()));
        playMethodStoreModel.setName(playTheme.getPlayThemeName());
        List<PlayProperty> properties = Optional.ofNullable(playTheme.getPlayThemeAttributes()).orElse(new ArrayList<>())
                .stream().filter(Objects::nonNull)
                .map(this::fromPlayThemeAttributes)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        playMethodStoreModel.setProperties(properties);
        return playMethodStoreModel;
    }

    /**
     * 设置玩法属性
     */
    private PlayProperty fromPlayThemeAttributes(PlayThemeAttribute playThemeAttribute) {
        if (Objects.isNull(playThemeAttribute) || playThemeAttribute.getAttributeType() == null) {
            return null;
        }
        PlayProperty playProperty = new PlayProperty();
        playProperty.setId(playThemeAttribute.getAttributeId());
        playProperty.setName(playThemeAttribute.getAttributeName());
        // 自动拆箱会有NPE
        playProperty.setType(playThemeAttribute.getAttributeType());
        List<PlayThemeAttributeValue> valueList = playThemeAttribute.getAttributeValues()
                .stream()
                .filter(Objects::nonNull)
                .filter(attributeValue -> BooleanUtils.isFalse(attributeValue.getDoesOtherValue()))
                .collect(Collectors.toList());
        List<PlayThemeAttributeValue> otherValueList = playThemeAttribute.getAttributeValues()
                .stream()
                .filter(Objects::nonNull)
                .filter(attributeValue -> BooleanUtils.isTrue(attributeValue.getDoesOtherValue()))
                .collect(Collectors.toList());
        playProperty.setIsOther(CollectionUtils.isNotEmpty(otherValueList) ? 1 : 0);

        playProperty.setValue(valueList.stream().map(PlayThemeAttributeValue::getValue).collect(Collectors.joining(",")));
        playProperty.setOtherVal(otherValueList.stream().map(PlayThemeAttributeValue::getValue).collect(Collectors.joining(",")));
        return playProperty;
    }
}
