package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketElementValueMapBO extends BaseElementValueMapBO {

    /**
     * 景点收费项目
     */
    private ScenicSpotChargeItemBO scenicSpotChargeItemBO;

    /**
     * 票种ID
     */
    private Long ticketKindId;

    /**
     * 票种名称
     */
    private String ticketKindName;

    /**
     * 门票售卖策略列表
     */
    private List<TicketSalePolicyBO> ticketSalePolicies;

    /**
     * 是否指定入园
     */
    private Boolean doesAppointEnterParkDate;

    /**
     * 指定入园时，是否需要独立价库
     */
    private Boolean doesInDependentPriceStock;

    /**
     * 指定入园日期
     */
    private List<Integer> admissionDates;
}
