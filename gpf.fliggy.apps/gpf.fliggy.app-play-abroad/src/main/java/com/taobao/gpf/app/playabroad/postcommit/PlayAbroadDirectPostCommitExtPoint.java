package com.taobao.gpf.app.playabroad.postcommit;

import com.alibaba.gpf.sdk.extpoint.commit.IPostCommitPlugin;
import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.taobao.gpf.domain.config.FliggyGraySwitchConfig;
import com.taobao.gpf.domain.utils.GraySwitchUtils;
import com.taobao.gpf.share.directItem.DirectItemDomainService;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/6 16:27
 */
public class PlayAbroadDirectPostCommitExtPoint implements IPostCommitPlugin<TravelItem> {

    @Resource
    DirectItemDomainService directItemDomainService;

    @Override
    public void postCommit(Param param, TravelItem commitDO) {
        if (Objects.isNull(commitDO)) {
            return;
        }

        LogUtil.sysInfoLog("PlayAbroadDirectPostCommitExtPoint.postCommit", "itemId={}", String.valueOf(commitDO.getItemId()));

        if (Objects.nonNull(commitDO.getSellerId()) && !GraySwitchUtils.isOnFor(FliggyGraySwitchConfig.PLAY_ABROAD_SCHEMA_PUBLISH_SELLER, commitDO.getSellerId())) {
            //sellerId不在白名单里，不用去做同步操作
            LogUtil.sysInfoLog("PlayAbroadDirectPostCommitExtPoint.postCommit", ",not need process, itemId={},sellerId = {}",
                    String.valueOf(commitDO.getItemId()),
                    String.valueOf(commitDO.getSellerId()));
            return;
        }

        if (CollectionUtils.isEmpty(commitDO.getSkuList())) {
            return;
        }

        directItemDomainService.saveItemDirectConfig4PlayAbroad(commitDO);
    }

    @Override
    public String getName() {
        return "playAbroadDirectPostCommitExtPoint";
    }
}
