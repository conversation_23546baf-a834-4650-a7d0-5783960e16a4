package com.taobao.gpf.app.playabroad.component.packageelements.strategy;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.adapt.ICompParseJsonStrategy;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;

/**
 * 套餐搭配元素JSON解析策略
 */
public class PackageElementsJsonStrategy implements ICompParseJsonStrategy<BasicCompDO<PackageElementsControlDO>> {

    @Override
    public String getName() {
        return "packageElementInfoJsonStrategy";
    }

    @Override
    public void parseJson(BasicCompDO<PackageElementsControlDO> compDO, JsonNode jsonNode, CompExtParam extParam) {
        // 解析JSON数据，转换为控制对象
        // 这里是具体的解析逻辑，将前端传来的JSON解析为PackageElementsControlDO对象
    }

    @Override
    public JsonNode renderJson(BasicCompDO<PackageElementsControlDO> compDO, CompExtParam extParam) {
        // 将控制对象转换为JSON数据
        if(compDO.getPagemodel() == null) {
            return null;
        }
        ObjectNode objectNode = compDO.getPagemodel().toJsonNode();
        return objectNode;
    }
} 