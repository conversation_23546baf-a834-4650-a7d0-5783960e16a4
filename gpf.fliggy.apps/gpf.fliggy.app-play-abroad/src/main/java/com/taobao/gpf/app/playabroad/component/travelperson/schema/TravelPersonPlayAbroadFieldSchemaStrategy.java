package com.taobao.gpf.app.playabroad.component.travelperson.schema;

import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.FliggyStringSingleCheckFieldSchemaStrategy;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.SchemaExtensionUtil;
import com.taobao.top.schema.field.SingleCheckField;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
public class TravelPersonPlayAbroadFieldSchemaStrategy<T extends AbstractCompDO<String>> extends FliggyStringSingleCheckFieldSchemaStrategy<T> {
    @Override
    public String getName() {
        return "travelPersonPlayAbroadFieldSchemaStrategy";
    }

    @Override
    protected void setCompValue(T compDO, SingleCheckField field, CompExtParam param, AdapterCompConfig compConfig,
                                SchemaParseContext context) {
        //影子组件或本身组件则赋值
        if (SchemaExtensionUtil.notNeedSetControlDOValue(compDO, compConfig)) {
            compDO.setValue(field.getValue().getValue());
            //设置control 值
        } else {
            SchemaExtensionUtil.setControlDOValue(compDO, field, param, compConfig, context);
        }
    }
}
