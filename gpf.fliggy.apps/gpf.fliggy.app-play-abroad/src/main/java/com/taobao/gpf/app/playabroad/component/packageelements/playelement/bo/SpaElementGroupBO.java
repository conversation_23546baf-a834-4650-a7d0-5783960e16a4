package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpaElementGroupBO extends BasePlayElementGroupBO {

    /**
     * spa元素列表
     */
    private List<ElementBO<SpaValueMapBO>> spaElementList;
}
