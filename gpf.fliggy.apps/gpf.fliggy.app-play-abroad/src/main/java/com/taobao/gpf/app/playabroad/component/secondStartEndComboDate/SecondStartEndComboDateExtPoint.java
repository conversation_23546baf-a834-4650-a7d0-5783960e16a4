package com.taobao.gpf.app.playabroad.component.secondStartEndComboDate;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.alibaba.gpf.shared.componet.standarddate.DateRangeValueDO;
import com.alitrip.travel.common.util.QuaDateUtils;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.component.selltype.SellType;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/27 15:56
 **/
@Slf4j
public class SecondStartEndComboDateExtPoint implements IInjectExtension {


    @Check(key = "preBookComboRuleCheck-abroad-play")
    public CheckResult check(CompExtParam param, BasicCompDO<DateRangeValueDO> compDO, @Category StdCategoryDO category) {

        CheckResult result = new CheckResult();

        if(CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID != category.getCategoryId()){
            return result;
        }
        try {

            //可选入住日期不能为空
            DateRangeValueDO dateRangeValueDO = compDO.getValue();
            if (dateRangeValueDO == null || dateRangeValueDO.isEmpty()) {
                result.addErrorCode(FliggyErrorEnum.CHK_START_DATE_OR_END_DATE_NOT_EMPTY.getErrorCode());
            }
            Map<String, AbstractCompDO> depends = param.getUnmodifiableDepends();

            //二次预约才需要校验预约时间
            if (depends.get("sellType") != null && depends.get("sellType").getValue() != null) {
                if (SellType.PreSell.getValue() != Integer.parseInt(depends.get("sellType").getValue().toString())) {
                    return result;
                }
            }

            if (depends.get("reserveStartEndDate") == null || depends.get("reserveStartEndDate").getValue() == null) {
                result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("reserveStartEndDate",
                        "请选择【预约时间段】"));
                return result;
            }

            DateRangeValueDO reserveStartEndDateValueDO = (DateRangeValueDO) depends.get("reserveStartEndDate").getValue();

            if (reserveStartEndDateValueDO.getStartDate() == null || reserveStartEndDateValueDO.getEntDate() == null) {
                result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("reserveStartEndDate",
                        "请选择【预约时间段】"));
                return result;
            }
            Date bookingStartTime = reserveStartEndDateValueDO.getStartDate();
            Date bookingEndTime = reserveStartEndDateValueDO.getEntDate();

            if (null == depends.get("duration") || null == depends.get("duration").getValue()){
                result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("duration",
                        "请填写【提前预订天数】"));
            }

            Integer duration = Integer.valueOf(depends.get("duration").getValue().toString());

            Date startComboDate = dateRangeValueDO.getStartDate();
            if(startComboDate != null){
                // 预约开始时间不能晚于套餐最早时间，忽略小时级
                Calendar bookingStartCalendar = Calendar.getInstance();
                bookingStartCalendar.setTime(bookingStartTime);
                bookingStartCalendar.set(Calendar.HOUR_OF_DAY, 0);
                boolean notValid = startComboDate.before(bookingStartCalendar.getTime());
                if(notValid){
                    result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("secondStartEndComboDate",
                            "可选出发日期的最早时间不能早于预约开始时间"));
                    return result;
                }
                //可选出发日期的起始时间=预约开始时间+商品提前预约天数
                if (FliggySwitchConfig.preBookItemCheckStartTime){
                    Date checkDate = QuaDateUtils.addDate(bookingStartTime, Calendar.DATE, duration);
                    checkDate = QuaDateUtils.clearHMS(checkDate);
                    startComboDate = QuaDateUtils.clearHMS(startComboDate);
                    if (!(QuaDateUtils.equalsByDate(checkDate, startComboDate) || startComboDate.after(checkDate))) {
                        result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("secondStartEndComboDate",
                                "可选出发日期的起始时间需等于或晚于预约开始时间+商品提前报名天数"));
                    }
                }
            }

            Date endComboDate = dateRangeValueDO.getEntDate();
            if(endComboDate != null){
                //可选出发日期的截止时间>预约截止时间+商品提前预约天数
                if(FliggySwitchConfig.preBookItemCheckEndTime){
                    Date checkDate = QuaDateUtils.addDate(bookingEndTime, Calendar.DATE, duration);
                    checkDate = QuaDateUtils.clearHMS(checkDate);
                    endComboDate = QuaDateUtils.clearHMS(endComboDate);
                    if (endComboDate.before(checkDate)) {
                        result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("secondStartEndComboDate",
                                "可选出发日期的截止时间需晚于预约截止时间+商品提前报名天数"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("PreBookComboRuleCheck check error,msg:"+e.getMessage(), e);
        }

        return result;
    }


}
