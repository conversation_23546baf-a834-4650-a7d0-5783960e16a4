package com.taobao.gpf.app.playabroad.component.packageelements.convert.vo2bo;

import com.taobao.forest.domain.dataobject.bizpvrelation.PvPairDO;
import com.taobao.forest.domain.dataobject.bizpvrelation.PvPairUtil;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo.*;
import com.taobao.gpf.app.playabroad.constant.element.BreakFastTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.CellularNetworkTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.PhoneCardTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Mapper(componentModel = "spring", imports = {PhoneCardTypeEnum.class, CellularNetworkTypeEnum.class})
public interface PlayElementVO2BOConvertorHelper {

    @Mapping(target = "ticketElementList", source = "elementValueMapList")
    TicketElementGroupBO toTicketElementGroupBO(TicketElementGroupVO ticketElementGroup);

    @Mapping(target = "valueMap.scenicSpotChargeItemBO.scenicSpotChargeItemId", source = "valueMap.scenicProductId")
    @Mapping(target = "valueMap.scenicSpotChargeItemBO.scenicSpotChargeItemName", source = "valueMap.productName")
    @Mapping(target = "valueMap.scenicSpotChargeItemBO.scenicSpots", source = "valueMap", qualifiedByName = "toScenicSpotsBO")
    @Mapping(target = "valueMap.ticketKindId", source = "valueMap.ticketKindVid")
    @Mapping(target = "valueMap.ticketKindName", source = "valueMap.productName") // ELEMENT_TYPE? 成人票
    @Mapping(target = "valueMap.ticketSalePolicies", source = "valueMap", qualifiedByName = "toTicketSalePolicyList")
    @Mapping(target = "valueMap.division.divisionId", source = "valueMap.divisionId")
    @Mapping(target = "valueMap.division.divisionName", source = "valueMap.city")
    ElementBO<TicketElementValueMapBO> toTicketElementValueMapBO(ElementVO<TicketElementValueMapVO> valueMapVO);

    @Mapping(target = "hotelElementList", source = "elementValueMapList")
    HotelElementGroupBO toHotelElementGroupBO(HotelElementGroupVO hotelElementGroupVO);

    @Mapping(target = "valueMap.hotelBO.hotelId", source = "valueMap.shId")
    @Mapping(target = "valueMap.hotelBO.hotelName", source = "valueMap.name")
    @Mapping(target = "valueMap.hotelBO.poi", source = "valueMap.poiId")
    @Mapping(target = "valueMap.roomBO.id", source = "valueMap.roomTypeId")
    @Mapping(target = "valueMap.roomBO.name", source = "valueMap.elementType")
    @Mapping(target = "valueMap.hotelName", source = "valueMap.name")
    @Mapping(target = "valueMap.roomName", source = "valueMap.elementType")
    @Mapping(target = "valueMap.breakfastCount", source = "valueMap.numberStock", qualifiedByName = "toBreakfastCount")
    @Mapping(target = "valueMap.division.divisionId", source = "valueMap.divisionId")
    @Mapping(target = "valueMap.division.divisionName", source = "valueMap.city")
    ElementBO<HotelElementValueMapBO> toHotelElementBO(ElementVO<HotelElementValueMapVO> hotelElementValueMapVO);

    @Mapping(target = "trafficElementList", source = "elementValueMapList")
    TrafficElementGroupBO toTrafficElementGroupBO(TrafficElementGroupVO elementGroupVO);

    @Mapping(target = "valueMap.additionalRemarks", ignore = true)
    @Mapping(target = "valueMap.numberStock", ignore = true)
    ElementBO<TrafficValueMapBO> toTrafficElementBO(ElementVO<TrafficValueMapVO> valueMapVO);

    @Mapping(target = "wifiElementList", source = "elementValueMapList")
    WifiElementGroupBO toWifiElementGroupBO(WifiElementGroupVO elementGroupVO);

    @Mapping(target = "valueMap.additionalRemarks", ignore = true)
    @Mapping(target = "valueMap.numberStock", ignore = true)
    ElementBO<WifiValueMapBO> toWifiElementBO(ElementVO<WifiValueMapVO> wifiValueMapVO);

    @Mapping(target = "phoneCardElementList", source = "elementValueMapList")
    PhoneCardGroupBO toPhoneCardElementGroupBO(PhoneCardGroupVO elementGroupVO);

    @Mapping(target = "valueMap.additionalRemarks", ignore = true)
    @Mapping(target = "valueMap.numberStock", ignore = true)
    @Mapping(target = "valueMap.cardType", expression = "java(PhoneCardTypeEnum.getByDesc(phoneCardValueMapVO.getSize()).getType())")
    @Mapping(target = "valueMap.cellularNetworkType", expression = "java(CellularNetworkTypeEnum.getByDesc(phoneCardValueMapVO.getSimNet()).getType())")
    ElementBO<PhoneCardValueMapBO> toPhoneCardElementBO(ElementVO<PhoneCardValueMapVO> phoneCardValueMapVOElementVO);

    @Mapping(target = "caterElementList", source = "elementValueMapList")
    CateringElementGroupBO toCateringGroupBO(CateringElementGroupVO elementGroupVO);

    @Mapping(target = "valueMap.additionalRemarks", ignore = true)
    @Mapping(target = "valueMap.cateringType", ignore = true)
    ElementBO<CateringValueMapBO> toCateringElementBO(ElementVO<CateringValueMapVO> cateringValueMapVOElementVO);

    @Mapping(target = "specialActivityElementList", source = "elementValueMapList")
    SpecialActivityElementGroupBO toSpecialActivityElementGroupBO(SpecialActivityElementGroupVO specialActivityElementGroupVO);

    @Mapping(target = "spaElementList", source = "elementValueMapList")
    SpaElementGroupBO toSpaElementGroupBO(SpaElementGroupVO spaElementGroupVO);

    LocalTouristGuideElementGroupBO toLocalTouristGuideElementGroupBO(LocalTouristGuideElementGroupVO localTouristGuideElementGroupVO);

    LocalTouristGuideValueMapBO toLocalTouristGuideValueMapBO(LocalTouristGuideElementGroupVO localTouristGuideElementGroupVO);

    PhotographyElementGroupBO toPhotographyElementGroupBO(PhotographyElementGroupVO elementGroupVO);

    PhotographyValueMapBO toPhotographyValueMapBO(PhotographyElementGroupVO elementGroupVO);

    DivingElementValueMapBO toDivingElementGroupBO(DivingElementGroupVO elementGroupVO);

    BasePlayElementGroupBO toBasePlayElementGroupBO(BasePlayElementGroupVO elementGroupVO);


    @Named("toTicketSalePolicyList")
    static List<TicketSalePolicyBO> toTicketSalePolicyList(TicketElementValueMapVO valueMapVO) {
        return Collections.singletonList(TicketSalePolicyBO.builder()
                .sessionId(PvPairUtil.fromString(valueMapVO.getEpisodePv()).stream().findFirst().map(PvPairDO::getVid).orElse(null))
                .session(valueMapVO.getEpisodeName())
                .region(valueMapVO.getAreaName())
                .regionId(PvPairUtil.fromString(valueMapVO.getAreaPv()).stream().findFirst().map(PvPairDO::getVid).orElse(null))
                .build());
    }

    @Named("toScenicSpotsBO")
    static List<ScenicSpotBO> toScenicSpotsBO(TicketElementValueMapVO valueMapVO) {
        return Collections.singletonList(ScenicSpotBO.builder()
                .scenicSpotName(valueMapVO.getName())
                .scenicSpotId(Objects.isNull(valueMapVO.getScenicId()) ? null : Long.valueOf(valueMapVO.getScenicId()))
                .poiId(valueMapVO.getPoiId())
                .build());
    }


    @Named("toBreakfastCount")
    static Integer toBreakfastCount(String hotelBreakfast) {
        BreakFastTypeEnum breakFastTypeEnum = BreakFastTypeEnum.getByType(Integer.valueOf(hotelBreakfast));
        if (Objects.isNull(breakFastTypeEnum)) {
            return 0;
        }
        return breakFastTypeEnum.getType();
    }
}
