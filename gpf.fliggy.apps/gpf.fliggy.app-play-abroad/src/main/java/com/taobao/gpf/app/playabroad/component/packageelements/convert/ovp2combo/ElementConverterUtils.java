package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@UtilityClass
public class ElementConverterUtils {
    public String convert2OuterCode(Element element) {
        List<ElementFeature> elementFeatures = element.getElementFeatures();
        if (CollectionUtils.isNotEmpty(elementFeatures)) {
            for (ElementFeature elementFeature : elementFeatures) {
                if (elementFeature.getFeatureKey().equals("OUTER_ID")) {
                    return elementFeature.getValue();
                }
            }
        }
        return null;
    }
}
