package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DivingElementValueMapBO extends BaseElementValueMapBO {

    /**
     * 名称
     */
    private String label;

    /**
     * poiId
     */
    private Long value;

    /**
     * 数量规格类型
     * @see QuantitySpecificationEnum
     */
    private String type;
}
