package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model;

import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.model.pagemodel.RuleModel;
import com.alitrip.travel.travelitems.model.structured.Combo;
import lombok.Data;

import java.util.List;

@Data
public class OverseaPlayPackageInfoListControlDO extends ControlDO {

    //数据汇总
    private List<OverseaPlayPackageInfoControlDO> packageInfos;

    //travelitems数据返回，用于组件解析
    private List<Combo> comboDOList;

    private List<RuleModel> customRules;

    private List<OverseaPlayPackageInfoControlDO> defaultValue;

}
