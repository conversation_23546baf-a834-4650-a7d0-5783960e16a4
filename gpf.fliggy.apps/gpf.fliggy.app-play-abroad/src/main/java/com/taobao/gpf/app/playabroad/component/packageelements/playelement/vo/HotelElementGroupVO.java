package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.HotelElementServiceBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HotelElementGroupVO extends BasePlayElementGroupVO {

    /**
     * 酒店住宿元素列表
     */
    private List<ElementVO<HotelElementValueMapVO>> elementValueMapList;

    /**
     * 最早入住时间
     */
    private String earliestCheckInTime;

    /**
     * 最晚退房时间
     */
    private String latestCheckOutTime;

    /**
     * 附加规则
     */
    private String additionalRule;

    /**
     * 可用日期
     */
    private List<Integer> usable;

    /**
     * 是否独立价库
     */
    private Boolean doseInDependentPriceStock;

    /**
     * 更多服务
     */
    private HotelElementServiceBO hotelElementServiceVO;
}
