package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BasePlayElementGroupVO {

    /**
     * 元素名称
     */
    private String elementName;

    /**
     * 元素类型
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum
     */
    private Integer elementCategory;

    /**
     * 元素类别
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.ElementTypeEnum
     */
    private Integer elementType;

    /**
     * 元素信息
     */
    private Object element;

    /**
     * 补充说明
     */
    private String additionalRemarks;

    /**
     * 玩法信息
     */
    private PlayThemeBO playTheme;
}
