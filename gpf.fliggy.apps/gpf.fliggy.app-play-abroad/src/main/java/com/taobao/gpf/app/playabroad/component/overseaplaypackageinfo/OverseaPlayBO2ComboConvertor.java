package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alibaba.gpf.shared.componet.standarddate.DateRangeValueDO;
import com.alitrip.travel.travelitems.constants.PriceType;
import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.constants.SellType;
import com.alitrip.travel.travelitems.model.ItemPVPair;
import com.alitrip.travel.travelitems.model.TravelItemSku;
import com.alitrip.travel.travelitems.model.TravelItemsSkuExt;
import com.alitrip.travel.travelitems.model.person.TravelPerson;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.alitrip.travel.travelitems.model.systemvendordirect.SystemVendorDirectCode;
import com.alitrip.travel.travelitems.model.systemvendordirect.SystemVendorDirectCodes;
import com.fliggy.vic.common.shared.play.PlayTheme;
import com.fliggy.vic.common.util.StreamUtils;
import com.fliggy.vpp.client.dto.response.line.element.ElementGroup;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyDO;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyValueDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp.PackageElementConvert;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp.PlayThemeBOConvertor;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo.CustomElementConverter;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo.IElementConverter;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.component.playmethod.PlayThemeConverter;
import com.taobao.gpf.app.playabroad.config.FliggyPlayAbroadSwitchConfig;
import com.taobao.gpf.app.playabroad.constant.ForestConstants;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.LocalTouristGuideBizTypeEnum;
import com.taobao.gpf.app.playabroad.convert.G35SellerConverter;
import com.taobao.gpf.app.playabroad.convert.PropertyValueConverter;
import com.taobao.gpf.domain.itemdirect.model.PlayAbroadPackageDirectInfo;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;
import com.taobao.gpf.app.playabroad.util.TravelItemsItemFeaturesHelper;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.common.constants.TagsConstants;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.constant.ic.IcTage;
import com.taobao.gpf.domain.constant.item.AlitripFeatureConstants;
import com.taobao.gpf.domain.itemdirect.DirectConstants;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import com.taobao.gpf.domain.publish.store.convertor.Store2ItemConvertor;
import com.taobao.gpf.domain.repository.CommonElementServiceRepo;
import com.taobao.gpf.domain.repository.taskcenter.CommonLlmTaskServiceRepo;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.SystemDirectUtil;
import com.taobao.gpf.domain.utils.common.MoneyUtil;
import com.taobao.travel.client.domain.dataobject.TravelItemPropDO;
import com.taobao.travel.client.domain.dataobject.TravelItemPropValueDO;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.javatuples.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 境外玩乐套餐转换器
 */
@Component
public class OverseaPlayBO2ComboConvertor implements PlayBO2ComboConvertor {

    @Autowired
    private PackageElementConvert packageElementConvert;

    @Autowired
    private List<IElementConverter> elementConverterList;

    @Autowired
    private CustomElementConverter customElementConverter;

    @Autowired
    private PlayThemeBOConvertor playThemeBOConvertor;

    @Autowired
    private PlayThemeConverter playThemeConverter;

    @Autowired
    private G35SellerConverter g35SellerConverter;

    @Autowired
    private PropertyValueConverter propertyValueConverter;

    @Resource
    private TravelSellConfig travelSellConfig;

    @Autowired
    private CommonLlmTaskServiceRepo llmTaskServiceRepo;

    @Autowired
    private CommonElementServiceRepo commonElementServiceRepo;

    @Override
    public void convertControlDO2StoreDO(OverseaPlayPackageInfoListControlDO controlDO, TravelItemStoreDO storeDO, CompExtParam param) {
        List<Combo> combos = new ArrayList<>();
        Map<Integer, Product> needAddProductCpvMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(controlDO.getPackageInfos())) {
            for (int i = 0; i < controlDO.getPackageInfos().size(); i++) {
                OverseaPlayPackageInfoControlDO packageInfo = controlDO.getPackageInfos().get(i);
                //创建基础套餐
                Combo combo = convertPackageInfoToCombo(packageInfo, i, needAddProductCpvMap, param);
                BasicCompDO<Integer> sellTypeCompDO = param.getDependCompDO(CompConstants.SELL_TYPE_COMP_NAME);
                switch (sellTypeCompDO.getValue()) {
                    case 1:
                        convertCalendarComboToStoreDO(packageInfo, i, combo, storeDO, param);
                        break;
                    case 2:
                        convertPreSellComboToStoreDO(packageInfo, i, combo, storeDO, param);
                        break;
                    case 4:
                        convertUncalendarComboToStoreDO(packageInfo, i, combo, storeDO, param);
                        break;
                    default:
                        break;
                }
                buildElement(packageInfo, needAddProductCpvMap, param, combo);
                combos.add(combo);
            }
        }

        storeDO.setCombos(combos);

        //拓展元素生成cpv
        setTravelItemPropertyValuesFromCustomElement(new ArrayList<>(needAddProductCpvMap.values()), storeDO);

        // 设置自由行元素pv对
        buildSelfGuidedTourPvPairs(controlDO, storeDO);
        // 根据套餐间夜数是否相同，处理商品间夜标标
        processPackageNight(storeDO);
        // 设置玩法
        playThemeConverter.fromPlayThemes(controlDO, storeDO);
        // 设置G35卖家相关标
        g35SellerConverter.processG35Seller(storeDO);
        // 把sku cpv聚合到商品
        propertyValueConverter.processSkuCpv(storeDO);
        // 设置线路真人讲解商品标签
        buildRealPersonExplainItemTag(controlDO, storeDO);
        //打标
        processIcTag(controlDO, storeDO, param);
        //处理行程天数
        processTripDays(combos, storeDO, param);
    }


    private void processTripDays(List<Combo> combos, TravelItemStoreDO storeDO, CompExtParam param) {
        // 默认是1天0晚
        int night = 0;
        if (CollectionUtils.isNotEmpty(combos)) {
            Combo combo = combos.get(0);
            Integer accomNights = combo.getAccomNights();
            if (Objects.nonNull(accomNights)) {
                night = accomNights;
            }
        }
        storeDO.setTripDays(night + 1);
        storeDO.setAccomNights(night);

        //property处理
        List<TravelItemPropValueDO> propertyValues = storeDO.getTravelItemExtendInfo().getPropertyValues();
        TravelItemPropValueDO travelItemPropValueDO = new TravelItemPropValueDO();
        travelItemPropValueDO.setPid(ForestConstants.TRAVEL_SCHEDULE);
        // 获取到类目属性平台对tripDay的所有cpv值对象。
        StdCategoryDO category = FliggyParamUtil.getCategoryDO(param);
        String matchTripDaysCpv = matchTripDaysCpv(category, String.valueOf(night + 1));
        travelItemPropValueDO.setVid(Long.parseLong(matchTripDaysCpv.split(":")[1]));
        travelItemPropValueDO.setPropName("行程天数");
        propertyValues.add(travelItemPropValueDO);

    }


    private String matchTripDaysCpv(StdCategoryDO category, String tripDay) {
        // 15日以上 value值固定
        if (Integer.parseInt(tripDay) >= 15) {
            return ForestConstants.TRAVEL_SCHEDULE + ":" + ForestConstants.TRAVEL_SCHEDULE_VALUE_ABOVE_15_DAY;
        }
        StdCategoryPropertyDO stdCategoryPropertyDO = category.getAllCategoryPropertiesMap().get(Integer.parseInt(String.valueOf(FliggySwitchConfig.travelDayPropId)));
        if (stdCategoryPropertyDO != null && CollectionUtils.isNotEmpty(stdCategoryPropertyDO.getValues())) {
            for (StdCategoryPropertyValueDO cpv : stdCategoryPropertyDO.getValues()) {
                if (cpv != null && cpv.getValue() != null && cpv.getValue().getValueData().equals(tripDay + "日游")) {
                    return cpv.getPropertyId() + ":" + cpv.getValueId();
                }
            }
        }
        return null;
    }

    private void processIcTag(OverseaPlayPackageInfoListControlDO controlDO, TravelItemStoreDO storeDO, CompExtParam param) {
        boolean needAddSeasonBillionTag = needAddSeasonBillionsWaterTagWithOVPList(storeDO.getTitle(), controlDO.getPackageInfos());
        // 如果有水上项目则打上水上项目标, 用于支持导购页透出十亿保障标
        if (needAddSeasonBillionTag) {
            storeDO.getExtraUpdateInfo().getAddTags().add(Store2ItemConvertor.WATER_PROJECT_MILLIONS_GUARANTEED_TAG);
        } else {
            storeDO.getExtraUpdateInfo().getRemoveTags().add(Store2ItemConvertor.WATER_PROJECT_MILLIONS_GUARANTEED_TAG);
        }

        // 打上新境外玩乐商品标
        storeDO.getExtraUpdateInfo().getAddTags().add(TagsConstants.OVERSEA_PLAY_ITEM_TAG);
        // 打上玩乐出行人标
        storeDO.getExtraUpdateInfo().getAddTags().addAll(FliggyPlayAbroadSwitchConfig.PLAY_TRAVELLER_TAG_LIST);
        // 购物车屏蔽标
        if (FliggyPlayAbroadSwitchConfig.ADD_DISABLED_SHOP_CART_TAG) {
            storeDO.getExtraUpdateInfo().getAddTags().add(TagsConstants.SHIELD_CART_TAG);
        } else {
            storeDO.getExtraUpdateInfo().getRemoveTags().add(TagsConstants.SHIELD_CART_TAG);
        }
        if (SystemDirectUtil.isOrderDirect(param) && FliggyPlayAbroadSwitchConfig.ORDER_DIRECT_FORBID_CAR) {
            //订单直连是否要打上购物车屏蔽标
            storeDO.getExtraUpdateInfo().getAddTags().add(TagsConstants.SHIELD_CART_TAG);
            storeDO.getExtraUpdateInfo().getRemoveTags().remove(TagsConstants.SHIELD_CART_TAG);
        } else {
            storeDO.getExtraUpdateInfo().getRemoveTags().add(TagsConstants.SHIELD_CART_TAG);
        }
        // 设置地陪讲解讲师 id 列表
        List<String> expertIdList = TravelItemsItemFeaturesHelper.getExpertIdListFromPlayPackageControlDO(controlDO);
        if (CollectionUtils.isNotEmpty(expertIdList)) {
            storeDO.getAlitripFeatures().put("expertIds", String.join(",", expertIdList));
        }
        // 二次预约二次确认标
        if (SellType.PreSell.equals(storeDO.getSellType()) && storeDO.getConfirmType() == 2) {
            storeDO.getExtraUpdateInfo().getAddTags().add(Store2ItemConvertor.SECOND_APPOINTMENT_SECOND_CONFIRMATION);
        } else {
            storeDO.getExtraUpdateInfo().getRemoveTags().add(Store2ItemConvertor.SECOND_APPOINTMENT_SECOND_CONFIRMATION);
        }
    }

    public static boolean needAddSeasonBillionsWaterTagWithOVPList(String title, List<OverseaPlayPackageInfoControlDO> packageList) {
        // 标题判断
        if (containKeyWord(title)) {
            return Boolean.TRUE;
        }
        for (OverseaPlayPackageInfoControlDO itemPackage : packageList) {
            // 套餐名
            Boolean packageContainKeyWord = Optional.ofNullable(itemPackage)
                    .map(OverseaPlayPackageInfoControlDO::getPackageName)
                    .map(OverseaPlayBO2ComboConvertor::containKeyWord)
                    .orElse(Boolean.FALSE);
            if (packageContainKeyWord) {
                return Boolean.TRUE;
            }
            List<PlayTheme> playThemeList = Optional.ofNullable(itemPackage)
                    .map(OverseaPlayPackageInfoControlDO::getPlayThemeList)
                    .orElse(new ArrayList<>());
            // 玩法
            if (CollectionUtils.isNotEmpty(playThemeList)) {
                boolean containsPlayMethod = playThemeList.stream()
                        .filter(Objects::nonNull)
                        .map(PlayTheme::getPlayThemeId)
                        .anyMatch(FliggyPlayAbroadSwitchConfig.SEASON_BILLIONS_WATER_PROJECT_PLAY_IDS::contains);
                if (containsPlayMethod) {
                    return Boolean.TRUE;
                }
            }
        }

        return Boolean.FALSE;
    }

    public static boolean containKeyWord(String text) {
        if (StringUtils.isNotBlank(text)) {
            return FliggyPlayAbroadSwitchConfig.SEASON_BILLIONS_PLAY_DESC.stream().anyMatch(text::contains);
        }
        return Boolean.FALSE;
    }

    /**
     * 将日历套餐信息转换为TravelItemStoreDO
     *
     * @param packageInfo 套餐信息
     * @param index       索引
     * @param combo       组合对象
     * @param storeDO     存储对象
     * @param param       扩展参数
     */
    public void convertCalendarComboToStoreDO(OverseaPlayPackageInfoControlDO packageInfo, int index, Combo combo, TravelItemStoreDO storeDO, CompExtParam param) {
        if (CollectionUtils.isEmpty(packageInfo.getPlayCanlendePriceStockControlDO())) {
            LogUtil.sysInfoLog("convertCalendarComboToStoreDO", "套餐价库信息为空, 套餐名称: ", packageInfo.getPackageName());
            return;
        }

        List<TravelItemSku> skuList = new ArrayList<>();
        combo.setSkuList(skuList);

        // 处理一口价和分类价格的情况
        if (BooleanUtils.isTrue(packageInfo.getFixedPrice())) {
            // 一口价：找到价格类型为0的记录处理
            for (PlaySkuPriceStockControlDO priceStockDO : packageInfo.getPlayCanlendePriceStockControlDO()) {
                if (priceStockDO.getPriceType() != PriceType.NODATA.getValue()) {
                    continue;
                }
                TravelItemSku itemSku = createBasicSku(packageInfo, index, combo, storeDO);
                processPriceStockData(priceStockDO, itemSku, param, storeDO);
                // 处理直连配置
                processCalendarDirectConfig(priceStockDO, itemSku, param, storeDO);
                skuList.add(itemSku);
            }
        } else {
            // 分类价格：处理成人、儿童、单房差
            for (PlaySkuPriceStockControlDO priceStockDO : packageInfo.getPlayCanlendePriceStockControlDO()) {
                if (priceStockDO.getPriceType() == PriceType.NODATA.getValue()) {
                    continue;
                }

                TravelItemSku itemSku = createBasicSku(packageInfo, index, combo, storeDO);
                processPriceStockData(priceStockDO, itemSku, param, storeDO);
                // 处理直连配置
                processCalendarDirectConfig(priceStockDO, itemSku, param, storeDO);

                // 添加价格类型属性
                addPriceTypeProperty(itemSku, priceStockDO.getPriceType());

                skuList.add(itemSku);
            }
        }

    }

    /**
     * 转换二次预约套餐
     *
     * @param packageInfo
     * @param index
     * @param combo
     * @param storeDO
     * @param param
     */
    public void convertPreSellComboToStoreDO(OverseaPlayPackageInfoControlDO packageInfo, int index, Combo combo, TravelItemStoreDO storeDO, CompExtParam param) {
        if (packageInfo.getPackagePrice() == null || packageInfo.getPackageStock() == null) {
            return;
        }

        Long price = MoneyUtil.string2CentOfMoney(packageInfo.getPackagePrice());

        List<TravelItemSku> skuList = new ArrayList<>();
        combo.setSkuList(skuList);

        TravelItemSku itemSku = createBasicSku(packageInfo, index, combo, storeDO);
        itemSku.setPrice(price == null ? null : Math.toIntExact(price));
        itemSku.setQuantity(packageInfo.getPackageStock());
        itemSku.setSkuId(packageInfo.getSkuId());

        skuList.add(itemSku);

    }

    /**
     * 转换普通套餐
     *
     * @param packageInfo
     * @param index
     * @param combo
     * @param storeDO
     * @param param
     */
    public void convertUncalendarComboToStoreDO(OverseaPlayPackageInfoControlDO packageInfo, int index, Combo combo, TravelItemStoreDO storeDO, CompExtParam param) {
        if (packageInfo.getPackagePrice() == null || packageInfo.getPackageStock() == null) {
            return;
        }

        Long price = MoneyUtil.string2CentOfMoney(packageInfo.getPackagePrice());

        List<TravelItemSku> skuList = new ArrayList<>();
        combo.setSkuList(skuList);

        TravelItemSku itemSku = createBasicSku(packageInfo, index, combo, storeDO);
        itemSku.setPrice(price == null ? null : Math.toIntExact(price));
        itemSku.setQuantity(packageInfo.getPackageStock());
        itemSku.setSkuId(packageInfo.getSkuId());

        processUnCalendarDirectConfig(packageInfo, itemSku, param, storeDO);

        skuList.add(itemSku);

    }

    /**
     * 创建基础SKU对象
     */
    private TravelItemSku createBasicSku(OverseaPlayPackageInfoControlDO packageInfo, int index, Combo combo, TravelItemStoreDO storeDO) {
        TravelItemSku itemSku = new TravelItemSku();

        // 设置基本信息
        itemSku.setFeeInclude(packageInfo.getFeeInclude());
        itemSku.setFeeExclude(packageInfo.getFeeExclude());
        itemSku.setBooktips(packageInfo.getPackageDesc());
        itemSku.setPackageDesc(packageInfo.getPackageDesc());
        itemSku.getPropertyList().add(buildPackageNameItemPair(index, combo.getPackageId(), combo.getComboName()));
        itemSku.setPackageId(combo.getPackageId());

        // 处理佣金商品标
        processCommissionTag(packageInfo, itemSku);

        // 处理白名单weeks配置
        processWeeksConfig(itemSku, storeDO);

        return itemSku;
    }

    /**
     * 处理佣金商品标
     */
    private void processCommissionTag(OverseaPlayPackageInfoControlDO packageInfo, TravelItemSku itemSku) {
        if (CollectionUtils.isNotEmpty(packageInfo.getPackageElementsControlDO().getElementInfoList())
                && Objects.equals(packageInfo.getPackageElementsControlDO().getElementInfoList().size(), 1)) {
            BasePlayElementGroupBO elementGroup = CollectionUtil.getFirst(packageInfo.getPackageElementsControlDO().getElementInfoList());

            if (FliggyPlayAbroadSwitchConfig.CATEGORY_COMMISSION_MAP.containsKey(elementGroup.getElementCategory())) {
                itemSku.getSkuFeatures().put("_F_play", String.valueOf(FliggyPlayAbroadSwitchConfig.CATEGORY_COMMISSION_MAP.get(elementGroup.getElementCategory())));
            }
        }
    }

    /**
     * 处理白名单weeks配置
     */
    private void processWeeksConfig(TravelItemSku itemSku, TravelItemStoreDO storeDO) {
        String itemId = String.valueOf(storeDO.getItemId());
        if (FliggyPlayAbroadSwitchConfig.WHITE_MAP_WEEKS.containsKey(itemId)) {
            Optional.ofNullable(FliggyPlayAbroadSwitchConfig.WHITE_MAP_WEEKS.get(itemId))
                    .ifPresent(itemSku::setWeeks);
        }
    }

    /**
     * 处理价格和库存数据
     */
    private void processPriceStockData(PlaySkuPriceStockControlDO priceStockDO, TravelItemSku itemSku,
                                       CompExtParam param, TravelItemStoreDO storeDO) {
        // 设置价格库存信息
        itemSku.setPrices(buildCalendarPrice(priceStockDO));
        itemSku.setQuantitys(buildCalendarStock(priceStockDO));

        // 设置不可售日期
        Optional.of(priceStockDO)
                .map(PlaySkuPriceStockControlDO::getUnavailableDate)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(dates -> itemSku.getSkuFeatures().put(DirectConstants.UNAVAILABLE_DATES, String.join(",", dates)));

        // 设置可选出发日期
        setDepartureDate(priceStockDO, itemSku, param);

        // 设置价格类型和SKU ID
        itemSku.setPriceType(PriceType.ADULT);
        itemSku.getPropertyList().add(new ItemPVPair(ForestConstants.BOOKING_PACKAGE,
                ForestConstants.ADULT_PRICE_STOCK_VALUE_ID));
        itemSku.setSkuId(priceStockDO.getSkuId());
    }

    /**
     * 处理日历直连配置
     */
    private void processCalendarDirectConfig(PlaySkuPriceStockControlDO priceStockDO, TravelItemSku itemSku,
                                             CompExtParam param, TravelItemStoreDO storeDO) {
        PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo = priceStockDO.getPlayAbroadPackageSkuDirectInfo();
        if (Objects.isNull(playAbroadPackageDirectInfo)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(priceStockDO.getUnavailableDate())) {
            playAbroadPackageDirectInfo.setUnavailableDate(priceStockDO.getUnavailableDate());
        }

        // 设置SKU特性
        Map<String, String> skuFeatures = itemSku.getSkuFeatures();
        skuFeatures.put(DirectConstants.PLAY_DIRECT, "1");
        skuFeatures.put(DirectConstants.PLAY_CALENDAR_DATE_OUT_CODE_FEATURE_KEY, "true");
        skuFeatures.put(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO, JSON.toJSONString(playAbroadPackageDirectInfo));

        // 构建系统商直连信息
        SystemVendorDirectCodes systemVendorDirectCodes = new SystemVendorDirectCodes();
        SystemVendorDirectCode systemVendorDirectCode = new SystemVendorDirectCode();
        systemVendorDirectCode.setSpId(playAbroadPackageDirectInfo.getSpId());
        systemVendorDirectCode.setSystemVendorCode(playAbroadPackageDirectInfo.getSupplierProductCode());
        systemVendorDirectCode.setSystemVendorDirectFeatures(new HashMap<>());
        systemVendorDirectCode.getSystemVendorDirectFeatures().put(DirectConstants.ADD_PRICE_RULE,
                JSON.toJSONString(playAbroadPackageDirectInfo.getAddPriceRuler()));
        systemVendorDirectCode.getSystemVendorDirectFeatures().put(DirectConstants.CROWD_CODE,
                playAbroadPackageDirectInfo.getCrowdCode());
        systemVendorDirectCodes.setSystemVendorDirectCode(systemVendorDirectCode);

        // 构建日历系统商码
        buildCalendarSystemVendorCodes(systemVendorDirectCodes, priceStockDO);
        itemSku.setSystemVendorDirectCodes(systemVendorDirectCodes);

        // 处理动态出行人
        processDynamicTravelPerson(itemSku, playAbroadPackageDirectInfo, param, storeDO);
    }

    /**
     * 处理普通商品直连配置
     */
    private void processUnCalendarDirectConfig(OverseaPlayPackageInfoControlDO playPackageInfoControlDO, TravelItemSku itemSku,
                                               CompExtParam param, TravelItemStoreDO storeDO) {
        PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo = playPackageInfoControlDO.getPlayAbroadPackageDirectInfo();
        if (Objects.isNull(playAbroadPackageDirectInfo)) {
            return;
        }

        // 设置SKU特性
        Map<String, String> skuFeatures = itemSku.getSkuFeatures();
        skuFeatures.put(DirectConstants.PLAY_DIRECT, "1");
        skuFeatures.put(DirectConstants.PLAY_CALENDAR_DATE_OUT_CODE_FEATURE_KEY, "true");
        skuFeatures.put(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO, JSON.toJSONString(playAbroadPackageDirectInfo));

        // 构建系统商直连信息
        SystemVendorDirectCodes systemVendorDirectCodes = new SystemVendorDirectCodes();
        SystemVendorDirectCode systemVendorDirectCode = new SystemVendorDirectCode();
        systemVendorDirectCode.setSpId(playAbroadPackageDirectInfo.getSpId());
        //这个地方不太对，应该要取价库上的outcode，而不是供应商的产品编码，因为outcode是会根据系统商的配置生成的，但也还好，普通商品没有交易直连，目前没有问题
        systemVendorDirectCode.setSystemVendorCode(playAbroadPackageDirectInfo.getSupplierProductCode());
        systemVendorDirectCode.setSystemVendorDirectFeatures(new HashMap<>());
        systemVendorDirectCode.getSystemVendorDirectFeatures().put(DirectConstants.ADD_PRICE_RULE,
                JSON.toJSONString(playAbroadPackageDirectInfo.getAddPriceRuler()));
        systemVendorDirectCode.getSystemVendorDirectFeatures().put(DirectConstants.CROWD_CODE,
                playAbroadPackageDirectInfo.getCrowdCode());
        systemVendorDirectCodes.setSystemVendorDirectCode(systemVendorDirectCode);
        itemSku.setSystemVendorDirectCodes(systemVendorDirectCodes);

        // 处理动态出行人
        processDynamicTravelPerson(itemSku, playAbroadPackageDirectInfo, param, storeDO);
    }

    /**
     * 处理动态出行人
     */
    private void processDynamicTravelPerson(TravelItemSku itemSku, PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo,
                                            CompExtParam param, TravelItemStoreDO storeDO) {
        if (!SystemDirectUtil.isOrderDirect(param) ||
                !storeDO.getAlitripFeatures().containsKey(AlitripFeatureConstants.SUPPLIER_ID)) {
            return;
        }

        long supplierId = Long.parseLong(storeDO.getAlitripFeatures().get(AlitripFeatureConstants.SUPPLIER_ID));
        if (!FliggyPlayAbroadSwitchConfig.DYNAMIC_TRAVEL_PERSON.contains(supplierId)) {
            return;
        }

        // 从LLM获取出行人信息
        Pair<TravelPerson, String> personInfo = llmTaskServiceRepo.getPersonMessageAndSwitchAgreementFromLLM(
                playAbroadPackageDirectInfo.getSpId());
        if (personInfo != null) {
            itemSku.setTravelPerson(personInfo.getValue0());
            itemSku.getSkuFeatures().put("switchAgreement", personInfo.getValue1());
        }
    }

    /**
     * 添加价格类型属性
     */
    private void addPriceTypeProperty(TravelItemSku itemSku, int priceType) {
        if (Objects.equals(priceType, PriceType.KID.getValue())) {
            itemSku.getPropertyList().add(new ItemPVPair(ForestConstants.BOOKING_PACKAGE,
                    ForestConstants.CHILD_PRICE_STOCK_VALUE_ID));
        } else if (Objects.equals(priceType, PriceType.ADULT.getValue())) {
            itemSku.getPropertyList().add(new ItemPVPair(ForestConstants.BOOKING_PACKAGE,
                    ForestConstants.ADULT_PRICE_STOCK_VALUE_ID));
        } else if (Objects.equals(priceType, PriceType.SUPPLEMENT.getValue())) {
            itemSku.getPropertyList().add(new ItemPVPair(ForestConstants.BOOKING_PACKAGE,
                    ForestConstants.SUPPLEMENT_PRICE_STOCK_VALUE_ID));
        }
    }

    private void buildCalendarSystemVendorCodes(SystemVendorDirectCodes systemVendorDirectCodes, PlaySkuPriceStockControlDO playSkuPriceStockControlDO) {
        if (systemVendorDirectCodes.getCalendarSystemVendorDirectCodes() == null) {
            systemVendorDirectCodes.setCalendarSystemVendorDirectCodes(new HashMap<>());
        }

        playSkuPriceStockControlDO.getCalendarPriceStock().forEach(t -> {
            systemVendorDirectCodes.getCalendarSystemVendorDirectCodes().put(
                    Date.from(LocalDate.parse(t.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()),
                    SystemVendorDirectCode.builder().spId(playSkuPriceStockControlDO.getPlayAbroadPackageSkuDirectInfo().getSpId()).systemVendorCode(t.getOuterCode()).build()
            );
        });
    }

    private void setDepartureDate(PlaySkuPriceStockControlDO playSkuPriceStockControlDO, TravelItemSku itemSku, CompExtParam param) {
        BasicCompDO<DateRangeValueDO> dependCompDO = param.getDependCompDO(CompConstants.SECOND_START_END_COMBO_DATE);
        if (dependCompDO != null && dependCompDO.getValue() != null) {
            DateRangeValueDO value = dependCompDO.getValue();
            itemSku.setStart(value.getStartDate());
            itemSku.setEnd(value.getEntDate());
        }
        StreamUtils.asStream(itemSku.getPrices().keySet()).min(Comparator.comparing(Date::getTime)).ifPresent(itemSku::setStart);
        StreamUtils.asStream(itemSku.getPrices().keySet()).max(Comparator.comparing(Date::getTime)).ifPresent(itemSku::setEnd);

    }

    private Map<Date, Long> buildCalendarPrice(PlaySkuPriceStockControlDO price) {
        Map<Date, Long> res = Maps.newHashMap();
        Optional.ofNullable(price.getCalendarPriceStock())
                .ifPresent(k -> k.forEach(dailyPrice -> {
                    try {
                        res.put(DateUtils.parseDate(dailyPrice.getDate(), "yyyy-MM-dd"), MoneyUtil.string2CentOfMoney(dailyPrice.getPrice()));
                    } catch (ParseException e) {
                        LogUtil.sysErrorLog("buildCalendarPrice", e, "date:", dailyPrice.getDate(), " price:", dailyPrice.getPrice());
                        throw new RuntimeException(e);
                    }
                }));
        return res;
    }

    private Map<Date, Integer> buildCalendarStock(PlaySkuPriceStockControlDO stock) {
        Map<Date, Integer> res = Maps.newHashMap();
        Optional.ofNullable(stock.getCalendarPriceStock())
                .ifPresent(k -> {
                    List<String> unavailableDates = Optional.ofNullable(stock.getUnavailableDate()).orElse(Lists.newArrayList());
                    k.forEach(dailyStock -> {
                        if (!unavailableDates.contains(dailyStock.getDate()) && Objects.nonNull(dailyStock.getStock()) && dailyStock.getStock() >= 0L) {
                            try {
                                res.put(DateUtils.parseDate(dailyStock.getDate(), "yyyy-MM-dd"), Math.toIntExact(dailyStock.getStock()));
                            } catch (ParseException e) {
                                LogUtil.sysErrorLog("buildVacationStock", e, "date:", dailyStock.getDate(), " stock:", dailyStock.getStock() + "");
                                throw new RuntimeException(e);
                            }
                        }
                    });
                });
        return res;
    }

    private ItemPVPair buildPackageNameItemPair(Integer index, Long packageId, String packageName) {
        TravelItemPropDO packageTypePairs = travelSellConfig.getTravelItemPropDO(CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID, ForestConstants.PACKAGE_TYPE);

        // 兼容下排序
        List<TravelItemPropValueDO> packageTypeValues = packageTypePairs.getTravelItemPropValueDOs().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(TravelItemPropValueDO::getVid))
                .collect(Collectors.toList());

        TravelItemPropValueDO packageTypePair = packageTypeValues.stream().filter(t -> Objects.equals(t.getVid(), packageId))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("套餐类型不存在"));

        ItemPVPair itemPvPair = new ItemPVPair();
        itemPvPair.setPropertyId(packageTypePair.getPid());
        itemPvPair.setValueId(packageTypePair.getVid());
        itemPvPair.setPropertyText(travelSellConfig.getTravelForestCache().getPropName(packageTypePair.getCid(), packageTypePair.getPid()));
        itemPvPair.setValueText(travelSellConfig.getTravelForestCache().getValueName(packageTypePair.getCid(), packageTypePair.getPid(), packageTypePair.getVid()));
        itemPvPair.setValueAliasText(packageName.length() < 30 ? packageName : packageName.substring(0, 30));
        itemPvPair.setValueSortOrder(index);

        return itemPvPair;
    }

    /**
     * 当任意套餐下的地陪元素业务类型为讲解时, 就需要在商品上打上线路真人讲解商品标签
     *
     * @param controlDO
     * @param storeDO   商品模型
     */
    private void buildRealPersonExplainItemTag(OverseaPlayPackageInfoListControlDO controlDO, TravelItemStoreDO storeDO) {
        if (Objects.isNull(controlDO) || Objects.isNull(storeDO)) {
            return;
        }
        // 层级比较深, 这里只能一层一层获取
        boolean hasExplainLocalTouristGuide = controlDO.getPackageInfos().stream()
                // 遍历套餐获取套餐的玩乐套餐信息
                .filter(t -> Objects.equals(t.getMainElementType(), ElementCategoryEnum.TOUR.getType()))
                // 获取套餐元素组
                .map(OverseaPlayPackageInfoControlDO::getPackageElementsControlDO)
                // 防御编程增加 NPE 判断
                .filter(Objects::nonNull)
                // 获取套餐元素组
                .map(PackageElementsControlDO::getElementInfoList)
                // 防御编程增加 NPE 判断
                .filter(CollectionUtils::isNotEmpty)
                // 打平套餐元素组 list
                .flatMap(Collection::stream)
                // 过滤出元素类型为地陪类型的元素
                .filter(t -> Objects.equals(ElementCategoryEnum.TOUR.getType(), t.getElementCategory()))
                // 判空并且判断对象类型
                .filter(t -> Objects.nonNull(t) && (t instanceof LocalTouristGuideElementGroupBO))
                // 强转为地陪元素类型对象
                .map(t -> (LocalTouristGuideElementGroupBO) t)
                // 判断是否有一个对象满足地陪元素的业务类型为讲解类型
                .anyMatch(t -> {
                            if (CollectionUtils.isEmpty(t.getLocalTouristGuideElementList())) {
                                return false;
                            }
                            for (ElementBO<LocalTouristGuideValueMapBO> valueMapBO : t.getLocalTouristGuideElementList()) {
                                if (Objects.isNull(valueMapBO.getValueMap())) {
                                    continue;
                                }

                                if (Objects.equals(valueMapBO.getValueMap().getBizType(), LocalTouristGuideBizTypeEnum.EXPLAIN.name())) {
                                    return true;
                                }
                            }
                            return false;
                        }
                );
        // 满足条件且没有打过的需要增加标签, 否则去除标签
        if (hasExplainLocalTouristGuide) {
            storeDO.getExtraUpdateInfo().getAddTags().add(IcTage.GUIDE_ITEM_TAG);
        } else {
            storeDO.getExtraUpdateInfo().getRemoveTags().add(IcTage.GUIDE_ITEM_TAG);
        }
    }

    private void buildSelfGuidedTourPvPairs(OverseaPlayPackageInfoListControlDO controlDO, TravelItemStoreDO storeDO) {
        Set<Integer> resourceTypeSet = Sets.newHashSet();
        if (CollectionUtils.isEmpty(controlDO.getPackageInfos())) {
            return;
        }
        for (OverseaPlayPackageInfoControlDO packageInfo : controlDO.getPackageInfos()) {
            if (Objects.isNull(packageInfo.getPackageElementsControlDO()) || CollectionUtils.isEmpty(packageInfo.getPackageElementsControlDO().getElementInfoList())) {
                continue;
            }
            for (BasePlayElementGroupBO elementGroup : packageInfo.getPackageElementsControlDO().getElementInfoList()) {
                Integer type = elementGroup.getElementCategory();
                resourceTypeSet.add(type);
            }
        }

        List<TravelItemPropValueDO> travelItemPropValueDOList = StreamUtils.asStream(resourceTypeSet).filter(Objects::nonNull)
                .map(type -> FliggyPlayAbroadSwitchConfig.RESOURCE_TYPE_2_SELF_GUIDED_TOUR_PV_PAIRS.get(type + StringUtils.EMPTY))
                .filter(StringUtils::isNotEmpty)
                .map(this::transPvIdsToPropValueDOs)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        storeDO.getTravelItemExtendInfo().getPropertyValues().addAll(travelItemPropValueDOList);
    }

    private TravelItemPropValueDO transPvIdsToPropValueDOs(String pvId) {
        if (StringUtils.isBlank(pvId) || !pvId.contains(":")) {
            return null;
        }

        String[] pvIds = pvId.split(":");
        if (pvIds.length != 2 || !StringUtils.isNumeric(pvIds[0]) || !StringUtils.isNumeric(pvIds[1])) {
            return null;
        }

        TravelItemPropValueDO travelItemPropValueDO = new TravelItemPropValueDO();
        travelItemPropValueDO.setPid(Long.parseLong(pvIds[0]));
        travelItemPropValueDO.setVid(Long.parseLong(pvIds[1]));
        return travelItemPropValueDO;
    }

    private void setTravelItemPropertyValuesFromCustomElement(List<Product> customProducts, TravelItemStoreDO storeDO) {
        // 设置自定义元素pv对
        List<ItemPVPair> customItemPvPairList;
        customItemPvPairList = customElementConverter.buildItemPVPairFromCustomProducts(customProducts);

        // 自定义元素设置到商品cpv
        List<TravelItemPropValueDO> travelItemPropValueDOList = customItemPvPairList.stream()
                .filter(Objects::nonNull)
                .map(this::buildPropertyValueDO)
                .collect(Collectors.toList());
        storeDO.getTravelItemExtendInfo().getPropertyValues().addAll(travelItemPropValueDOList);
    }

    private TravelItemPropValueDO buildPropertyValueDO(ItemPVPair itemPVPair) {
        TravelItemPropValueDO travelItemPropValueDO = new TravelItemPropValueDO();
        travelItemPropValueDO.setPid(itemPVPair.getPropertyId());
        travelItemPropValueDO.setVid(itemPVPair.getValueId());
        return travelItemPropValueDO;
    }

    /**
     * 将套餐信息转换为Combo对象
     *
     * @param packageInfo          套餐信息
     * @param index                索引
     * @param needAddProductCpvMap
     * @param param
     * @return Combo对象
     */
    private Combo convertPackageInfoToCombo(OverseaPlayPackageInfoControlDO packageInfo, int index, Map<Integer, Product> needAddProductCpvMap, CompExtParam param) {
        Combo combo = new Combo();

        // 设置套餐基本信息
        combo.setComboName(packageInfo.getPackageName());
        combo.setOuterId(packageInfo.getOutId());
        //todo 是不是需要跟vpublish一样设置套餐id
        if (packageInfo.getPackageId() != null) {
            combo.setPackageId(packageInfo.getPackageId());
        } else {
            combo.setPackageId(getPackageValueId(index));
        }

        // 设置出行人
        buildTravelPerson(packageInfo, combo);

        //设置套餐间夜
        buildPackageRoomNight(packageInfo, combo);

        return combo;
    }

    private void buildElement(OverseaPlayPackageInfoControlDO packageInfo, Map<Integer, Product> needAddProductCpvMap, CompExtParam param, Combo combo) {
        // 设置元素库元素
        List<Product> productList = buildPackageElements(packageInfo, combo);

        //这里发现元素库没有的话去新建
        addToElementDepositary(productList, param);


        List<Product> customProducts = new ArrayList<>();
        // 设置自定义元素和正常元素。
        StreamUtils.asStream(productList).forEach(product -> {
            if (isCustomElement(product.getElement())) {
                customProducts.add(product);
            } else {
                if (Objects.isNull(combo.getProducts())) {
                    combo.setProducts(new ArrayList<>());
                }
                combo.getProducts().add(product);
            }
        });

        // 设置自定义元素
        List<TravelItemsSkuExt> travelItemsSkuExtList = customElementConverter.buildTravelItemSkuExtList(customProducts);
        // 将自定义元素设置到sku拓展字段
        StreamUtils.asStream(combo.getSkuList()).forEach(sku -> sku.getTravelItemsSkuExtList().addAll(travelItemsSkuExtList));

        // 设置pv对
        StreamUtils.asStream(productList).filter(product -> Objects.nonNull(product) && Objects.nonNull(product.getElement()) && Objects.nonNull(product.getElement().getType()))
                .forEach(product -> {
                    if (!needAddProductCpvMap.containsKey(product.getElement().getType())) {
                        needAddProductCpvMap.put(product.getElement().getType(), product);
                    }
                });

        // 设置套餐特性
        if (MapUtils.isEmpty(combo.getComboFeatures())) {
            combo.setComboFeatures(Maps.newHashMap());
        }

        // 如果有玩法信息，添加到特性中
        if (Objects.nonNull(packageInfo.getPlayThemeBO())) {
            List<PlayTheme> productLinePlayThemes = playThemeBOConvertor.handleSubmitPlayThemes(packageInfo.getPlayThemeBO());
            packageInfo.setPlayThemeList(productLinePlayThemes);
            combo.getComboFeatures().put(PlayThemeConverter.PLAY_PROP,
                    //这么写有病，但是先这样
                    JSON.toJSONString(playThemeBOConvertor.handleRenderVPublishPlayTheme(productLinePlayThemes))
            );
        }

        if (Objects.nonNull(packageInfo.getActivityHour())) {
            combo.getComboFeatures().put("activityDuration", String.valueOf(packageInfo.getActivityHour()));
        }

        if (Objects.nonNull(packageInfo.getFixedPrice())) {
            combo.getComboFeatures().put("doesIndependentStockForDifferentCrowds", String.valueOf(!packageInfo.getFixedPrice()));
        }

        // 设置讲师id, 兼容历史线路写入逻辑, 这里combo feature中也需要写入
        buildExpertId(combo, packageInfo);
    }

    private void addToElementDepositary(List<Product> productList, CompExtParam param) {
        productList.iterator().forEachRemaining(product -> {
            if (Objects.isNull(product) || Objects.isNull(product.getElement()) || product.getElement().getId() != null) {
                return;
            }
            Element element = product.getElement();
            Element newElement = commonElementServiceRepo.addNewElementToElementLibrary(FliggyParamUtil.getUserId(param), FliggyParamUtil.getCategoryId(param), element);
            product.setElement(newElement);
        });
    }

    private Long getPackageValueId(Integer index) {

        TravelItemPropDO packageTypePairs = travelSellConfig.getTravelItemPropDO(CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID, ForestConstants.PACKAGE_TYPE);

        // 兼容下排序
        List<TravelItemPropValueDO> packageTypeValues = packageTypePairs.getTravelItemPropValueDOs().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(TravelItemPropValueDO::getVid))
                .collect(Collectors.toList());
        TravelItemPropValueDO packageTypePair = packageTypeValues.get(index);
        return packageTypePair.getVid();
    }

    private boolean isCustomElement(Element element) {
        return FliggyPlayAbroadSwitchConfig.CUSTOM_ELEMENT_LIST.contains(element.getType());
    }

    public List<Product> buildPackageElements(OverseaPlayPackageInfoControlDO packageInfo, Combo combo) {
        //这里为了快速写代码，直接把vpublish的代码拷贝过来，后续可以去掉vpp依赖，少convert一层
        List<ElementGroup> elementGroups = packageElementConvert.handleSubmitElementGroup(packageInfo.getPackageElementsControlDO().getElementInfoList());

        return StreamUtils.asStream(elementGroups)
                .flatMap(eleGroup -> StreamUtils.asStream(eleGroup.getElements()))
                .map(ele -> fromPackageElement(ele, combo, packageInfo)).collect(Collectors.toList());
    }

    /**
     * 使用vpp元素build产品
     */
    private Product fromPackageElement(PackageElement packageElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoDTO) {
        if (MapUtils.isEmpty(combo.getComboFeatures())) {
            combo.setComboFeatures(Maps.newHashMap());
        }

        Integer elementCategory = packageElement.getElementCategoryEnum();
        IElementConverter elementConverter = elementConverterList.stream()
                .filter(Objects::nonNull)
                .filter(converter -> Objects.equals(converter.getElementCategory(), elementCategory))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("没有找到对应的元素converter"));
        return elementConverter.fromOvpElement(packageElement, combo, playPackageInfoDTO);
    }

    public void buildTravelPerson(OverseaPlayPackageInfoControlDO packageInfo, Combo combo) {
        if (packageInfo.getTravelPersonTemplateId() != null) {
            TravelPerson travelPerson = new TravelPerson();
            travelPerson.setTemplateId(packageInfo.getTravelPersonTemplateId());
            combo.setTravelPerson(travelPerson);
        } else if (packageInfo.getTravelPerson() != null) {
            // 如果有动态出行人信息，优先使用
            combo.setTravelPerson(packageInfo.getTravelPerson());
        }
    }

    public void processPackageNight(TravelItemStoreDO storeDO) {
        // 处理套餐间间夜和不一致打标和去标的情况
        if (isNotSamePackageNight(storeDO.getCombos())) {
            storeDO.getExtraUpdateInfo().getAddTags().add(TagsConstants.PACKAGE_NIGHT_NOT_SAME);
        } else {
            storeDO.getExtraUpdateInfo().getRemoveTags().add(TagsConstants.PACKAGE_NIGHT_NOT_SAME);
        }
        // 标含义 "酒景商品优先使用套餐级别间夜信息"
        storeDO.getExtraUpdateInfo().getAddTags().add(TagsConstants.PRIORITY_EACH_NIGHT);
    }

    private boolean isNotSamePackageNight(List<Combo> combos) {
        if (CollectionUtils.isEmpty(combos)) {
            return true;
        }
        return StreamUtils.asStream(combos).map(Combo::getAccomNights).collect(Collectors.toSet()).size() != 1;
    }

    public void buildPackageRoomNight(OverseaPlayPackageInfoControlDO playPackageInfoControlDO, Combo combo) {
        if (Objects.equals(ResourceType.HOTEL.getType(), playPackageInfoControlDO.getMainElementType())) {
            Integer accomNights = StreamUtils.asStream(playPackageInfoControlDO.getPackageElementsControlDO().getElementInfoList())
                    // 当前只算酒店的间夜数
                    .filter(elementGroup -> Objects.equals(ResourceType.HOTEL.getType(), elementGroup.getElementCategory()))
                    .map(t -> ((HotelElementGroupBO) t).getHotelElementList())
                    .flatMap(List::stream)
                    .map(ElementBO::getValueMap)
                    .map(HotelElementValueMapBO::getNightNumber)
                    .filter(Objects::nonNull)
                    .reduce(Integer::sum)
                    .orElse(0);
            combo.setAccomNights(accomNights);
            combo.setTripDays(accomNights + 1);
        } else {
            combo.setAccomNights(0);
            combo.setTripDays(1);
        }
    }

    /**
     * 设置讲师 id
     *
     * @param combo                组件信息
     * @param packageInfoControlDO 套餐信息
     */
    private void buildExpertId(Combo combo, OverseaPlayPackageInfoControlDO packageInfoControlDO) {
        List<String> expertIdListFromPackage = TravelItemsItemFeaturesHelper.getExpertIdListFromPackage(packageInfoControlDO);
        if (CollectionUtils.isNotEmpty(expertIdListFromPackage)) {
            combo.getComboFeatures().put("expertId", CollectionUtil.getFirst(expertIdListFromPackage));
        }
    }
}