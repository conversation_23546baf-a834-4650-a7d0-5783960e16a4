package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.packageelements.CellularNetworkTypeEnum;
import com.fliggy.vic.common.constant.packageelements.PhoneCardTypeEnum;
import com.fliggy.vic.common.constant.packageelements.VacationPackageElementCategoryEnum;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.element.PhoneCardElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class PhoneCardElementConverter extends AbstractElementConverter implements IElementConverter{

    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.PHONECARD.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.PHONE_CARD.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        Element element = product.getElement();
        PhoneCardElement phoneCardElement = new PhoneCardElement();
        phoneCardElement.setElementCategoryEnum(VacationPackageElementCategoryEnum.PHONE_CARD.getCode());
        phoneCardElement.setName(element.getName());
        phoneCardElement.setCardType(convert2CardType(element));
        phoneCardElement.setCellularNetworkType(convert2CellularNetworkType(element));
        phoneCardElement.setDesc(element.getDescr());
        phoneCardElement.setAdditionalRemarks(element.getDescr());
        return phoneCardElement;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        PhoneCardElement phoneCardElement = (PhoneCardElement) pElement;
        Product product = super.fromOvpElement(phoneCardElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        elementFeatures.add(new ElementFeature("NAME", phoneCardElement.getName()));
        Optional.ofNullable(PhoneCardTypeEnum.getByCode(phoneCardElement.getCardType()))
                .map(PhoneCardTypeEnum::getDesc).ifPresent(desc -> elementFeatures.add(new ElementFeature("SIZE", desc)));
        Optional.ofNullable(CellularNetworkTypeEnum.getByCode(phoneCardElement.getCellularNetworkType()))
                .map(CellularNetworkTypeEnum::getDesc).ifPresent(desc -> elementFeatures.add(new ElementFeature("SIM_NET", desc)));
        elementFeatures.add(new ElementFeature("DESC", phoneCardElement.getDesc()));
        elementFeatures.add(new ElementFeature("OUTER_ID", phoneCardElement.getOuterCode()));
        combo.getComboFeatures().put("phoneCardDescr", "");
        return product;
    }

    private Integer convert2CellularNetworkType(Element element) {
        List<ElementFeature> elementFeatures = element.getElementFeatures();
        if (CollectionUtils.isNotEmpty(elementFeatures)) {
            for (ElementFeature elementFeature : elementFeatures) {
                if (elementFeature.getFeatureKey().equals("SIM_NET")) {
                    // TODO: SIM_NET在sell存的是枚举里的desc
                    return Integer.valueOf(elementFeature.getValue());
                }
            }
        }
        return null;
    }

    private Integer convert2CardType(Element element) {
        List<ElementFeature> elementFeatures = element.getElementFeatures();
        if (CollectionUtils.isNotEmpty(elementFeatures)) {
            for (ElementFeature elementFeature : elementFeatures) {
                if (elementFeature.getFeatureKey().equals("SIZE")) {
                    // TODO: SIZE在sell存的是枚举里的desc
                    return Integer.valueOf(elementFeature.getValue());
                }
            }
        }
        return null;
    }
}
