package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.schema;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2vo.PlayElementBO2VOConvertor;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo.*;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.model.CalendarPriceStockBO;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodValueModel;
import com.taobao.gpf.domain.component.playmethod.model.PlayProperty;
import com.taobao.gpf.domain.constant.schema.SchemaFieldIdEnum;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.IRenderFieldValueSchema;
import com.taobao.gpf.domain.utils.schema.vistor.Json2FieldVistor;
import com.taobao.top.schema.enums.FieldTypeEnum;
import com.taobao.top.schema.enums.ValueTypeEnum;
import com.taobao.top.schema.field.*;
import com.taobao.top.schema.value.ComplexValue;
import com.taobao.top.schema.value.Value;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Component
public class OverseaPlayPackageInfoRenderFieldValue implements IRenderFieldValueSchema<ParentCompDO<AbstractCompDO>, MultiComplexField> {

    @Autowired
    private PlayElementBO2VOConvertor playElementBO2VOConvertor;

    @Override
    public void renderValue(ParentCompDO<AbstractCompDO> compDO, MultiComplexField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

        OverseaPlayPackageInfoListControlDO controlDO = compDO.getControlDO();
        if (controlDO == null || CollectionUtils.isEmpty(controlDO.getPackageInfos())) {
            return;
        }

        List<ComplexValue> packageValues = new ArrayList<>();
        for (OverseaPlayPackageInfoControlDO packageInfo : controlDO.getPackageInfos()) {
            ComplexValue packageComplexValue = new ComplexValue();

            //设置套餐id
            setValueToField(packageComplexValue, SchemaFieldIdEnum.PACKAGE_ID.getId(), packageInfo.getPackageId(), FieldTypeEnum.INPUT, ValueTypeEnum.LONG);

            // 设置套餐名称
            setValueToField(packageComplexValue, SchemaFieldIdEnum.PACKAGE_NAME.getId(), packageInfo.getPackageName(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置商家编码
            setValueToField(packageComplexValue, SchemaFieldIdEnum.OUTID.getId(), packageInfo.getOutId(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置套餐使用说明
            setValueToField(packageComplexValue, SchemaFieldIdEnum.PACKAGE_DESC.getId(), packageInfo.getPackageDesc(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置套餐价格
            setValueToField(packageComplexValue, SchemaFieldIdEnum.MAN_PRICE.getId(), packageInfo.getPackagePrice(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置套餐库存
            setValueToField(packageComplexValue, SchemaFieldIdEnum.MAN_STOCK.getId(), packageInfo.getPackageStock(), FieldTypeEnum.INPUT, ValueTypeEnum.INTEGER);

            // 设置活动时长
            setValueToField(packageComplexValue, SchemaFieldIdEnum.ACTIVITY_TIME.getId(), packageInfo.getActivityHour(), FieldTypeEnum.INPUT, ValueTypeEnum.INTEGER);

            // 设置套餐搭配元素
            if (CollectionUtils.isNotEmpty(packageInfo.getOtherElementTypes())) {
                MultiCheckField packageElementsField = new MultiCheckField();
                packageElementsField.setId(SchemaFieldIdEnum.PACKAGE_ELEMENTS.getId());
                packageElementsField.setName("套餐搭配元素");
                List<Value> values = packageInfo.getOtherElementTypes().stream()
                        .map(String::valueOf)
                        .map(Value::new)
                        .collect(Collectors.toList());
                packageElementsField.setValues(values);
                packageComplexValue.put(packageElementsField);
            }

            // 设置费用包含
            setValueToField(packageComplexValue, SchemaFieldIdEnum.FEE_INCLUDE.getId(), packageInfo.getFeeInclude(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置费用不含
            setValueToField(packageComplexValue, SchemaFieldIdEnum.FEE_EXCLUDE.getId(), packageInfo.getFeeExclude(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置出行人模板
            if (packageInfo.getTravelPersonTemplateId() != null) {
                SingleCheckField travelPersonTemplateIdField = new SingleCheckField();
                travelPersonTemplateIdField.setId(SchemaFieldIdEnum.PACKAGE_TRAVEL_PERSON.getId());
                travelPersonTemplateIdField.setName(SchemaFieldIdEnum.PACKAGE_TRAVEL_PERSON.getName());
                travelPersonTemplateIdField.setValue(new Value(String.valueOf(packageInfo.getTravelPersonTemplateId())));
                packageComplexValue.put(travelPersonTemplateIdField);
            }

            // 设置玩法
            processPlayProp(packageComplexValue, field, packageInfo.getPlayThemeBO());

            // 设置日历价库
            processCalendarPriceStock(packageComplexValue, packageInfo.getPlayCanlendePriceStockControlDO());

            // 处理套餐元素
            processPackageElements(packageComplexValue, packageInfo.getPackageElementsControlDO());

            // 处理套餐直连信息
            processPackageDirectInfo(packageComplexValue, packageInfo.getPlayAbroadPackageDirectInfo());

            packageValues.add(packageComplexValue);
        }
        field.setComplexValues(packageValues);
    }

    private void processPackageDirectInfo(ComplexValue packageComplexValue, com.taobao.gpf.domain.itemdirect.model.PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo) {
        if (playAbroadPackageDirectInfo == null) {
            return;
        }

        ComplexField directInfoField = new ComplexField();
        directInfoField.setId(SchemaFieldIdEnum.PACKAGE_ADD_PRICE_RULER.getId());
        directInfoField.setName(SchemaFieldIdEnum.PACKAGE_ADD_PRICE_RULER.getName());

        ComplexValue directComplexValue = new ComplexValue();

        // spId
        setValueToField(directComplexValue, SchemaFieldIdEnum.SPID.getId(), playAbroadPackageDirectInfo.getSpId(), FieldTypeEnum.INPUT, ValueTypeEnum.LONG);

        // supplierProductCode
        setValueToField(directComplexValue, SchemaFieldIdEnum.SUPPLIER_PRODUCT_CODE.getId(), playAbroadPackageDirectInfo.getSupplierProductCode(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

        // syncType
        setValueToField(directComplexValue, SchemaFieldIdEnum.SYNC_TYPE.getId(), playAbroadPackageDirectInfo.getSyncType(), FieldTypeEnum.INPUT, ValueTypeEnum.INTEGER);

        // crowdCode
        setValueToField(directComplexValue, SchemaFieldIdEnum.CROWD_CODE.getId(), playAbroadPackageDirectInfo.getCrowdCode(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

        // 加价规则
        if (playAbroadPackageDirectInfo.getAddPriceRuler() != null) {
            processAddPriceRule(directComplexValue, SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getId(), SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getName(), playAbroadPackageDirectInfo.getAddPriceRuler());
        }
        directInfoField.setComplexValue(directComplexValue);
        packageComplexValue.put(directInfoField);

    }

    private void processPackageElements(ComplexValue packageComplexValue, PackageElementsControlDO packageElementsControlDO) {
        List<BasePlayElementGroupVO> basePlayElementGroupVOList = playElementBO2VOConvertor.convertElementBO2VO(packageElementsControlDO.getElementInfoList());
        if (CollectionUtils.isEmpty(basePlayElementGroupVOList)) {
            return;
        }

        for (BasePlayElementGroupVO basePlayElementGroupVO : basePlayElementGroupVOList) {
            ElementCategoryEnum elementCategory = ElementCategoryEnum.getByType(basePlayElementGroupVO.getElementCategory());
            if (elementCategory == null) {
                continue;
            }

            switch (elementCategory) {
                case HOTEL:
                    processHotelElement(packageComplexValue, (HotelElementGroupVO) basePlayElementGroupVO);
                    break;
                case TICKET:
                    processTicketElement(packageComplexValue, (TicketElementGroupVO) basePlayElementGroupVO);
                    break;
                case TRAFFIC:
                    processTrafficElement(packageComplexValue, (TrafficElementGroupVO) basePlayElementGroupVO);
                    break;
                case WIFI:
                    processWifiElement(packageComplexValue, (WifiElementGroupVO) basePlayElementGroupVO);
                    break;
                case PHONECARD:
                    processPhoneCardElement(packageComplexValue, (PhoneCardGroupVO) basePlayElementGroupVO);
                    break;
                case SPECIAL_ACTIVITY:
                    processSpecialActivityElement(packageComplexValue, (SpecialActivityElementGroupVO) basePlayElementGroupVO);
                    break;
                case SPA:
                    processSpaElement(packageComplexValue, (SpaElementGroupVO) basePlayElementGroupVO);
                    break;
                case TOUR:
                    processTourElement(packageComplexValue, (LocalTouristGuideElementGroupVO) basePlayElementGroupVO);
                    break;
                case PHOTOGRAPHY:
                    processPhotographyElement(packageComplexValue, (PhotographyElementGroupVO) basePlayElementGroupVO);
                    break;
                case DIVING:
                    processDivingElement(packageComplexValue, (DivingElementGroupVO) basePlayElementGroupVO);
                    break;
                case CATERING:
                    processCateringElement(packageComplexValue, (CateringElementGroupVO) basePlayElementGroupVO);
                    break;
                default:
                    // Handle unknown element categories if necessary
                    break;
            }
        }
    }

    // 酒店元素处理
    private void processHotelElement(ComplexValue packageComplexValue, HotelElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.HOTEL.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 最早入住时间
        if (StringUtils.isNotBlank(vo.getEarliestCheckInTime())) {
            value.put(getInputField(SchemaFieldIdEnum.EARLIEST_CHECK_IN_TIME.getId(), ValueTypeEnum.TEXT, vo.getEarliestCheckInTime()));
        }

        // 最晚退房时间
        if (StringUtils.isNotBlank(vo.getLatestCheckOutTime())) {
            value.put(getInputField(SchemaFieldIdEnum.LATEST_CHECK_OUT_TIME.getId(), ValueTypeEnum.TEXT, vo.getLatestCheckOutTime()));
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        ComplexField hotelField = new ComplexField();
        hotelField.setId(SchemaFieldIdEnum.HOTEL_ELEMENT.getId());
        hotelField.setName(SchemaFieldIdEnum.HOTEL_ELEMENT.getName());
        hotelField.setComplexValue(value);
        packageComplexValue.put(hotelField);
    }

    // 门票元素处理
    private void processTicketElement(ComplexValue packageComplexValue, TicketElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.TICKET.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        ComplexField ticketField = new ComplexField();
        ticketField.setId(SchemaFieldIdEnum.TICKET_ELEMENT.getId());
        ticketField.setName(SchemaFieldIdEnum.TICKET_ELEMENT.getName());
        ticketField.setComplexValue(value);
        packageComplexValue.put(ticketField);
    }

    // 交通元素处理
    private void processTrafficElement(ComplexValue packageComplexValue, TrafficElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.TRAFFIC.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        ComplexField trafficField = new ComplexField();
        trafficField.setId(SchemaFieldIdEnum.TRAFFIC_ELEMENT.getId());
        trafficField.setName(SchemaFieldIdEnum.TRAFFIC_ELEMENT.getName());
        trafficField.setComplexValue(value);
        packageComplexValue.put(trafficField);
    }

    // WiFi元素处理
    private void processWifiElement(ComplexValue packageComplexValue, WifiElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.WIFI.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        ComplexField wifiField = new ComplexField();
        wifiField.setId(SchemaFieldIdEnum.WIFI_ELEMENT.getId());
        wifiField.setName(SchemaFieldIdEnum.WIFI_ELEMENT.getName());
        wifiField.setComplexValue(value);
        packageComplexValue.put(wifiField);
    }

    // 电话卡元素处理
    private void processPhoneCardElement(ComplexValue packageComplexValue, PhoneCardGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.PHONECARD.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        ComplexField phoneCardField = new ComplexField();
        phoneCardField.setId(SchemaFieldIdEnum.PHONECARD_ELEMENT.getId());
        phoneCardField.setName(SchemaFieldIdEnum.PHONECARD_ELEMENT.getName());
        phoneCardField.setComplexValue(value);
        packageComplexValue.put(phoneCardField);
    }

    // 特色活动元素处理
    private void processSpecialActivityElement(ComplexValue packageComplexValue, SpecialActivityElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.SPECIAL_ACTIVITY.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        ComplexField specialActivityField = new ComplexField();
        specialActivityField.setId(SchemaFieldIdEnum.SPECIAL_ACTIVITY_ELEMENT.getId());
        specialActivityField.setName(SchemaFieldIdEnum.SPECIAL_ACTIVITY_ELEMENT.getName());
        specialActivityField.setComplexValue(value);
        packageComplexValue.put(specialActivityField);
    }

    // SPA元素处理
    private void processSpaElement(ComplexValue packageComplexValue, SpaElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.SPA.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // SPA店用途范围
        if (vo.getSpaUsageScope() != null) {
            SingleCheckField field = new SingleCheckField();
            field.setId(SchemaFieldIdEnum.SPA_USAGE_SCOPE.getId());
            field.addValueTypeRule(ValueTypeEnum.INTEGER);
            field.setValue(new Value(String.valueOf(vo.getSpaUsageScope())));
            value.put(field);
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        // 处理玩法
        if (vo.getPlayTheme() != null) {
            processPlayTheme(value, vo.getPlayTheme());
        }

        ComplexField spaField = new ComplexField();
        spaField.setId(SchemaFieldIdEnum.SPA_ELEMENT.getId());
        spaField.setName(SchemaFieldIdEnum.SPA_ELEMENT.getName());
        spaField.setComplexValue(value);
        packageComplexValue.put(spaField);
    }

    // 地陪元素处理
    private void processTourElement(ComplexValue packageComplexValue, LocalTouristGuideElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.TOUR.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 语种
        if (CollectionUtils.isNotEmpty(vo.getLanguage())) {
            MultiCheckField field = new MultiCheckField();
            field.setId(SchemaFieldIdEnum.TOUR_LANGUAGE.getId());
            field.setName(SchemaFieldIdEnum.TOUR_LANGUAGE.getName());
            List<Value> values = vo.getLanguage().stream()
                    .map(Value::new)
                    .collect(Collectors.toList());
            field.setValues(values);
            value.put(field);
        }

        // 数量
        if (vo.getNumberStock() != null) {
            value.put(getInputField(SchemaFieldIdEnum.TOUR_NUMBER_STOCK.getId(), ValueTypeEnum.INTEGER, vo.getNumberStock()));
        }

        // 数量规格类型
        if (vo.getType() != null) {
            SingleCheckField field = new SingleCheckField();
            field.setId(SchemaFieldIdEnum.TOUR_TYPE.getId());
            field.addValueTypeRule(ValueTypeEnum.INTEGER);
            field.setValue(new Value(String.valueOf(vo.getType())));
            value.put(field);
        }

        // 商家编码
        if (StringUtils.isNotBlank(vo.getOuterId())) {
            value.put(getInputField(SchemaFieldIdEnum.TOUR_OUTER_ID.getId(), ValueTypeEnum.TEXT, vo.getOuterId()));
        }

        // 讲解类型
        if (StringUtils.isNotBlank(vo.getBizType())) {
            SingleCheckField field = new SingleCheckField();
            field.setId(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getId());
            field.addValueTypeRule(ValueTypeEnum.TEXT);
            field.setValue(new Value(vo.getBizType()));
            value.put(field);
        }

        // 是否含票
        if (vo.getHasTicket() != null) {
            SingleCheckField field = new SingleCheckField();
            field.setId(SchemaFieldIdEnum.TOUR_HAS_TICKET.getId());
            field.addValueTypeRule(ValueTypeEnum.TEXT);
            field.setValue(new Value(String.valueOf(vo.getHasTicket())));
            value.put(field);
        }

        // 讲师ID
        if (StringUtils.isNotBlank(vo.getRelationLecturerId())) {
            value.put(getInputField(SchemaFieldIdEnum.TOUR_RELATION_LECTURER_ID.getId(), ValueTypeEnum.TEXT, vo.getRelationLecturerId()));
        }

        // 讲师昵称
        if (StringUtils.isNotBlank(vo.getLecturerNickName())) {
            value.put(getInputField(SchemaFieldIdEnum.TOUR_LECTURER_NICK_NAME.getId(), ValueTypeEnum.TEXT, vo.getLecturerNickName()));
        }

        // 讲师名字
        if (StringUtils.isNotBlank(vo.getLecturerRealName())) {
            value.put(getInputField(SchemaFieldIdEnum.TOUR_LECTURER_REAL_NAME.getId(), ValueTypeEnum.TEXT, vo.getLecturerRealName()));
        }

        ComplexField tourField = new ComplexField();
        tourField.setId(SchemaFieldIdEnum.TOUR_ELEMENT.getId());
        tourField.setName(SchemaFieldIdEnum.TOUR_ELEMENT.getName());
        tourField.setComplexValue(value);
        packageComplexValue.put(tourField);
    }

    // 旅拍元素处理
    private void processPhotographyElement(ComplexValue packageComplexValue, PhotographyElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.PHOTOGRAPHY.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 拍摄地点
        if (StringUtils.isNotBlank(vo.getAddress())) {
            value.put(getInputField(SchemaFieldIdEnum.PHOTOGRAPHY_ADDRESS.getId(), ValueTypeEnum.TEXT, vo.getAddress()));
        }

        // 拍摄天数
        if (vo.getDays() != null) {
            value.put(getInputField(SchemaFieldIdEnum.PHOTOGRAPHY_DAYS.getId(), ValueTypeEnum.INTEGER, vo.getDays()));
        }

        // 服装数量
        if (vo.getClothingNumber() != null) {
            value.put(getInputField(SchemaFieldIdEnum.PHOTOGRAPHY_CLOTHING_NUMBER.getId(), ValueTypeEnum.INTEGER, vo.getClothingNumber()));
        }

        // 商家编码
        if (StringUtils.isNotBlank(vo.getOuterId())) {
            value.put(getInputField(SchemaFieldIdEnum.PHOTOGRAPHY_OUTER_ID.getId(), ValueTypeEnum.TEXT, vo.getOuterId()));
        }

        // 拍摄风格
        if (StringUtils.isNotBlank(vo.getPhotographyStyle())) {
            value.put(getInputField(SchemaFieldIdEnum.PHOTOGRAPHY_STYLE.getId(), ValueTypeEnum.TEXT, vo.getPhotographyStyle()));
        }

        // 处理玩法
        if (vo.getPlayTheme() != null) {
            processPlayTheme(value, vo.getPlayTheme());
        }

        ComplexField photographyField = new ComplexField();
        photographyField.setId(SchemaFieldIdEnum.PHOTOGRAPHY_ELEMENT.getId());
        photographyField.setName(SchemaFieldIdEnum.PHOTOGRAPHY_ELEMENT.getName());
        photographyField.setComplexValue(value);
        packageComplexValue.put(photographyField);
    }

    // 潜水元素处理
    private void processDivingElement(ComplexValue packageComplexValue, DivingElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.DIVING.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 潜水次数
        if (vo.getNumberStock() != null) {
            value.put(getInputField(SchemaFieldIdEnum.DIVING_NUMBER_STOCK.getId(), ValueTypeEnum.INTEGER, vo.getNumberStock()));
        }

        // 数量规格类型
        if (vo.getType() != null) {
            SingleCheckField field = new SingleCheckField();
            field.setId(SchemaFieldIdEnum.DIVING_TYPE.getId());
            field.addValueTypeRule(ValueTypeEnum.INTEGER);
            field.setValue(new Value(String.valueOf(vo.getType())));
            value.put(field);
        }

        // 潜水元素列表
        if (CollectionUtils.isNotEmpty(vo.getDivingElementList())) {
            MultiComplexField listField = new MultiComplexField();
            listField.setId(SchemaFieldIdEnum.DIVING_ELEMENT_LIST.getId());
            listField.setName(SchemaFieldIdEnum.DIVING_ELEMENT_LIST.getName());

            List<ComplexValue> elementValues = new ArrayList<>();
            for (DivingElementVO elementVO : vo.getDivingElementList()) {
                ComplexValue elementValue = new ComplexValue();

                // 名称
                if (StringUtils.isNotBlank(elementVO.getLabel())) {
                    value.put(getInputField(SchemaFieldIdEnum.DIVING_ELEMENT_LABEL.getId(), ValueTypeEnum.TEXT, elementVO.getLabel()));
                }

                // POI ID
                if (elementVO.getValue() != null) {
                    value.put(getInputField(SchemaFieldIdEnum.DIVING_ELEMENT_VALUE.getId(), ValueTypeEnum.LONG, elementVO.getValue()));
                }

                // 是否关联POI
                if (elementVO.getPoiRelated() != null) {
                    value.put(getInputField(SchemaFieldIdEnum.DIVING_ELEMENT_POI_RELATED.getId(), ValueTypeEnum.TEXT, elementVO.getPoiRelated()));
                }

                elementValues.add(elementValue);
            }

            listField.setComplexValues(elementValues);
            value.put(listField);
        }

        // 处理玩法
        if (vo.getPlayTheme() != null) {
            processPlayTheme(value, vo.getPlayTheme());
        }

        ComplexField divingField = new ComplexField();
        divingField.setId(SchemaFieldIdEnum.DIVING_ELEMENT.getId());
        divingField.setName(SchemaFieldIdEnum.DIVING_ELEMENT.getName());
        divingField.setComplexValue(value);
        packageComplexValue.put(divingField);
    }

    // 美食元素处理
    private void processCateringElement(ComplexValue packageComplexValue, CateringElementGroupVO vo) {
        ComplexValue value = new ComplexValue();

        // 元素类型
        value.put(getInputField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId(), ValueTypeEnum.INTEGER, ElementCategoryEnum.CATERING.getType()));

        // 补充说明
        if (StringUtils.isNotBlank(vo.getAdditionalRemarks())) {
            value.put(getInputField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId(), ValueTypeEnum.TEXT, vo.getAdditionalRemarks()));
        }

        // 业务类型
        if (vo.getBusinessType() != null) {
            SingleCheckField field = new SingleCheckField();
            field.setId(SchemaFieldIdEnum.CATERING_BUSINESS_TYPE.getId());
            field.addValueTypeRule(ValueTypeEnum.INTEGER);
            field.setValue(new Value(String.valueOf(vo.getBusinessType())));
            value.put(field);
        }

        // POI信息
        if (vo.getPlayPoiVO() != null) {
            ComplexField poiField = new ComplexField();
            poiField.setId(SchemaFieldIdEnum.CATERING_POI.getId());
            poiField.setName(SchemaFieldIdEnum.CATERING_POI.getName());

            ComplexValue poiValue = new ComplexValue();

            // POI ID
            if (vo.getPlayPoiVO().getPoiId() != null) {
                poiValue.put(getInputField(SchemaFieldIdEnum.CATERING_POI_ID.getId(), ValueTypeEnum.LONG, vo.getPlayPoiVO().getPoiId()));
            }

            // POI名称
            if (vo.getPlayPoiVO().getPoiValue() != null && vo.getPlayPoiVO().getPoiValue().get("poiName") != null) {
                poiValue.put(getInputField(SchemaFieldIdEnum.CATERING_POI_NAME.getId(), ValueTypeEnum.TEXT, vo.getPlayPoiVO().getPoiValue().get("poiName")));
            }

            poiField.setComplexValue(poiValue);
            value.put(poiField);
        }

        // 处理元素列表
        if (CollectionUtils.isNotEmpty(vo.getElementValueMapList())) {
            processElementList(value, vo.getElementValueMapList());
        }

        // 处理玩法
        if (vo.getPlayTheme() != null) {
            processPlayTheme(value, vo.getPlayTheme());
        }

        ComplexField cateringField = new ComplexField();
        cateringField.setId(SchemaFieldIdEnum.CATERING_ELEMENT.getId());
        cateringField.setName(SchemaFieldIdEnum.CATERING_ELEMENT.getName());
        cateringField.setComplexValue(value);
        packageComplexValue.put(cateringField);
    }

    // 处理玩法主题
    private void processPlayTheme(ComplexValue parentValue, PlayThemeBO playThemeBO) {
        if (playThemeBO == null) {
            return;
        }

        ComplexField playThemeField = new ComplexField();
        playThemeField.setId(SchemaFieldIdEnum.PLAY_THEME.getId());
        playThemeField.setName(SchemaFieldIdEnum.PLAY_THEME.getName());

        ComplexValue playThemeValue = new ComplexValue();

        if (CollectionUtils.isNotEmpty(playThemeBO.getPlayThemeValue())) {
            MultiComplexField playThemeValueField = new MultiComplexField();
            playThemeValueField.setId(SchemaFieldIdEnum.PLAY_THEME_VALUE.getId());
            playThemeValueField.setName(SchemaFieldIdEnum.PLAY_THEME_VALUE.getName());

            List<ComplexValue> themeValues = new ArrayList<>();
            for (PlayMethodValueModel themeValue : playThemeBO.getPlayThemeValue()) {
                ComplexValue themeComplexValue = new ComplexValue();

                // Key (玩法ID)
                if (StringUtils.isNotBlank(themeValue.getKey())) {
                    themeComplexValue.put(getInputField(SchemaFieldIdEnum.PLAY_THEME_VALUE_KEY.getId(), ValueTypeEnum.LONG, themeValue.getKey()));
                }

                // Tab (玩法名称)
                if (StringUtils.isNotBlank(themeValue.getTab())) {
                    themeComplexValue.put(getInputField(SchemaFieldIdEnum.PLAY_THEME_VALUE_TAB.getId(), ValueTypeEnum.TEXT, themeValue.getTab()));
                }

                // 属性值列表
                if (CollectionUtils.isNotEmpty(themeValue.getProperties())) {
                    MultiComplexField attributeField = new MultiComplexField();
                    attributeField.setId(SchemaFieldIdEnum.PLAY_THEME_ATTRIBUTE.getId());
                    attributeField.setName(SchemaFieldIdEnum.PLAY_THEME_ATTRIBUTE.getName());
                    for (PlayProperty attribute : themeValue.getProperties()) {

                        ComplexValue attributeValue = new ComplexValue();

                        // ID
                        if (attribute.getId() != null) {
                            attributeValue.put(getInputField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_ID.getId(), ValueTypeEnum.LONG, attribute.getId()));
                        }

                        // 名称
                        if (StringUtils.isNotBlank(attribute.getName())) {
                            attributeValue.put(getInputField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_NAME.getId(), ValueTypeEnum.TEXT, attribute.getName()));
                        }

                        // 类型
                        attributeValue.put(getInputField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_TYPE.getId(), ValueTypeEnum.INTEGER, attribute.getType()));
                        /**
                        SingleCheckField typeField = new SingleCheckField();
                        typeField.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_TYPE.getId());
                        typeField.addValueTypeRule(ValueTypeEnum.INTEGER);
                        typeField.setValue(new Value(String.valueOf(attribute.getType())));
                        attributeValue.put(typeField);
                         */

                        // 值
                        if (StringUtils.isNotBlank(attribute.getValue())) {
                            attributeValue.put(getInputField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_VALUE.getId(), ValueTypeEnum.TEXT, attribute.getValue()));
                        }

                        /**
                        if (StringUtils.isNotBlank(attribute.getValue())) {
                            if (attribute.getType() == 2) {
                                // 多选
                                MultiCheckField valueField = new MultiCheckField();
                                valueField.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_VALUE.getId());
                                valueField.addValueTypeRule(ValueTypeEnum.TEXT);

                                // 设置多选值
                                String[] values = StringUtils.split(attribute.getValue(), ",");
                                List<Value> fieldValues = Arrays.stream(values)
                                        .map(Value::new)
                                        .collect(Collectors.toList());
                                valueField.setValues(fieldValues);
                                attributeValue.put(valueField);
                            } else {
                                // 单选
                                SingleCheckField valueField = new SingleCheckField();
                                valueField.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_VALUE.getId());
                                valueField.addValueTypeRule(ValueTypeEnum.TEXT);
                                valueField.setValue(new Value(attribute.getValue()));
                                attributeValue.put(valueField);
                            }
                        }
                         */

                        //是否其他
                        attributeValue.put(getInputField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_IS_OTHER.getId(), ValueTypeEnum.INTEGER, attribute.getIsOther()));
                        /**
                        SingleCheckField isOtherField = new SingleCheckField();
                        isOtherField.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_IS_OTHER.getId());
                        isOtherField.addValueTypeRule(ValueTypeEnum.INTEGER);
                        isOtherField.setValue(new Value(String.valueOf(attribute.getIsOther())));
                        attributeValue.put(isOtherField);
                         */

                        //为其他时的用户自定义value
                        if (attribute.getIsOther() == 1 && StringUtils.isNotBlank(attribute.getOtherVal())) {
                            attributeValue.put(getInputField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_OTHER_VAL.getId(), ValueTypeEnum.TEXT, attribute.getOtherVal()));
                        }

                        attributeField.addComplexValue(attributeValue);
                        themeComplexValue.put(attributeField);
                    }
                }

                themeValues.add(themeComplexValue);
            }

            playThemeValueField.setComplexValues(themeValues);
            playThemeValue.put(playThemeValueField);
        }

        playThemeField.setComplexValue(playThemeValue);
        parentValue.put(playThemeField);
    }

    private void setValueToField(ComplexValue complexValue, String fieldId, Object value, FieldTypeEnum fieldType, ValueTypeEnum valueType) {
        if (value != null) {
            complexValue.put(getInputField(fieldId, valueType, value));
        }
    }

    private void processPlayProp(ComplexValue packageComplexValue, MultiComplexField field, PlayThemeBO playThemeBO) {
        if (playThemeBO == null) {
            return;
        }

        // 设置玩法主题属性
        if (CollectionUtils.isNotEmpty(playThemeBO.getPlayThemeProps())) {
            MultiCheckField playMethodField = new MultiCheckField();
            playMethodField.setId(SchemaFieldIdEnum.PLAY_METHOD.getId());
            playMethodField.setName(SchemaFieldIdEnum.PLAY_METHOD.getName());
            List<Value> values = playThemeBO.getPlayThemeProps().stream()
                    .flatMap(List::stream)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .map(Value::new)
                    .collect(Collectors.toList());
            playMethodField.setValues(values);
            packageComplexValue.put(playMethodField);
        }

        if (CollectionUtils.isNotEmpty(playThemeBO.getPlayThemeValue())) {
            Field playMethodPropField = field.getFieldMap().get(SchemaFieldIdEnum.PLAY_METHOD_PROP.getId());
            Field playMethodProp = Json2FieldVistor.parseJson(playMethodPropField, JacksonUtil.valueToTree(playThemeBO.getPlayThemeValue()));

            if (playMethodProp instanceof MultiComplexField) {
                packageComplexValue.put(playMethodProp);
            } else {
                LogUtil.sysErrorLog("OverseaPlayPackageInfoRenderFieldValue.processPlayProp", "playMethodProp is not MultiComplexField: " + JSON.toJSONString(playMethodProp));
            }

        }
    }

    private void processCalendarPriceStock(ComplexValue packageComplexValue, List<PlaySkuPriceStockControlDO> playSkuPriceStockList) {
        if (CollectionUtils.isEmpty(playSkuPriceStockList)) {
            return;
        }

        MultiComplexField calendarPriceStockField = new MultiComplexField();
        calendarPriceStockField.setId("calendarPriceStock");
        calendarPriceStockField.setName("日历价库");

        List<ComplexValue> calendarValues = new ArrayList<>();
        for (PlaySkuPriceStockControlDO skuPriceStock : playSkuPriceStockList) {
            ComplexValue calendarComplexValue = new ComplexValue();

            // 设置skuId
            setValueToField(calendarComplexValue, SchemaFieldIdEnum.SKUID.getId(), skuPriceStock.getSkuId(), FieldTypeEnum.INPUT, ValueTypeEnum.LONG);

            // 设置价格类型
            setValueToField(calendarComplexValue, SchemaFieldIdEnum.PRICE_TYPE.getId(), skuPriceStock.getPriceType(), FieldTypeEnum.SINGLECHECK, ValueTypeEnum.INTEGER);

            // 设置币种
            setValueToField(calendarComplexValue, SchemaFieldIdEnum.CURRENCY.getId(), skuPriceStock.getCurrency(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

            // 设置不可用日期
            if (CollectionUtils.isNotEmpty(skuPriceStock.getUnavailableDate())) {
                calendarComplexValue.put(getInputField(SchemaFieldIdEnum.UNAVAILABLE_DATE.getId(), ValueTypeEnum.TEXT, StringUtils.join(skuPriceStock.getUnavailableDate(), ",")));
            }

            // 设置直连配置
            if (skuPriceStock.getPlayAbroadPackageSkuDirectInfo() != null) {
                ComplexField directInfoField = new ComplexField();
                directInfoField.setId(SchemaFieldIdEnum.DIRECT_INFO.getId());
                directInfoField.setName(SchemaFieldIdEnum.DIRECT_INFO.getName());
                ComplexValue directComplexValue = new ComplexValue();
                setValueToField(directComplexValue, SchemaFieldIdEnum.SPID.getId(), skuPriceStock.getPlayAbroadPackageSkuDirectInfo().getSpId(), FieldTypeEnum.INPUT, ValueTypeEnum.LONG);
                setValueToField(directComplexValue, SchemaFieldIdEnum.SUPPLIER_PRODUCT_CODE.getId(), skuPriceStock.getPlayAbroadPackageSkuDirectInfo().getSupplierProductCode(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);
                setValueToField(directComplexValue, SchemaFieldIdEnum.SYNC_TYPE.getId(), skuPriceStock.getPlayAbroadPackageSkuDirectInfo().getSyncType(), FieldTypeEnum.INPUT, ValueTypeEnum.INTEGER);
                setValueToField(directComplexValue, SchemaFieldIdEnum.CROWD_CODE.getId(), skuPriceStock.getPlayAbroadPackageSkuDirectInfo().getCrowdCode(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);

                // SKU 加价规则
                if (skuPriceStock.getPlayAbroadPackageSkuDirectInfo().getAddPriceRuler() != null) {
                    processAddPriceRule(directComplexValue, SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getId(), SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getName(), skuPriceStock.getPlayAbroadPackageSkuDirectInfo().getAddPriceRuler());
                }

                directInfoField.setComplexValue(directComplexValue);
                calendarComplexValue.put(directInfoField);
            }

            // 设置日历价库数据
            if (CollectionUtils.isNotEmpty(skuPriceStock.getCalendarPriceStock())) {
                MultiComplexField calendarDataField = new MultiComplexField();
                calendarDataField.setId(SchemaFieldIdEnum.CALENDAR_PRICE_STOCK_DATA.getId());
                calendarDataField.setName(SchemaFieldIdEnum.CALENDAR_PRICE_STOCK_DATA.getName());

                List<ComplexValue> calendarDataValues = new ArrayList<>();
                for (CalendarPriceStockBO calendarData : skuPriceStock.getCalendarPriceStock()) {
                    ComplexValue calendarDataValue = new ComplexValue();
                    setValueToField(calendarDataValue, SchemaFieldIdEnum.DATE.getId(), calendarData.getDate(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);
                    setValueToField(calendarDataValue, SchemaFieldIdEnum.PRICE.getId(), calendarData.getPrice(), FieldTypeEnum.INPUT, ValueTypeEnum.DOUBLE);
                    setValueToField(calendarDataValue, SchemaFieldIdEnum.STOCK.getId(), calendarData.getStock(), FieldTypeEnum.INPUT, ValueTypeEnum.LONG);
                    setValueToField(calendarDataValue, SchemaFieldIdEnum.OUTER_CODE.getId(), calendarData.getOuterCode(), FieldTypeEnum.INPUT, ValueTypeEnum.TEXT);
                    calendarDataValues.add(calendarDataValue);
                }
                calendarDataField.setComplexValues(calendarDataValues);
                calendarComplexValue.put(calendarDataField);
            }

            calendarValues.add(calendarComplexValue);
        }
        calendarPriceStockField.setComplexValues(calendarValues);
        packageComplexValue.put(calendarPriceStockField);
    }

    // 通用元素列表处理
    private <T extends BaseElementValueMapVO> void processElementList(ComplexValue parentValue, List<ElementVO<T>> elementList) {
        if (CollectionUtils.isEmpty(elementList)) {
            return;
        }

        MultiComplexField elementsField = new MultiComplexField();
        elementsField.setId(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        elementsField.setName(SchemaFieldIdEnum.ELEMENT_LIST.getName());

        List<ComplexValue> elementValues = new ArrayList<>();
        for (ElementVO<T> elementVO : elementList) {
            ComplexValue elementValue = new ComplexValue();

            // 元素ID
            if (elementVO.getId() != null) {
                elementValue.put(getInputField(SchemaFieldIdEnum.ELEMENT_ID.getId(), ValueTypeEnum.LONG, elementVO.getId()));
            }

            // 元素Type
            if (elementVO.getElementType() != null) {
                elementValue.put(getInputField(SchemaFieldIdEnum.ELEMENT_TYPE.getId(), ValueTypeEnum.INTEGER, elementVO.getElementType()));
            }

            // 元素特征
            if (elementVO.getValueMap() != null) {
                MultiComplexField featuresField = new MultiComplexField();
                featuresField.setId(SchemaFieldIdEnum.ELEMENT_FEATURES.getId());
                featuresField.setName(SchemaFieldIdEnum.ELEMENT_FEATURES.getName());

                List<ComplexValue> featureValues = new ArrayList<>();

                // 处理通用字段为特征
                T valueMap = elementVO.getValueMap();

                if (StringUtils.isNotBlank(valueMap.getName())) {
                    ComplexValue featureValue = new ComplexValue();
                    featureValue.put(getFeatureKeyInputField("NAME"));
                    featureValue.put(getFeatureValueInputField(valueMap.getName()));
                    featureValues.add(featureValue);
                }

                if (StringUtils.isNotBlank(valueMap.getDesc())) {
                    ComplexValue featureValue = new ComplexValue();
                    featureValue.put(getFeatureKeyInputField("DESC"));
                    featureValue.put(getFeatureValueInputField(valueMap.getDesc()));
                    featureValues.add(featureValue);
                }

                if (StringUtils.isNotBlank(valueMap.getOuterId())) {
                    ComplexValue featureValue = new ComplexValue();
                    featureValue.put(getFeatureKeyInputField("OUTER_ID"));
                    featureValue.put(getFeatureValueInputField(valueMap.getOuterId()));
                    featureValues.add(featureValue);
                }

                //POIID
                if (StringUtils.isNotBlank(valueMap.getPoiId())) {
                    ComplexValue featureValue = new ComplexValue();
                    featureValue.put(getFeatureKeyInputField("POI_ID"));
                    featureValue.put(getFeatureValueInputField(valueMap.getPoiId()));
                    featureValues.add(featureValue);
                }

                //POI_RELATED
                if (StringUtils.isNotBlank(valueMap.getPoiRelated())) {
                    ComplexValue featureValue = new ComplexValue();
                    featureValue.put(getFeatureKeyInputField("POI_RELATED"));
                    featureValue.put(getFeatureValueInputField(valueMap.getPoiRelated()));
                    featureValues.add(featureValue);
                }

                //ELEMENT_TYPE
                if (StringUtils.isNotBlank(valueMap.getElementType())) {
                    ComplexValue featureValue = new ComplexValue();
                    featureValue.put(getFeatureKeyInputField("ELEMENT_TYPE"));
                    featureValue.put(getFeatureValueInputField(String.valueOf(elementVO.getElementType())));
                    featureValues.add(featureValue);
                }

                //元素特有的feature
                if (valueMap instanceof HotelElementValueMapVO) {
                    processHotelElementValueFeature((HotelElementValueMapVO) valueMap, featureValues);
                } else if(valueMap instanceof TicketElementValueMapVO) {
                    processTicketElementValueFeature((TicketElementValueMapVO) valueMap, featureValues);
                } else if(valueMap instanceof PhoneCardValueMapVO){
                    processPhoneCardElementValueFeature((PhoneCardValueMapVO) valueMap, featureValues);
                } else if(valueMap instanceof SpecialActivityValueMapVO) {
                    processSpecialActivityElementValueFeature((SpecialActivityValueMapVO) valueMap, featureValues);
                } else if (valueMap instanceof SpaValueMapVO) {
                    processSpaElementValueFeature((SpaValueMapVO) valueMap, featureValues);
                } else if (valueMap instanceof CateringValueMapVO) {
                    processCateringElementValueFeature((CateringValueMapVO) valueMap, featureValues);
                }

                featuresField.setComplexValues(featureValues);
                elementValue.put(featuresField);
            }

            elementValues.add(elementValue);
        }

        elementsField.setComplexValues(elementValues);
        parentValue.put(elementsField);
    }

    private void processCateringElementValueFeature(CateringValueMapVO valueMap, List<ComplexValue> featureValues) {
        //POI_NAME
        if (StringUtils.isNotBlank(valueMap.getPoiName())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("POI_NAME"));
            featureValue.put(getFeatureValueInputField(valueMap.getPoiName()));
            featureValues.add(featureValue);
        }
        //numberStock
        if (valueMap.getNumberStock() != null) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("numberStock"));
            featureValue.put(getFeatureValueInputField(String.valueOf(valueMap.getNumberStock())));
            featureValues.add(featureValue);
        }
    }

    private void processSpaElementValueFeature(SpaValueMapVO valueMap, List<ComplexValue> featureValues) {
        //POI_NAME
        if (StringUtils.isNotBlank(valueMap.getPoiName())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("POI_NAME"));
            featureValue.put(getFeatureValueInputField(valueMap.getPoiName()));
            featureValues.add(featureValue);
        }
        //CITY
        if (StringUtils.isNotBlank(valueMap.getCity())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("CITY"));
            featureValue.put(getFeatureValueInputField(valueMap.getCity()));
            featureValues.add(featureValue);
        }
        //numberStock
        if (valueMap.getNumberStock() != null) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("numberStock"));
            featureValue.put(getFeatureValueInputField(String.valueOf(valueMap.getNumberStock())));
            featureValues.add(featureValue);
        }
    }

    private void processSpecialActivityElementValueFeature(SpecialActivityValueMapVO valueMap, List<ComplexValue> featureValues) {
        //numberStock
        if (valueMap.getNumberStock() != null) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("numberStock"));
            featureValue.put(getFeatureValueInputField(String.valueOf(valueMap.getNumberStock())));
            featureValues.add(featureValue);
        }
        //activityType
        if (valueMap.getActivityType() != null) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("activityType"));
            featureValue.put(getFeatureValueInputField(String.valueOf(valueMap.getActivityType())));
            featureValues.add(featureValue);
        }
        //ACTIVITY_CATEGORY_PATH_IDS
        if (StringUtils.isNotBlank(valueMap.getActivityCategoryPathIds())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("ACTIVITY_CATEGORY_PATH_IDS"));
            featureValue.put(getFeatureValueInputField(valueMap.getActivityCategoryPathIds()));
            featureValues.add(featureValue);
        }
    }

    private void processPhoneCardElementValueFeature(PhoneCardValueMapVO valueMap, List<ComplexValue> featureValues) {
        //SIM_NET
        if (StringUtils.isNotBlank(valueMap.getSimNet())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("SIM_NET"));
            featureValue.put(getFeatureValueInputField(valueMap.getSimNet()));
            featureValues.add(featureValue);
        }
        //SIZE
        if (StringUtils.isNotBlank(valueMap.getSize())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("SIZE"));
            featureValue.put(getFeatureValueInputField(valueMap.getSize()));
            featureValues.add(featureValue);
        }
    }

    private void processTicketElementValueFeature(TicketElementValueMapVO valueMap, List<ComplexValue> featureValues) {
        //SCENIC_PRODUCT_ID
        if (StringUtils.isNotBlank(valueMap.getScenicProductId())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("SCENIC_PRODUCT_ID"));
            featureValue.put(getFeatureValueInputField(valueMap.getScenicProductId()));
            featureValues.add(featureValue);
        }
        //TICKET_KIND_VID
        if (StringUtils.isNotBlank(valueMap.getTicketKindVid())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("TICKET_KIND_VID"));
            featureValue.put(getFeatureValueInputField(valueMap.getTicketKindVid()));
            featureValues.add(featureValue);
        }
        //AREA_NAME
        if (StringUtils.isNotBlank(valueMap.getAreaName())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("AREA_NAME"));
            featureValue.put(getFeatureValueInputField(valueMap.getAreaName()));
            featureValues.add(featureValue);
        }
        //AREA_PV
        if (StringUtils.isNotBlank(valueMap.getAreaPv())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("AREA_PV"));
            featureValue.put(getFeatureValueInputField(valueMap.getAreaPv()));
            featureValues.add(featureValue);
        }
        //SCENIC_ID
        if (StringUtils.isNotBlank(valueMap.getScenicId())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("SCENIC_ID"));
            featureValue.put(getFeatureValueInputField(valueMap.getScenicId()));
            featureValues.add(featureValue);
        }
        //PRODUCT_NAME
        if (StringUtils.isNotBlank(valueMap.getProductName())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("PRODUCT_NAME"));
            featureValue.put(getFeatureValueInputField(valueMap.getProductName()));
            featureValues.add(featureValue);
        }
        //EPISODE_NAME
        if (StringUtils.isNotBlank(valueMap.getEpisodeName())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("EPISODE_NAME"));
            featureValue.put(getFeatureValueInputField(valueMap.getEpisodeName()));
            featureValues.add(featureValue);
        }
        //CITY
        if (StringUtils.isNotBlank(valueMap.getCity())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("CITY"));
            featureValue.put(getFeatureValueInputField(valueMap.getCity()));
            featureValues.add(featureValue);
        }
        //divisionId
        if (StringUtils.isNotBlank(valueMap.getDivisionId())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("divisionId"));
            featureValue.put(getFeatureValueInputField(valueMap.getDivisionId()));
            featureValues.add(featureValue);
        }
        //EPISODE_PV
        if (StringUtils.isNotBlank(valueMap.getEpisodePv())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("EPISODE_PV"));
            featureValue.put(getFeatureValueInputField(valueMap.getEpisodePv()));
            featureValues.add(featureValue);
        }
        //TICKET_KIND_PV
        if (StringUtils.isNotBlank(valueMap.getTicketKindPv())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("TICKET_KIND_PV"));
            featureValue.put(getFeatureValueInputField(valueMap.getTicketKindPv()));
            featureValues.add(featureValue);
        }
        //numberStock
        if (StringUtils.isNotBlank(valueMap.getNumberStock())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("numberStock"));
            featureValue.put(getFeatureValueInputField(valueMap.getNumberStock()));
            featureValues.add(featureValue);
        }

    }

    private static void processHotelElementValueFeature(HotelElementValueMapVO hotelElementValueMapVO, List<ComplexValue> featureValues) {
        //CITY
        if (StringUtils.isNotBlank(hotelElementValueMapVO.getCity())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("CITY"));
            featureValue.put(getFeatureValueInputField(hotelElementValueMapVO.getCity()));
            featureValues.add(featureValue);
        }

        //ROOM_TYPE_ID
        if (StringUtils.isNotBlank(hotelElementValueMapVO.getRoomTypeId())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("ROOM_TYPE_ID"));
            featureValue.put(getFeatureValueInputField(hotelElementValueMapVO.getRoomTypeId()));
            featureValues.add(featureValue);
        }

        //SHID
        if (StringUtils.isNotBlank(hotelElementValueMapVO.getShId())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("SHID"));
            featureValue.put(getFeatureValueInputField(hotelElementValueMapVO.getShId()));
            featureValues.add(featureValue);
        }

        //DIVISION_ID
        if (StringUtils.isNotBlank(hotelElementValueMapVO.getDivisionId())) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("DIVISION_ID"));
            featureValue.put(getFeatureValueInputField(hotelElementValueMapVO.getDivisionId()));
            featureValues.add(featureValue);
        }

        //numberStock
        if (hotelElementValueMapVO.getNumberStock() != null) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("numberStock"));
            featureValue.put(getFeatureValueInputField(String.valueOf(hotelElementValueMapVO.getNumberStock())));
            featureValues.add(featureValue);
        }

        //nightNumber
        if (hotelElementValueMapVO.getNightNumber() != null) {
            ComplexValue featureValue = new ComplexValue();
            featureValue.put(getFeatureKeyInputField("nightNumber"));
            featureValue.put(getFeatureValueInputField(String.valueOf(hotelElementValueMapVO.getNightNumber())));
            featureValues.add(featureValue);
        }

    }

    @NotNull
    private static InputField getFeatureKeyInputField(String value) {
        return getInputField(SchemaFieldIdEnum.FEATURE_KEY.getId(), ValueTypeEnum.TEXT, value);
    }

    @NotNull
    private static InputField getFeatureValueInputField(String value) {
        return getInputField(SchemaFieldIdEnum.FEATURE_VALUE.getId(), ValueTypeEnum.TEXT, value);
    }

    @NotNull
    private static InputField getInputField(String fieldId, ValueTypeEnum valueTypeEnum, Object value) {
        InputField keyField = new InputField();
        keyField.setId(fieldId);
        keyField.addValueTypeRule(valueTypeEnum);
        keyField.setValue(new Value(String.valueOf(value)));
        return keyField;
    }

    @NotNull
    private static SingleCheckField getSingleCheckField(String fieldId, ValueTypeEnum valueTypeEnum, Object value) {
        SingleCheckField field = new SingleCheckField();
        field.setId(fieldId);
        field.addValueTypeRule(valueTypeEnum);
        field.setValue(new Value(String.valueOf(value)));
        return field;
    }

    @NotNull
    private static MultiInputField getMultiInputField(String fieldId, ValueTypeEnum valueTypeEnum, List<Integer> values) {
        MultiInputField field = new MultiInputField();
        field.setId(fieldId);
        field.addValueTypeRule(valueTypeEnum);
        if (CollectionUtils.isNotEmpty(values)) {
            field.setValues(values.stream().map(String::valueOf).collect(Collectors.toList()));
        }
        return field;
    }

    private void processAddPriceRule(ComplexValue parentComplexValue, String fieldId, String fieldName, com.taobao.gpf.domain.itemdirect.model.AddPriceRuler addPriceRuler) {
        ComplexField addPriceRulerField = new ComplexField();
        addPriceRulerField.setId(fieldId);
        addPriceRulerField.setName(fieldName);

        ComplexValue addPriceRulerComplexValue = new ComplexValue();

        // Type
        if (addPriceRuler.getType() != null) {
            addPriceRulerComplexValue.put(getSingleCheckField(SchemaFieldIdEnum.PRICE_RULE_TYPE.getId(), ValueTypeEnum.INTEGER, addPriceRuler.getType()));
        }

        // Value
        if (StringUtils.isNotBlank(addPriceRuler.getValue())) {
            addPriceRulerComplexValue.put(getInputField(SchemaFieldIdEnum.ADD_PRICE_RULE_MAIN_VALUE.getId(), ValueTypeEnum.DOUBLE, addPriceRuler.getValue()));
        }

        // SpecialRule
        if (CollectionUtils.isNotEmpty(addPriceRuler.getSpecialRule())) {
            MultiComplexField specialRuleField = new MultiComplexField();
            specialRuleField.setId(SchemaFieldIdEnum.SPECIAL_RULE.getId());
            specialRuleField.setName(SchemaFieldIdEnum.SPECIAL_RULE.getName());

            List<ComplexValue> specialRuleValues = new ArrayList<>();
            for (com.taobao.gpf.domain.itemdirect.model.SpecialRuleDTO specialRuleDTO : addPriceRuler.getSpecialRule()) {
                ComplexValue specialRuleComplexValue = new ComplexValue();

                // Type
                if (specialRuleDTO.getType() != null) {
                    specialRuleComplexValue.put(getSingleCheckField(SchemaFieldIdEnum.PRICE_RULE_TYPE.getId(), ValueTypeEnum.INTEGER, specialRuleDTO.getType()));
                }

                // Value
                if (StringUtils.isNotBlank(specialRuleDTO.getValue())) {
                    specialRuleComplexValue.put(getInputField(SchemaFieldIdEnum.ADD_PRICE_RULE_SPECIAL_DETAIL_VALUE.getId(), ValueTypeEnum.DOUBLE, specialRuleDTO.getValue()));
                }

                // Start Date
                if (specialRuleDTO.getStart() != null) {
                    specialRuleComplexValue.put(getInputField(SchemaFieldIdEnum.START_DATE.getId(), ValueTypeEnum.LONG, specialRuleDTO.getStart().getTime()));
                }

                // End Date
                if (specialRuleDTO.getEnd() != null) {
                    specialRuleComplexValue.put(getInputField(SchemaFieldIdEnum.END_DATE.getId(), ValueTypeEnum.LONG, specialRuleDTO.getEnd().getTime()));
                }

                // Available Week Days
                if (CollectionUtils.isNotEmpty(specialRuleDTO.getAvailableWeedDays())) {
                    specialRuleComplexValue.put(getMultiInputField(SchemaFieldIdEnum.AVAILABLE_WEEK_DAYS.getId(), ValueTypeEnum.INTEGER, specialRuleDTO.getAvailableWeedDays()));
                }

                specialRuleValues.add(specialRuleComplexValue);
            }
            specialRuleField.setComplexValues(specialRuleValues);
            addPriceRulerComplexValue.put(specialRuleField);
        }

        addPriceRulerField.setComplexValue(addPriceRulerComplexValue);
        parentComplexValue.put(addPriceRulerField);
    }

}
