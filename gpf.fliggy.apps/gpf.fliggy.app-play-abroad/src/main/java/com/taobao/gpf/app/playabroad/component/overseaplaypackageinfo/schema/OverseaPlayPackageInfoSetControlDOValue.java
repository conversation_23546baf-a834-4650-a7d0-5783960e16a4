package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.schema;

import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.fliggy.travel.guide.param.playtype.PlayType;
import com.google.common.collect.Lists;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.convert.vo2bo.PlayElementVO2BOConvert;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.BasePlayElementGroupBO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo.*;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementPlayThemeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementTypeEnum;
import com.taobao.gpf.app.playabroad.model.CalendarPriceStockBO;
import com.taobao.gpf.domain.itemdirect.model.AddPriceRuler;
import com.taobao.gpf.domain.itemdirect.model.PlayAbroadPackageDirectInfo;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodValueModel;
import com.taobao.gpf.domain.component.playmethod.model.PlayProperty;
import com.taobao.gpf.domain.constant.schema.SchemaFieldIdEnum;
import com.taobao.gpf.domain.itemdirect.model.SpecialRuleDTO;
import com.taobao.gpf.domain.publish.schemastrategy.controldo.ISetControlDOValueSchema;
import com.taobao.gpf.domain.publish.schemastrategy.model.ControlValue;
import com.taobao.gpf.domain.repository.PlayTypeHelpServiceRepo;
import com.taobao.top.schema.field.*;
import com.taobao.top.schema.value.ComplexValue;
import com.taobao.top.schema.value.Value;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 境外玩乐套餐组件设置ControlDO值的实现类
 *
 * <AUTHOR>
 */
@Component
public class OverseaPlayPackageInfoSetControlDOValue implements ISetControlDOValueSchema<MultiComplexField, ParentCompDO<AbstractCompDO>> {

    @Autowired
    private PlayElementVO2BOConvert prayElementVO2BOConvert;

    @Autowired
    private PlayTypeHelpServiceRepo playTypeHelpServiceRepo;

    @Override
    public ControlValue transfer(ParentCompDO<AbstractCompDO> compDO, MultiComplexField field, CompExtParam param, AdapterCompConfig compConfig,
                                 SchemaParseContext context) {
        if (CollectionUtils.isEmpty(field.getComplexValues())) {
            return null;
        }

        // 创建controlDO对象
        OverseaPlayPackageInfoListControlDO listControlDO = new OverseaPlayPackageInfoListControlDO();
        List<OverseaPlayPackageInfoControlDO> packageInfos = new ArrayList<>();

        // 遍历处理每个套餐数据
        for (ComplexValue complexValue : field.getComplexValues()) {
            OverseaPlayPackageInfoControlDO packageInfoControlDO = new OverseaPlayPackageInfoControlDO();

            //获取套餐id
            Field packageIdField = complexValue.getValueField(SchemaFieldIdEnum.PACKAGE_ID.getId());
            if (packageIdField instanceof InputField && ((InputField) packageIdField).getValue() != null) {
                packageInfoControlDO.setPackageId(Long.valueOf(((InputField) packageIdField).getValue().getValue()));
            }

            // 获取套餐名称
            Field packageNameField = complexValue.getValueField(SchemaFieldIdEnum.PACKAGE_NAME.getId());
            if (packageNameField instanceof InputField && ((InputField) packageNameField).getValue() != null) {
                packageInfoControlDO.setPackageName(((InputField) packageNameField).getValue().getValue());
            }

            // 获取商家编码
            Field outIdField = complexValue.getValueField(SchemaFieldIdEnum.OUTID.getId());
            if (outIdField instanceof InputField && ((InputField) outIdField).getValue() != null) {
                packageInfoControlDO.setOutId(((InputField) outIdField).getValue().getValue());
            }

            // 获取套餐使用说明
            Field packageDescField = complexValue.getValueField(SchemaFieldIdEnum.PACKAGE_DESC.getId());
            if (packageDescField instanceof InputField && ((InputField) packageDescField).getValue() != null) {
                packageInfoControlDO.setPackageDesc(((InputField) packageDescField).getValue().getValue());
            }

            // 获取套餐价格
            Field packagePriceField = complexValue.getValueField(SchemaFieldIdEnum.MAN_PRICE.getId());
            if (packagePriceField instanceof InputField && ((InputField) packagePriceField).getValue() != null) {
                packageInfoControlDO.setPackagePrice(((InputField) packagePriceField).getValue().getValue());
            }

            // 获取套餐库存
            Field packageStockField = complexValue.getValueField(SchemaFieldIdEnum.MAN_STOCK.getId());
            if (packageStockField instanceof InputField && ((InputField) packageStockField).getValue() != null) {
                String stockValue = ((InputField) packageStockField).getValue().getValue();
                if (StringUtils.isNotBlank(stockValue)) {
                    packageInfoControlDO.setPackageStock(Integer.valueOf(stockValue));
                }
            }

            // 获取活动时长
            Field activityDurationField = complexValue.getValueField(SchemaFieldIdEnum.ACTIVITY_TIME.getId());
            if (activityDurationField instanceof InputField && ((InputField) activityDurationField).getValue() != null) {
                String activityHourValue = ((InputField) activityDurationField).getValue().getValue();
                if (StringUtils.isNotBlank(activityHourValue) && NumberUtils.isCreatable(activityHourValue)) {
                    packageInfoControlDO.setActivityHour(Integer.valueOf(activityHourValue));
                }
            }

            //主元素
            BasicCompDO<Integer> dependCompDO = param.getDependCompDO(CompConstants.MAIN_ELEMENT_COMP_NAME);
            if (dependCompDO != null && dependCompDO.getValue() != null) {
                packageInfoControlDO.setMainElementType(dependCompDO.getValue());
            }

            // 获取套餐搭配元素
            Field packageElementsField = complexValue.getValueField(SchemaFieldIdEnum.PACKAGE_ELEMENTS.getId());
            if (packageElementsField instanceof MultiCheckField && ((MultiCheckField) packageElementsField).getValues() != null) {
                List<Integer> otherElements = ((MultiCheckField) packageElementsField).getValues().stream()
                        .map(Value::getValue)
                        .filter(NumberUtils::isCreatable)
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());

                if (dependCompDO != null && dependCompDO.getValue() != null) {
                    otherElements.remove(dependCompDO.getValue());
                }

                packageInfoControlDO.setOtherElementTypes(otherElements);
            }

            // 获取费用包含
            Field feeIncludeField = complexValue.getValueField(SchemaFieldIdEnum.FEE_INCLUDE.getId());
            if (feeIncludeField instanceof InputField && ((InputField) feeIncludeField).getValue() != null) {
                packageInfoControlDO.setFeeInclude(((InputField) feeIncludeField).getValue().getValue());
            }

            // 获取费用不含
            Field feeExcludeField = complexValue.getValueField(SchemaFieldIdEnum.FEE_EXCLUDE.getId());
            if (feeExcludeField instanceof InputField && ((InputField) feeExcludeField).getValue() != null) {
                packageInfoControlDO.setFeeExclude(((InputField) feeExcludeField).getValue().getValue());
            }

            //出行人模板
            Field packageTravelPersonTemplateField = complexValue.getValueField(SchemaFieldIdEnum.PACKAGE_TRAVEL_PERSON.getId());
            if (packageTravelPersonTemplateField instanceof SingleCheckField && ((SingleCheckField) packageTravelPersonTemplateField).getValue() != null) {
                String templateIdValue = ((SingleCheckField) packageTravelPersonTemplateField).getValue().getValue();
                if (StringUtils.isNotBlank(templateIdValue) && NumberUtils.isCreatable(templateIdValue)) {
                    packageInfoControlDO.setTravelPersonTemplateId(Long.valueOf(templateIdValue));
                }
            }

            PlayThemeBO playThemeBO = new PlayThemeBO();
            packageInfoControlDO.setPlayThemeBO(playThemeBO);
            //玩法
            processPlayProp(complexValue, playThemeBO, param);

            //日历价库
            processCalendarPriceStock(complexValue, packageInfoControlDO);

            //套餐维度的直连信息配置
            processPlayAbroadPackageDirectInfo(complexValue, packageInfoControlDO);

            //套餐元素
            processPackageElements(complexValue, packageInfoControlDO);

            packageInfos.add(packageInfoControlDO);
        }

        listControlDO.setPackageInfos(packageInfos);

        // 返回控制值对象
        ControlValue controlValue = new ControlValue();
        controlValue.setControlDO(listControlDO);
        //这里默认塞一个object，实际没有用到，防止套餐required校验报错
        controlValue.setValue(new Object());
        return controlValue;
    }

    private void processPackageElements(ComplexValue complexValue, OverseaPlayPackageInfoControlDO packageInfoControlDO) {
        Integer mainElementType = packageInfoControlDO.getMainElementType();
        List<BasePlayElementGroupVO> elementInfoList = buildElementInfoList(complexValue, mainElementType);

        try {
            if (packageInfoControlDO.getPackageElementsControlDO() == null) {
                packageInfoControlDO.setPackageElementsControlDO(new PackageElementsControlDO());
            }
            List<BasePlayElementGroupBO> playElementGroupBOList = prayElementVO2BOConvert.convert(elementInfoList);
            packageInfoControlDO.getPackageElementsControlDO().setElementInfoList(playElementGroupBOList);
        } catch (IOException e) {
            LogUtil.sysErrorLog("OverseaPlayPackageInfoSetControlDOValue setPackageElements excepton;", e, e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private List<BasePlayElementGroupVO> buildElementInfoList(ComplexValue complexValue, Integer mainElementType) {
        List<BasePlayElementGroupVO> elementInfoList = new ArrayList<>();
        if (complexValue == null) {
            return elementInfoList;
        }
        Field packageElementInfoField = complexValue.getValueField(SchemaFieldIdEnum.PACKAGE_ELEMENT_INFO.getId());

        if (!(packageElementInfoField instanceof ComplexField)) {
            return elementInfoList;
        }
        ComplexField packageElementInfoComplexField = (ComplexField) packageElementInfoField;
        ComplexValue packageElementInfoComplexFieldComplexValue = packageElementInfoComplexField.getComplexValue();
        parseHotelElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseTicketElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseTrafficElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseWifiElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parsePhoneCardElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseSpecialActivityElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseSpaElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseTourElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parsePhotographyElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseDivingElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);
        parseCateringElement(packageElementInfoComplexFieldComplexValue, elementInfoList, mainElementType);

        return elementInfoList;
    }

    private void parseHotelElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field hotelField = complexValue.getValueField(SchemaFieldIdEnum.HOTEL_ELEMENT.getId());
        if (!(hotelField instanceof ComplexField)) {
            return;
        }

        ComplexField hotelComplexField = (ComplexField) hotelField;
        ComplexValue hotelValue = hotelComplexField.getComplexValue();
        if (hotelValue == null) {
            return;
        }

        HotelElementGroupVO hotelVO = new HotelElementGroupVO();

        Field elementCategoryField = hotelValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                hotelVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        if (ElementCategoryEnum.HOTEL.getType().equals(mainElementType)) {
            hotelVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            hotelVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = hotelValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            hotelVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field checkInTimeField = hotelValue.getValueField(SchemaFieldIdEnum.EARLIEST_CHECK_IN_TIME.getId());
        if (checkInTimeField instanceof InputField && ((InputField) checkInTimeField).getValue() != null) {
            hotelVO.setEarliestCheckInTime(((InputField) checkInTimeField).getValue().getValue());
        }

        Field checkOutTimeField = hotelValue.getValueField(SchemaFieldIdEnum.LATEST_CHECK_OUT_TIME.getId());
        if (checkOutTimeField instanceof InputField && ((InputField) checkOutTimeField).getValue() != null) {
            hotelVO.setLatestCheckOutTime(((InputField) checkOutTimeField).getValue().getValue());
        }

        Field elementsField = hotelValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<HotelElementValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.HOTEL.getType());
            hotelVO.setElementValueMapList(elementList);
        }

        elementInfoList.add(hotelVO);
    }

    private void parseTicketElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field ticketField = complexValue.getValueField(SchemaFieldIdEnum.TICKET_ELEMENT.getId());
        if (!(ticketField instanceof ComplexField)) {
            return;
        }

        ComplexField ticketComplexField = (ComplexField) ticketField;
        ComplexValue ticketValue = ticketComplexField.getComplexValue();
        if (ticketValue == null) {
            return;
        }

        TicketElementGroupVO ticketVO = new TicketElementGroupVO();

        Field elementCategoryField = ticketValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                ticketVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.TICKET.getType().equals(mainElementType)) {
            ticketVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            ticketVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = ticketValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            ticketVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field elementsField = ticketValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<TicketElementValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.TICKET.getType());
            ticketVO.setElementValueMapList(elementList);
        }

        elementInfoList.add(ticketVO);
    }

    private void parseTrafficElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field trafficField = complexValue.getValueField(SchemaFieldIdEnum.TRAFFIC_ELEMENT.getId());
        if (!(trafficField instanceof ComplexField)) {
            return;
        }

        ComplexField trafficComplexField = (ComplexField) trafficField;
        ComplexValue trafficValue = trafficComplexField.getComplexValue();
        if (trafficValue == null) {
            return;
        }

        TrafficElementGroupVO trafficVO = new TrafficElementGroupVO();

        Field elementCategoryField = trafficValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                trafficVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.TRAFFIC.getType().equals(mainElementType)) {
            trafficVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            trafficVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = trafficValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            trafficVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field elementsField = trafficValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<TrafficValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.TRAFFIC.getType());
            trafficVO.setElementValueMapList(elementList);
        }

        elementInfoList.add(trafficVO);
    }

    private void parseWifiElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field wifiField = complexValue.getValueField(SchemaFieldIdEnum.WIFI_ELEMENT.getId());
        if (!(wifiField instanceof ComplexField)) {
            return;
        }

        ComplexField wifiComplexField = (ComplexField) wifiField;
        ComplexValue wifiValue = wifiComplexField.getComplexValue();
        if (wifiValue == null) {
            return;
        }

        WifiElementGroupVO wifiVO = new WifiElementGroupVO();

        Field elementCategoryField = wifiValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                wifiVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.WIFI.getType().equals(mainElementType)) {
            wifiVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            wifiVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = wifiValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            wifiVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field elementsField = wifiValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<WifiValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.WIFI.getType());
            wifiVO.setElementValueMapList(elementList);
        }

        elementInfoList.add(wifiVO);
    }

    private void parsePhoneCardElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field phoneCardField = complexValue.getValueField(SchemaFieldIdEnum.PHONECARD_ELEMENT.getId());
        if (!(phoneCardField instanceof ComplexField)) {
            return;
        }

        ComplexField phoneCardComplexField = (ComplexField) phoneCardField;
        ComplexValue phoneCardValue = phoneCardComplexField.getComplexValue();
        if (phoneCardValue == null) {
            return;
        }

        PhoneCardGroupVO phoneCardVO = new PhoneCardGroupVO();

        Field elementCategoryField = phoneCardValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                phoneCardVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.PHONECARD.getType().equals(mainElementType)) {
            phoneCardVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            phoneCardVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = phoneCardValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            phoneCardVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field elementsField = phoneCardValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<PhoneCardValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.PHONECARD.getType());
            phoneCardVO.setElementValueMapList(elementList);
        }

        elementInfoList.add(phoneCardVO);
    }

    private void parseSpecialActivityElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field specialActivityField = complexValue.getValueField(SchemaFieldIdEnum.SPECIAL_ACTIVITY_ELEMENT.getId());
        if (!(specialActivityField instanceof ComplexField)) {
            return;
        }

        ComplexField specialActivityComplexField = (ComplexField) specialActivityField;
        ComplexValue specialActivityValue = specialActivityComplexField.getComplexValue();
        if (specialActivityValue == null) {
            return;
        }

        SpecialActivityElementGroupVO specialActivityVO = new SpecialActivityElementGroupVO();

        Field elementCategoryField = specialActivityValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                specialActivityVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.SPECIAL_ACTIVITY.getType().equals(mainElementType)) {
            specialActivityVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            specialActivityVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = specialActivityValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            specialActivityVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field elementsField = specialActivityValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<SpecialActivityValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.SPECIAL_ACTIVITY.getType());
            specialActivityVO.setElementValueMapList(elementList);
        }

        elementInfoList.add(specialActivityVO);
    }

    private void parseSpaElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field spaField = complexValue.getValueField(SchemaFieldIdEnum.SPA_ELEMENT.getId());
        if (!(spaField instanceof ComplexField)) {
            return;
        }

        ComplexField spaComplexField = (ComplexField) spaField;
        ComplexValue spaValue = spaComplexField.getComplexValue();
        if (spaValue == null) {
            return;
        }

        SpaElementGroupVO spaVO = new SpaElementGroupVO();

        Field elementCategoryField = spaValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                spaVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }


        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.SPA.getType().equals(mainElementType)) {
            spaVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            spaVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = spaValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            spaVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field spaUsageScopeField = spaValue.getValueField(SchemaFieldIdEnum.SPA_USAGE_SCOPE.getId());
        if (spaUsageScopeField instanceof SingleCheckField && ((SingleCheckField) spaUsageScopeField).getValue() != null) {
            String scopeValue = ((SingleCheckField) spaUsageScopeField).getValue().getValue();
            if (StringUtils.isNotBlank(scopeValue) && NumberUtils.isCreatable(scopeValue)) {
                spaVO.setSpaUsageScope(Integer.valueOf(scopeValue));
            }
        }

        Field elementsField = spaValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<SpaValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.SPA.getType());
            spaVO.setElementValueMapList(elementList);
        }

        Field playThemeField = spaValue.getValueField(SchemaFieldIdEnum.PLAY_THEME.getId());
        if (playThemeField instanceof ComplexField) {
            PlayThemeBO playThemeBO = parsePlayTheme((ComplexField) playThemeField, ElementCategoryEnum.SPA);
            spaVO.setPlayTheme(playThemeBO);
        }

        elementInfoList.add(spaVO);
    }

    private void parseTourElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field tourField = complexValue.getValueField(SchemaFieldIdEnum.TOUR_ELEMENT.getId());
        if (!(tourField instanceof ComplexField)) {
            return;
        }

        ComplexField tourComplexField = (ComplexField) tourField;
        ComplexValue tourValue = tourComplexField.getComplexValue();
        if (tourValue == null) {
            return;
        }

        LocalTouristGuideElementGroupVO tourVO = new LocalTouristGuideElementGroupVO();

        Field elementCategoryField = tourValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                tourVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.TOUR.getType().equals(mainElementType)) {
            tourVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            tourVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = tourValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            tourVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field languageField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_LANGUAGE.getId());
        if (languageField instanceof MultiCheckField && ((MultiCheckField) languageField).getValues() != null) {
            List<String> languages = ((MultiCheckField) languageField).getValues().stream()
                    .map(Value::getValue)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            tourVO.setLanguage(languages);
        }

        Field numberStockField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_NUMBER_STOCK.getId());
        if (numberStockField instanceof InputField && ((InputField) numberStockField).getValue() != null) {
            String stockValue = ((InputField) numberStockField).getValue().getValue();
            if (StringUtils.isNotBlank(stockValue) && NumberUtils.isCreatable(stockValue)) {
                tourVO.setNumberStock(Integer.valueOf(stockValue));
            }
        }

        Field typeField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_TYPE.getId());
        if (typeField instanceof SingleCheckField && ((SingleCheckField) typeField).getValue() != null) {
            String typeValue = ((SingleCheckField) typeField).getValue().getValue();
            if (StringUtils.isNotBlank(typeValue) && NumberUtils.isCreatable(typeValue)) {
                tourVO.setType(Integer.valueOf(typeValue));
            }
        }

        Field outerIdField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_OUTER_ID.getId());
        if (outerIdField instanceof InputField && ((InputField) outerIdField).getValue() != null) {
            tourVO.setOuterId(((InputField) outerIdField).getValue().getValue());
        }

        Field bizTypeField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getId());
        if (bizTypeField instanceof SingleCheckField && ((SingleCheckField) bizTypeField).getValue() != null) {
            tourVO.setBizType(((SingleCheckField) bizTypeField).getValue().getValue());
        }

        Field hasTicketField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_HAS_TICKET.getId());
        if (hasTicketField instanceof SingleCheckField && ((SingleCheckField) hasTicketField).getValue() != null) {
            tourVO.setHasTicket(Boolean.valueOf(((SingleCheckField) hasTicketField).getValue().getValue()));
        }

        Field lecturerIdField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_RELATION_LECTURER_ID.getId());
        if (lecturerIdField instanceof InputField && ((InputField) lecturerIdField).getValue() != null) {
            tourVO.setRelationLecturerId(((InputField) lecturerIdField).getValue().getValue());
        }

        Field lecturerNickNameField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_LECTURER_NICK_NAME.getId());
        if (lecturerNickNameField instanceof InputField && ((InputField) lecturerNickNameField).getValue() != null) {
            tourVO.setLecturerNickName(((InputField) lecturerNickNameField).getValue().getValue());
        }

        Field lecturerRealNameField = tourValue.getValueField(SchemaFieldIdEnum.TOUR_LECTURER_REAL_NAME.getId());
        if (lecturerRealNameField instanceof InputField && ((InputField) lecturerRealNameField).getValue() != null) {
            tourVO.setLecturerRealName(((InputField) lecturerRealNameField).getValue().getValue());
        }

        elementInfoList.add(tourVO);
    }

    private void parsePhotographyElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field photographyField = complexValue.getValueField(SchemaFieldIdEnum.PHOTOGRAPHY_ELEMENT.getId());
        if (!(photographyField instanceof ComplexField)) {
            return;
        }

        ComplexField photographyComplexField = (ComplexField) photographyField;
        ComplexValue photographyValue = photographyComplexField.getComplexValue();
        if (photographyValue == null) {
            return;
        }

        PhotographyElementGroupVO photographyVO = new PhotographyElementGroupVO();

        Field elementCategoryField = photographyValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                photographyVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.PHOTOGRAPHY.getType().equals(mainElementType)) {
            photographyVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            photographyVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = photographyValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            photographyVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field addressField = photographyValue.getValueField(SchemaFieldIdEnum.PHOTOGRAPHY_ADDRESS.getId());
        if (addressField instanceof InputField && ((InputField) addressField).getValue() != null) {
            photographyVO.setAddress(((InputField) addressField).getValue().getValue());
        }

        Field daysField = photographyValue.getValueField(SchemaFieldIdEnum.PHOTOGRAPHY_DAYS.getId());
        if (daysField instanceof InputField && ((InputField) daysField).getValue() != null) {
            String daysValue = ((InputField) daysField).getValue().getValue();
            if (StringUtils.isNotBlank(daysValue) && NumberUtils.isCreatable(daysValue)) {
                photographyVO.setDays(Integer.valueOf(daysValue));
            }
        }

        Field clothingNumberField = photographyValue.getValueField(SchemaFieldIdEnum.PHOTOGRAPHY_CLOTHING_NUMBER.getId());
        if (clothingNumberField instanceof InputField && ((InputField) clothingNumberField).getValue() != null) {
            String numberValue = ((InputField) clothingNumberField).getValue().getValue();
            if (StringUtils.isNotBlank(numberValue) && NumberUtils.isCreatable(numberValue)) {
                photographyVO.setClothingNumber(Integer.valueOf(numberValue));
            }
        }

        Field outerIdField = photographyValue.getValueField(SchemaFieldIdEnum.PHOTOGRAPHY_OUTER_ID.getId());
        if (outerIdField instanceof InputField && ((InputField) outerIdField).getValue() != null) {
            photographyVO.setOuterId(((InputField) outerIdField).getValue().getValue());
        }

        Field styleField = photographyValue.getValueField(SchemaFieldIdEnum.PHOTOGRAPHY_STYLE.getId());
        if (styleField instanceof InputField && ((InputField) styleField).getValue() != null) {
            photographyVO.setPhotographyStyle(((InputField) styleField).getValue().getValue());
        }

        Field playThemeField = photographyValue.getValueField(SchemaFieldIdEnum.PLAY_THEME.getId());
        if (playThemeField instanceof ComplexField) {
            PlayThemeBO playThemeBO = parsePlayTheme((ComplexField) playThemeField, ElementCategoryEnum.PHOTOGRAPHY);
            photographyVO.setPlayTheme(playThemeBO);
        }

        elementInfoList.add(photographyVO);
    }

    private void parseDivingElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field divingField = complexValue.getValueField(SchemaFieldIdEnum.DIVING_ELEMENT.getId());
        if (!(divingField instanceof ComplexField)) {
            return;
        }

        ComplexField divingComplexField = (ComplexField) divingField;
        ComplexValue divingValue = divingComplexField.getComplexValue();
        if (divingValue == null) {
            return;
        }

        DivingElementGroupVO divingVO = new DivingElementGroupVO();

        Field elementCategoryField = divingValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                divingVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.DIVING.getType().equals(mainElementType)) {
            divingVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            divingVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = divingValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            divingVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field numberStockField = divingValue.getValueField(SchemaFieldIdEnum.DIVING_NUMBER_STOCK.getId());
        if (numberStockField instanceof InputField && ((InputField) numberStockField).getValue() != null) {
            String stockValue = ((InputField) numberStockField).getValue().getValue();
            if (StringUtils.isNotBlank(stockValue) && NumberUtils.isCreatable(stockValue)) {
                divingVO.setNumberStock(Integer.valueOf(stockValue));
            }
        }

        Field typeField = divingValue.getValueField(SchemaFieldIdEnum.DIVING_TYPE.getId());
        if (typeField instanceof SingleCheckField && ((SingleCheckField) typeField).getValue() != null) {
            String typeValue = ((SingleCheckField) typeField).getValue().getValue();
            if (StringUtils.isNotBlank(typeValue) && NumberUtils.isCreatable(typeValue)) {
                divingVO.setType(Integer.valueOf(typeValue));
            }
        }

        Field divingElementListField = divingValue.getValueField(SchemaFieldIdEnum.DIVING_ELEMENT_LIST.getId());
        if (divingElementListField instanceof MultiComplexField) {
            List<DivingElementVO> divingElementList = parseDivingElementList((MultiComplexField) divingElementListField);
            divingVO.setDivingElementList(divingElementList);
        }

        Field playThemeField = divingValue.getValueField(SchemaFieldIdEnum.PLAY_THEME.getId());
        if (playThemeField instanceof ComplexField) {
            PlayThemeBO playThemeBO = parsePlayTheme((ComplexField) playThemeField, ElementCategoryEnum.DIVING);
            divingVO.setPlayTheme(playThemeBO);
        }

        elementInfoList.add(divingVO);
    }

    private List<DivingElementVO> parseDivingElementList(MultiComplexField divingElementListField) {
        List<DivingElementVO> divingElementList = new ArrayList<>();
        if (CollectionUtils.isEmpty(divingElementListField.getComplexValues())) {
            return divingElementList;
        }

        for (ComplexValue elementValue : divingElementListField.getComplexValues()) {
            DivingElementVO elementVO = new DivingElementVO();

            Field labelField = elementValue.getValueField(SchemaFieldIdEnum.DIVING_ELEMENT_LABEL.getId());
            if (labelField instanceof InputField && ((InputField) labelField).getValue() != null) {
                elementVO.setLabel(((InputField) labelField).getValue().getValue());
            }

            Field valueField = elementValue.getValueField(SchemaFieldIdEnum.DIVING_ELEMENT_VALUE.getId());
            if (valueField instanceof InputField && ((InputField) valueField).getValue() != null) {
                String value = ((InputField) valueField).getValue().getValue();
                if (StringUtils.isNotBlank(value) && NumberUtils.isCreatable(value)) {
                    elementVO.setValue(Long.valueOf(value));
                }
            }

            Field poiRelatedField = elementValue.getValueField(SchemaFieldIdEnum.DIVING_ELEMENT_POI_RELATED.getId());
            if (poiRelatedField instanceof InputField && ((InputField) poiRelatedField).getValue() != null) {
                elementVO.setPoiRelated(Boolean.valueOf(((InputField) poiRelatedField).getValue().getValue()));
            }

            divingElementList.add(elementVO);
        }
        return divingElementList;
    }

    private void parseCateringElement(ComplexValue complexValue, List<BasePlayElementGroupVO> elementInfoList, Integer mainElementType) {
        Field cateringField = complexValue.getValueField(SchemaFieldIdEnum.CATERING_ELEMENT.getId());
        if (!(cateringField instanceof ComplexField)) {
            return;
        }

        ComplexField cateringComplexField = (ComplexField) cateringField;
        ComplexValue cateringValue = cateringComplexField.getComplexValue();
        if (cateringValue == null) {
            return;
        }

        CateringElementGroupVO cateringVO = new CateringElementGroupVO();

        Field elementCategoryField = cateringValue.getValueField(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        if (elementCategoryField instanceof InputField && ((InputField) elementCategoryField).getValue() != null) {
            String categoryValue = ((InputField) elementCategoryField).getValue().getValue();
            if (StringUtils.isNotBlank(categoryValue) && NumberUtils.isCreatable(categoryValue)) {
                cateringVO.setElementCategory(Integer.valueOf(categoryValue));
            }
        }

        // 根据mainElementType设置ElementType
        if (ElementCategoryEnum.CATERING.getType().equals(mainElementType)) {
            cateringVO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            cateringVO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }

        Field remarksField = cateringValue.getValueField(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        if (remarksField instanceof InputField && ((InputField) remarksField).getValue() != null) {
            cateringVO.setAdditionalRemarks(((InputField) remarksField).getValue().getValue());
        }

        Field businessTypeField = cateringValue.getValueField(SchemaFieldIdEnum.CATERING_BUSINESS_TYPE.getId());
        if (businessTypeField instanceof SingleCheckField && ((SingleCheckField) businessTypeField).getValue() != null) {
            String typeValue = ((SingleCheckField) businessTypeField).getValue().getValue();
            if (StringUtils.isNotBlank(typeValue) && NumberUtils.isCreatable(typeValue)) {
                cateringVO.setBusinessType(Integer.valueOf(typeValue));
            }
        }

        Field poiInfoField = cateringValue.getValueField(SchemaFieldIdEnum.CATERING_POI.getId());
        if (poiInfoField instanceof ComplexField) {
            ComplexField poiComplexField = (ComplexField) poiInfoField;
            ComplexValue poiComplexValue = poiComplexField.getComplexValue();
            if (poiComplexValue != null) {
                PlayPoiVO playPoiVO = new PlayPoiVO();
                Field poiIdField = poiComplexValue.getValueField(SchemaFieldIdEnum.CATERING_POI_ID.getId());
                if (poiIdField instanceof InputField && ((InputField) poiIdField).getValue() != null) {
                    String idValue = ((InputField) poiIdField).getValue().getValue();
                    if (StringUtils.isNotBlank(idValue) && NumberUtils.isCreatable(idValue)) {
                        playPoiVO.setPoiId(Long.valueOf(idValue));
                    }
                }
                Field poiNameField = poiComplexValue.getValueField(SchemaFieldIdEnum.CATERING_POI_NAME.getId());
                if (poiNameField instanceof InputField && ((InputField) poiNameField).getValue() != null) {
                    if (playPoiVO.getPoiValue() == null) {
                        playPoiVO.setPoiValue(new java.util.HashMap<>());
                    }
                    playPoiVO.getPoiValue().put("poiName", ((InputField) poiNameField).getValue().getValue());
                }
                cateringVO.setPlayPoiVO(playPoiVO);
            }
        }

        Field numberStockField = cateringValue.getValueField(SchemaFieldIdEnum.CATERING_NUMBER_STOCK.getId());
        if (numberStockField instanceof InputField && ((InputField) numberStockField).getValue() != null) {
            String stockValue = ((InputField) numberStockField).getValue().getValue();
            if (StringUtils.isNotBlank(stockValue) && NumberUtils.isCreatable(stockValue)) {
                cateringVO.setNumberStock(Integer.valueOf(stockValue));
            }
        }

        Field elementsField = cateringValue.getValueField(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        if (elementsField instanceof MultiComplexField) {
            List<ElementVO<CateringValueMapVO>> elementList = parseElementList((MultiComplexField) elementsField, ElementCategoryEnum.CATERING.getType());
            cateringVO.setElementValueMapList(elementList);
        }

        Field playThemeField = cateringValue.getValueField(SchemaFieldIdEnum.PLAY_THEME.getId());
        if (playThemeField instanceof ComplexField) {
            PlayThemeBO playThemeBO = parsePlayTheme((ComplexField) playThemeField, ElementCategoryEnum.CATERING);
            cateringVO.setPlayTheme(playThemeBO);
        }

        elementInfoList.add(cateringVO);
    }

    private <T extends BaseElementValueMapVO> List<ElementVO<T>> parseElementList(MultiComplexField elementsField, Integer elementType) {
        List<ElementVO<T>> elementList = new ArrayList<>();
        if (CollectionUtils.isEmpty(elementsField.getComplexValues())) {
            return elementList;
        }

        for (ComplexValue elementValue : elementsField.getComplexValues()) {
            ElementVO<T> elementVO = new ElementVO<>();

            Field elementIdField = elementValue.getValueField(SchemaFieldIdEnum.ELEMENT_ID.getId());
            if (elementIdField instanceof InputField && ((InputField) elementIdField).getValue() != null) {
                String idValue = ((InputField) elementIdField).getValue().getValue();
                if (StringUtils.isNotBlank(idValue) && NumberUtils.isCreatable(idValue)) {
                    elementVO.setId(Long.valueOf(idValue));
                }
            }

            Integer currentElementType = elementType;
            Field elementTypeField = elementValue.getValueField(SchemaFieldIdEnum.ELEMENT_TYPE.getId());
            if (elementTypeField instanceof InputField && ((InputField) elementTypeField).getValue() != null) {
                String typeValue = ((InputField) elementTypeField).getValue().getValue();
                if (StringUtils.isNotBlank(typeValue) && NumberUtils.isCreatable(typeValue)) {
                    currentElementType = Integer.valueOf(typeValue);
                }
            }
            if (currentElementType != null) {
                elementVO.setElementType(currentElementType);
            }

            Field featuresField = elementValue.getValueField(SchemaFieldIdEnum.ELEMENT_FEATURES.getId());
            if (featuresField instanceof MultiComplexField) {
                T valueMap = parseElementFeatures((MultiComplexField) featuresField, currentElementType);
                elementVO.setValueMap(valueMap);
            }

            elementList.add(elementVO);
        }

        return elementList;
    }

    @SuppressWarnings("unchecked")
    private <T extends BaseElementValueMapVO> T parseElementFeatures(MultiComplexField featuresField, Integer elementType) {
        BaseElementValueMapVO valueMap;

        ElementCategoryEnum category = ElementCategoryEnum.getByType(elementType);
        if (category != null) {
            switch (category) {
                case HOTEL:
                    valueMap = new HotelElementValueMapVO();
                    break;
                case TICKET:
                    valueMap = new TicketElementValueMapVO();
                    break;
                case TRAFFIC:
                    valueMap = new TrafficValueMapVO();
                    break;
                case WIFI:
                    valueMap = new WifiValueMapVO();
                    break;
                case PHONECARD:
                    valueMap = new PhoneCardValueMapVO();
                    break;
                case SPECIAL_ACTIVITY:
                    valueMap = new SpecialActivityValueMapVO();
                    break;
                case SPA:
                    valueMap = new SpaValueMapVO();
                    break;
                case CATERING:
                    valueMap = new CateringValueMapVO();
                    break;
                case TOUR:
                case PHOTOGRAPHY:
                case DIVING:
                default:
                    valueMap = new BaseElementValueMapVO();
                    break;
            }
        } else {
            valueMap = new BaseElementValueMapVO();
        }

        if (CollectionUtils.isEmpty(featuresField.getComplexValues())) {
            return (T) valueMap;
        }

        for (ComplexValue featureValue : featuresField.getComplexValues()) {
            Field keyField = featureValue.getValueField(SchemaFieldIdEnum.FEATURE_KEY.getId());
            Field valueField = featureValue.getValueField(SchemaFieldIdEnum.FEATURE_VALUE.getId());

            if (keyField instanceof InputField && ((InputField) keyField).getValue() != null &&
                    valueField instanceof InputField && ((InputField) valueField).getValue() != null) {

                String key = ((InputField) keyField).getValue().getValue();
                String value = ((InputField) valueField).getValue().getValue();

                if (valueMap instanceof HotelElementValueMapVO) {
                    HotelElementValueMapVO hotelValueMap = (HotelElementValueMapVO) valueMap;
                    if ("CITY".equals(key)) {
                        hotelValueMap.setCity(value);
                    } else if ("ROOM_TYPE_ID".equals(key)) {
                        hotelValueMap.setRoomTypeId(value);
                    } else if ("SHID".equals(key)) {
                        hotelValueMap.setShId(value);
                    } else if ("DIVISION_ID".equals(key)) {
                        hotelValueMap.setDivisionId(value);
                    } else if ("numberStock".equals(key) && NumberUtils.isCreatable(value)) {
                        hotelValueMap.setNumberStock(value);
                    } else if ("nightNumber".equals(key) && NumberUtils.isCreatable(value)) {
                        hotelValueMap.setNightNumber(Integer.valueOf(value));
                    } else {
                        setBaseElementValueMapProperty(valueMap, key, value);
                    }
                } else if (valueMap instanceof TicketElementValueMapVO) {
                    TicketElementValueMapVO ticketValueMap = (TicketElementValueMapVO) valueMap;
                    if ("SCENIC_PRODUCT_ID".equals(key)) {
                        ticketValueMap.setScenicProductId(value);
                    } else if ("TICKET_KIND_VID".equals(key)) {
                        ticketValueMap.setTicketKindVid(value);
                    } else if ("AREA_NAME".equals(key)) {
                        ticketValueMap.setAreaName(value);
                    } else if ("AREA_PV".equals(key)) {
                        ticketValueMap.setAreaPv(value);
                    } else if ("SCENIC_ID".equals(key)) {
                        ticketValueMap.setScenicId(value);
                    } else if ("PRODUCT_NAME".equals(key)) {
                        ticketValueMap.setProductName(value);
                    } else if ("EPISODE_NAME".equals(key)) {
                        ticketValueMap.setEpisodeName(value);
                    } else if ("CITY".equals(key)) {
                        ticketValueMap.setCity(value);
                    } else if ("divisionId".equals(key)) {
                        ticketValueMap.setDivisionId(value);
                    } else if ("EPISODE_PV".equals(key)) {
                        ticketValueMap.setEpisodePv(value);
                    } else if ("TICKET_KIND_PV".equals(key)) {
                        ticketValueMap.setTicketKindPv(value);
                    } else if ("numberStock".equals(key) && NumberUtils.isCreatable(value)) {
                        ticketValueMap.setNumberStock(value);
                    } else {
                        setBaseElementValueMapProperty(valueMap, key, value);
                    }
                } else if (valueMap instanceof PhoneCardValueMapVO) {
                    PhoneCardValueMapVO phoneCardValueMap = (PhoneCardValueMapVO) valueMap;
                    if ("SIM_NET".equals(key)) {
                        phoneCardValueMap.setSimNet(value);
                    } else if ("SIZE".equals(key)) {
                        phoneCardValueMap.setSize(value);
                    } else {
                        setBaseElementValueMapProperty(valueMap, key, value);
                    }
                } else if (valueMap instanceof SpecialActivityValueMapVO) {
                    SpecialActivityValueMapVO specialActivityValueMap = (SpecialActivityValueMapVO) valueMap;
                    if ("numberStock".equals(key) && NumberUtils.isCreatable(value)) {
                        specialActivityValueMap.setNumberStock(Integer.valueOf(value));
                    } else if ("activityType".equals(key) && NumberUtils.isCreatable(value)) {
                        specialActivityValueMap.setActivityType(Integer.valueOf(value));
                    } else if ("ACTIVITY_CATEGORY_PATH_IDS".equals(key)) {
                        specialActivityValueMap.setActivityCategoryPathIds(value);
                    } else {
                        setBaseElementValueMapProperty(valueMap, key, value);
                    }
                } else if (valueMap instanceof SpaValueMapVO) {
                    SpaValueMapVO spaValueMap = (SpaValueMapVO) valueMap;
                    if ("POI_NAME".equals(key)) {
                        spaValueMap.setPoiName(value);
                    } else if ("CITY".equals(key)) {
                        spaValueMap.setCity(value);
                    } else if ("numberStock".equals(key) && NumberUtils.isCreatable(value)) {
                        spaValueMap.setNumberStock(Integer.valueOf(value));
                    } else {
                        setBaseElementValueMapProperty(valueMap, key, value);
                    }
                } else if (valueMap instanceof CateringValueMapVO) {
                    CateringValueMapVO cateringValueMap = (CateringValueMapVO) valueMap;
                    if ("POI_NAME".equals(key)) {
                        cateringValueMap.setPoiName(value);
                    } else if ("numberStock".equals(key) && NumberUtils.isCreatable(value)) {
                        cateringValueMap.setNumberStock(Integer.valueOf(value));
                    } else {
                        setBaseElementValueMapProperty(valueMap, key, value);
                    }
                } else {
                    setBaseElementValueMapProperty(valueMap, key, value);
                }
            }
        }

        return (T) valueMap;
    }

    private void setBaseElementValueMapProperty(BaseElementValueMapVO valueMap, String key, String value) {
        if ("NAME".equals(key)) {
            valueMap.setName(value);
        } else if ("DESC".equals(key)) {
            valueMap.setDesc(value);
        } else if ("OUTER_ID".equals(key)) {
            valueMap.setOuterId(value);
        } else if ("POIID".equals(key)) {
            valueMap.setPoiId(value);
        } else if ("POI_RELATED".equals(key)) {
            valueMap.setPoiRelated(value);
        } else if ("ELEMENT_TYPE".equals(key)) {
            valueMap.setElementType(value);
        }
    }

    private PlayThemeBO parsePlayTheme(ComplexField playThemeField, ElementCategoryEnum elementCategoryEnum) {
        PlayThemeBO playThemeBO = new PlayThemeBO();

        ComplexValue playThemeValue = playThemeField.getComplexValue();
        if (playThemeValue == null) {
            return playThemeBO;
        }

        switch (elementCategoryEnum) {
            case SPA:
                playThemeBO.setPlayThemeProps(Collections.singletonList(Lists.newArrayList(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME.getParentPlayThemeId(), ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME.getPlayThemeId())));
                break;
            case CATERING:
                playThemeBO.setPlayThemeProps(Collections.singletonList(Lists.newArrayList(ElementPlayThemeEnum.CATERING_ELEMENT_PLAY_THEME.getParentPlayThemeId(), ElementPlayThemeEnum.CATERING_ELEMENT_PLAY_THEME.getPlayThemeId())));
                break;
            case PHOTOGRAPHY:
                playThemeBO.setPlayThemeProps(Collections.singletonList(Lists.newArrayList(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME.getParentPlayThemeId(), ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME.getPlayThemeId())));
                break;
            case DIVING:
                playThemeBO.setPlayThemeProps(Collections.singletonList(Lists.newArrayList(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME.getParentPlayThemeId(), ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME.getPlayThemeId())));
                break;
            default:
                break;
        }

        Field playThemeValueField = playThemeValue.getValueField(SchemaFieldIdEnum.PLAY_THEME_VALUE.getId());
        getPlayMethodPropFromField(playThemeBO, playThemeValueField);

        return playThemeBO;
    }

    private void processPlayProp(ComplexValue playPropComplexValue, PlayThemeBO playThemeBO, CompExtParam param) {
        Field playMethodField = playPropComplexValue.getValueField(SchemaFieldIdEnum.PLAY_METHOD.getId());
        if (playMethodField instanceof MultiCheckField && ((MultiCheckField) playMethodField).getValues() != null) {
            List<Long> playThemes = ((MultiCheckField) playMethodField).getValues().stream()
                    .map(Value::getValue)
                    .filter(StringUtils::isNotBlank)
                    .filter(NumberUtils::isCreatable)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            List<List<Long>> playThemeProps = new ArrayList<>();
            List<PlayType> playTypeTree = playTypeHelpServiceRepo.getPlayTypeTree();
            for (Long playThemeId : playThemes) {
                Long parentId = findParentId(playTypeTree, playThemeId);
                if (parentId != null) {
                    List<Long> pair = new ArrayList<>();
                    pair.add(parentId);
                    pair.add(playThemeId);
                    playThemeProps.add(pair);
                }
            }
            playThemeBO.setPlayThemeProps(playThemeProps);
        }

        Field playMethodPropField = playPropComplexValue.getValueField(SchemaFieldIdEnum.PLAY_METHOD_PROP.getId());
        getPlayMethodPropFromField(playThemeBO, playMethodPropField);
    }

    /**
     * 递归查找玩法ID的父ID
     */
    private Long findParentId(List<PlayType> playTypeTree, Long targetId) {
        if (playTypeTree == null) {
            return null;
        }
        for (PlayType node : playTypeTree) {
            if (node.getChildren() != null) {
                for (PlayType child : node.getChildren()) {
                    if (child.getId().equals(targetId)) {
                        return node.getId();
                    }
                }
                Long parentId = findParentId(node.getChildren(), targetId);
                if (parentId != null) {
                    return parentId;
                }
            }
        }
        return null;
    }

    private static void getPlayMethodPropFromField(PlayThemeBO playThemeBO, Field playMethodPropField) {
        if (playMethodPropField instanceof MultiComplexField &&
                CollectionUtils.isNotEmpty(((MultiComplexField) playMethodPropField).getComplexValues())) {

            List<PlayMethodValueModel> valueModels = new ArrayList<>();

            for (ComplexValue themeValue : ((MultiComplexField) playMethodPropField).getComplexValues()) {
                PlayMethodValueModel valueModel = new PlayMethodValueModel();

                Field keyField = themeValue.getValueField(SchemaFieldIdEnum.PLAY_THEME_VALUE_KEY.getId());
                if (keyField instanceof InputField && ((InputField) keyField).getValue() != null) {
                    valueModel.setKey(((InputField) keyField).getValue().getValue());
                }

                Field tabField = themeValue.getValueField(SchemaFieldIdEnum.PLAY_THEME_VALUE_TAB.getId());
                if (tabField instanceof InputField && ((InputField) tabField).getValue() != null) {
                    valueModel.setTab(((InputField) tabField).getValue().getValue());
                }

                // 实现properties解析
                List<PlayProperty> properties = new ArrayList<>();
                List<ComplexValue> complexFieldValues = themeValue.getComplexFieldValues(SchemaFieldIdEnum.PLAY_PROPERTIES.getId());

                for (ComplexValue complexValue : complexFieldValues) {

                    if (complexValue == null) {
                        continue;
                    }

                    PlayProperty playProperty = new PlayProperty();

                    //ID
                    Field idField = complexValue.getValueField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_ID.getId());
                    if (idField instanceof InputField && ((InputField) idField).getValue() != null) {
                        String idValue = ((InputField) idField).getValue().getValue();
                        if (StringUtils.isNotBlank(idValue) && NumberUtils.isCreatable(idValue)) {
                            playProperty.setId(Long.parseLong(idValue));
                        }
                    }

                    // 名称
                    Field nameField = complexValue.getValueField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_NAME.getId());
                    if (nameField instanceof InputField && ((InputField) nameField).getValue() != null) {
                        playProperty.setName(((InputField) nameField).getValue().getValue());
                    }

                    // 类型
                    Field typeField = complexValue.getValueField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_TYPE.getId());
                    if (typeField instanceof InputField && ((InputField) typeField).getValue() != null) {
                        String typeValue = ((InputField) typeField).getValue().getValue();
                        if (StringUtils.isNotBlank(typeValue) && NumberUtils.isCreatable(typeValue)) {
                            playProperty.setType(Integer.parseInt(typeValue));
                        }
                    }

                    // 值
                    Field valueField = complexValue.getValueField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_VALUE.getId());
                    if (valueField instanceof InputField && ((InputField) valueField).getValue() != null) {
                        playProperty.setValue(((InputField) valueField).getValue().getValue());
                    }

                    // 是否其他
                    Field isOtherField = complexValue.getValueField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_IS_OTHER.getId());
                    if (isOtherField instanceof InputField && ((InputField) isOtherField).getValue() != null) {
                        String isOtherValue = ((InputField) isOtherField).getValue().getValue();
                        if (StringUtils.isNotBlank(isOtherValue) && NumberUtils.isCreatable(isOtherValue)) {
                            playProperty.setIsOther(Integer.valueOf(isOtherValue));
                        }
                    } else {
                        playProperty.setIsOther(0); // Default to 0 if not present
                    }

                    //其他值
                    Field otherValField = complexValue.getValueField(SchemaFieldIdEnum.PLAY_ATTRIBUTE_OTHER_VAL.getId());
                    if (otherValField instanceof InputField && ((InputField) otherValField).getValue() != null) {
                        playProperty.setOtherVal(((InputField) otherValField).getValue().getValue());
                    }

                    properties.add(playProperty);


                }
                valueModel.setProperties(properties);


                valueModels.add(valueModel);
            }

            playThemeBO.setPlayThemeValue(valueModels);
        }
    }

    private void processCalendarPriceStock(ComplexValue complexValue, OverseaPlayPackageInfoControlDO packageInfoControlDO) {
        Field calendarPriceStockField = complexValue.getValueField(SchemaFieldIdEnum.CALENDAR_PRICE_STOCK.getId());
        if (!(calendarPriceStockField instanceof MultiComplexField)) {
            return;
        }

        MultiComplexField calendarField = (MultiComplexField) calendarPriceStockField;
        if (CollectionUtils.isEmpty(calendarField.getComplexValues())) {
            return;
        }

        List<PlaySkuPriceStockControlDO> playSkuPriceStockList = new ArrayList<>();

        for (ComplexValue calendarComplexValue : calendarField.getComplexValues()) {
            PlaySkuPriceStockControlDO playSkuPriceStockControlDO = new PlaySkuPriceStockControlDO();

            Field skuIdField = calendarComplexValue.getValueField(SchemaFieldIdEnum.SKUID.getId());
            if (skuIdField instanceof InputField && ((InputField) skuIdField).getValue() != null) {
                String skuIdValue = ((InputField) skuIdField).getValue().getValue();
                if (StringUtils.isNotBlank(skuIdValue) && NumberUtils.isCreatable(skuIdValue)) {
                    playSkuPriceStockControlDO.setSkuId(Long.valueOf(skuIdValue));
                }
            }

            Field priceTypeField = calendarComplexValue.getValueField(SchemaFieldIdEnum.PRICE_TYPE.getId());
            if (priceTypeField instanceof SingleCheckField && ((SingleCheckField) priceTypeField).getValue() != null) {
                String priceTypeValue = ((SingleCheckField) priceTypeField).getValue().getValue();
                if (StringUtils.isNotBlank(priceTypeValue) && NumberUtils.isCreatable(priceTypeValue)) {
                    Integer priceType = Integer.valueOf(priceTypeValue);
                    playSkuPriceStockControlDO.setPriceType(priceType);
                    //一口价和成人儿童单房差不能同时出现
                    if (priceType != 0) {
                        packageInfoControlDO.setFixedPrice(false);
                    }
                }
            }

            Field currencyField = calendarComplexValue.getValueField(SchemaFieldIdEnum.CURRENCY.getId());
            if (currencyField instanceof InputField && ((InputField) currencyField).getValue() != null) {
                playSkuPriceStockControlDO.setCurrency(((InputField) currencyField).getValue().getValue());
            }

            Field unavailableDateField = calendarComplexValue.getValueField(SchemaFieldIdEnum.UNAVAILABLE_DATE.getId());
            if (unavailableDateField instanceof InputField && ((InputField) unavailableDateField).getValue() != null) {
                String unavailableDateValue = ((InputField) unavailableDateField).getValue().getValue();
                if (StringUtils.isNotBlank(unavailableDateValue)) {
                    List<String> unavailableDates = Lists.newArrayList(unavailableDateValue.split(","));
                    playSkuPriceStockControlDO.setUnavailableDate(unavailableDates);
                }
            }

            Field directInfoField = calendarComplexValue.getValueField(SchemaFieldIdEnum.DIRECT_INFO.getId());
            if (directInfoField instanceof ComplexField) {
                ComplexField directComplexField = (ComplexField) directInfoField;
                ComplexValue directComplexValue = directComplexField.getComplexValue();
                if (directComplexValue != null) {
                    PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo = new PlayAbroadPackageDirectInfo();
                    processDirectInfoInner(directComplexValue, playAbroadPackageDirectInfo);

                    playSkuPriceStockControlDO.setPlayAbroadPackageSkuDirectInfo(playAbroadPackageDirectInfo);
                }
            }

            Field calendarPriceStockDataField = calendarComplexValue.getValueField(SchemaFieldIdEnum.CALENDAR_PRICE_STOCK_DATA.getId());
            if (calendarPriceStockDataField instanceof MultiComplexField) {
                MultiComplexField calendarDataField = (MultiComplexField) calendarPriceStockDataField;
                if (CollectionUtils.isNotEmpty(calendarDataField.getComplexValues())) {
                    List<CalendarPriceStockBO> calendarPriceStockList = new ArrayList<>();

                    for (ComplexValue calendarDataValue : calendarDataField.getComplexValues()) {
                        CalendarPriceStockBO calendarPriceStockBO = new CalendarPriceStockBO();

                        Field dateField = calendarDataValue.getValueField(SchemaFieldIdEnum.DATE.getId());
                        if (dateField instanceof InputField && ((InputField) dateField).getValue() != null) {
                            calendarPriceStockBO.setDate(((InputField) dateField).getValue().getValue());
                        }

                        Field priceField = calendarDataValue.getValueField(SchemaFieldIdEnum.PRICE.getId());
                        if (priceField instanceof InputField && ((InputField) priceField).getValue() != null) {
                            calendarPriceStockBO.setPrice(((InputField) priceField).getValue().getValue());
                        }

                        Field stockField = calendarDataValue.getValueField(SchemaFieldIdEnum.STOCK.getId());
                        if (stockField instanceof InputField && ((InputField) stockField).getValue() != null) {
                            String stockValue = ((InputField) stockField).getValue().getValue();
                            if (StringUtils.isNotBlank(stockValue) && NumberUtils.isCreatable(stockValue)) {
                                calendarPriceStockBO.setStock(Long.valueOf(stockValue));
                            }
                        }

                        Field outerCodeField = calendarDataValue.getValueField(SchemaFieldIdEnum.OUTER_CODE.getId());
                        if (outerCodeField instanceof InputField && ((InputField) outerCodeField).getValue() != null) {
                            calendarPriceStockBO.setOuterCode(((InputField) outerCodeField).getValue().getValue());
                        }

                        calendarPriceStockList.add(calendarPriceStockBO);
                    }

                    playSkuPriceStockControlDO.setCalendarPriceStock(calendarPriceStockList);
                }
            }

            playSkuPriceStockList.add(playSkuPriceStockControlDO);
        }

        packageInfoControlDO.setPlayCanlendePriceStockControlDO(playSkuPriceStockList);
    }


    private void processPlayAbroadPackageDirectInfo(ComplexValue directComplexValue, OverseaPlayPackageInfoControlDO packageInfoControlDO) {
        Field addPriceRulerField = directComplexValue.getValueField(SchemaFieldIdEnum.PACKAGE_ADD_PRICE_RULER.getId());
        if (addPriceRulerField instanceof ComplexField && ((ComplexField) addPriceRulerField).getComplexValue() != null) {

            PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo = new PlayAbroadPackageDirectInfo();

            processDirectInfoInner(((ComplexField) addPriceRulerField).getComplexValue(), playAbroadPackageDirectInfo);

            packageInfoControlDO.setPlayAbroadPackageDirectInfo(playAbroadPackageDirectInfo);
        }
    }

    private void processDirectInfoInner(ComplexValue directComplexValue, PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo) {

        Field spIdField = directComplexValue.getValueField(SchemaFieldIdEnum.SPID.getId());
        if (spIdField instanceof InputField && ((InputField) spIdField).getValue() != null) {
            String spIdValue = ((InputField) spIdField).getValue().getValue();
            if (StringUtils.isNotBlank(spIdValue) && NumberUtils.isCreatable(spIdValue)) {
                playAbroadPackageDirectInfo.setSpId(Long.valueOf(spIdValue));
            }
        }

        Field supplierProductCodeField = directComplexValue.getValueField(SchemaFieldIdEnum.SUPPLIER_PRODUCT_CODE.getId());
        if (supplierProductCodeField instanceof InputField && ((InputField) supplierProductCodeField).getValue() != null) {
            playAbroadPackageDirectInfo.setSupplierProductCode(((InputField) supplierProductCodeField).getValue().getValue());
        }

        Field syncTypeField = directComplexValue.getValueField(SchemaFieldIdEnum.SYNC_TYPE.getId());
        if (syncTypeField instanceof InputField && ((InputField) syncTypeField).getValue() != null) {
            String syncTypeValue = ((InputField) syncTypeField).getValue().getValue();
            if (StringUtils.isNotBlank(syncTypeValue) && NumberUtils.isCreatable(syncTypeValue)) {
                playAbroadPackageDirectInfo.setSyncType(Integer.valueOf(syncTypeValue));
            }
        }

        Field crowdCodeField = directComplexValue.getValueField(SchemaFieldIdEnum.CROWD_CODE.getId());
        if (crowdCodeField instanceof InputField && ((InputField) crowdCodeField).getValue() != null) {
            playAbroadPackageDirectInfo.setCrowdCode(((InputField) crowdCodeField).getValue().getValue());
        }

        Field packageSkuAddPriceRuleField = directComplexValue.getValueField(SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getId());
        if (packageSkuAddPriceRuleField instanceof ComplexField) {
            processAddPriceRule((ComplexField) packageSkuAddPriceRuleField, playAbroadPackageDirectInfo);
        }
    }

    private static void processAddPriceRule(ComplexField addPriceRulerField, PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo) {
        ComplexValue addPriceRulerComplexValue = addPriceRulerField.getComplexValue();
        if (addPriceRulerComplexValue != null) {
            AddPriceRuler addPriceRuler = new AddPriceRuler();
            Field priceRuleTypeField = addPriceRulerComplexValue.getValueField(SchemaFieldIdEnum.PRICE_RULE_TYPE.getId());
            if (priceRuleTypeField instanceof SingleCheckField && ((SingleCheckField) priceRuleTypeField).getValue() != null) {
                String priceRuleTypeValue = ((SingleCheckField) priceRuleTypeField).getValue().getValue();
                if (StringUtils.isNotBlank(priceRuleTypeValue) && NumberUtils.isCreatable(priceRuleTypeValue)) {
                    addPriceRuler.setType(Integer.valueOf(priceRuleTypeValue));
                }
            }

            Field priceRuleValueField = addPriceRulerComplexValue.getValueField(SchemaFieldIdEnum.ADD_PRICE_RULE_MAIN_VALUE.getId());
            if (priceRuleValueField instanceof InputField && ((InputField) priceRuleValueField).getValue() != null) {
                addPriceRuler.setValue(((InputField) priceRuleValueField).getValue().getValue());
            }

            //multiComplexField
            Field priceRuleSpecialDetailValueField = addPriceRulerComplexValue.getValueField(SchemaFieldIdEnum.ADD_PRICE_RULE_SPECIAL_DETAIL_VALUE.getId());
            if (priceRuleSpecialDetailValueField instanceof MultiComplexField) {
                MultiComplexField priceRuleSpecialDetailValueComplexField = (MultiComplexField) priceRuleSpecialDetailValueField;
                if (CollectionUtils.isNotEmpty(priceRuleSpecialDetailValueComplexField.getComplexValues())) {
                    List<SpecialRuleDTO> addPriceRulerSpecialDTOList = new ArrayList<>();
                    for (ComplexValue priceRuleSpecialDetailValueComplexValue : priceRuleSpecialDetailValueComplexField.getComplexValues()) {
                        SpecialRuleDTO addPriceRulerSpecialDTO = new SpecialRuleDTO();
                        Field specialTypeField = priceRuleSpecialDetailValueComplexValue.getValueField(SchemaFieldIdEnum.PRICE_RULE_TYPE.getId());
                        if (specialTypeField instanceof SingleCheckField && ((SingleCheckField) specialTypeField).getValue() != null) {
                            String specialTypeValue = ((SingleCheckField) specialTypeField).getValue().getValue();
                            if (StringUtils.isNotBlank(specialTypeValue) && NumberUtils.isCreatable(specialTypeValue)) {
                                addPriceRulerSpecialDTO.setType(Integer.valueOf(specialTypeValue));
                            }
                        }
                        Field specialPriceRuleValueField = priceRuleSpecialDetailValueComplexValue.getValueField(SchemaFieldIdEnum.ADD_PRICE_RULE_SPECIAL_DETAIL_VALUE.getId());
                        if (specialPriceRuleValueField instanceof InputField && ((InputField) specialPriceRuleValueField).getValue() != null) {
                            addPriceRulerSpecialDTO.setValue(((InputField) specialPriceRuleValueField).getValue().getValue());
                        }
                        Field specialStartDateField = priceRuleSpecialDetailValueComplexValue.getValueField(SchemaFieldIdEnum.START_DATE.getId());
                        if (specialStartDateField instanceof InputField && ((InputField) specialStartDateField).getValue() != null) {
                            addPriceRulerSpecialDTO.setStart(new Date(Long.parseLong(((InputField) specialStartDateField).getValue().getValue())));
                        }
                        Field specialEndDateField = priceRuleSpecialDetailValueComplexValue.getValueField(SchemaFieldIdEnum.END_DATE.getId());
                        if (specialEndDateField instanceof InputField && ((InputField) specialEndDateField).getValue() != null) {
                            addPriceRulerSpecialDTO.setEnd(new Date(Long.parseLong(((InputField) specialEndDateField).getValue().getValue())));
                        }

                        Field specialAvailableWeedDaysField = priceRuleSpecialDetailValueComplexValue.getValueField(SchemaFieldIdEnum.AVAILABLE_WEEK_DAYS.getId());
                        if (specialAvailableWeedDaysField instanceof MultiInputField && ((MultiInputField) specialAvailableWeedDaysField).getValues() != null) {
                            List<Value> specialAvailableWeedDaysValue = ((MultiInputField) specialAvailableWeedDaysField).getValues();
                            if (CollectionUtils.isNotEmpty(specialAvailableWeedDaysValue)) {
                                List<Integer> specialAvailableWeedDays = specialAvailableWeedDaysValue.stream()
                                        .filter(Objects::nonNull)
                                        .map(Value::getValue)
                                        .filter(StringUtils::isNotBlank)
                                        .map(Integer::parseInt)
                                        .collect(Collectors.toList());
                                addPriceRulerSpecialDTO.setAvailableWeedDays(specialAvailableWeedDays);
                            }
                        }
                        addPriceRulerSpecialDTOList.add(addPriceRulerSpecialDTO);
                    }
                    addPriceRuler.setSpecialRule(addPriceRulerSpecialDTOList);
                }
            }
            playAbroadPackageDirectInfo.setAddPriceRuler(addPriceRuler);
        }
    }
}