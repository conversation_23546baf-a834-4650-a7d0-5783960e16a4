package com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2vo;

import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo.*;
import com.taobao.gpf.app.playabroad.constant.element.BreakFastTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Objects;


@Mapper(componentModel = "spring", imports = {ElementCategoryEnum.class})
public interface PlayProductConvertorHelper {

    @Mapping(target = "elementValueMapList", source = "ticketElementList")
    TicketElementGroupVO toTicketElementGroupVO(TicketElementGroupBO value);

    @Mapping(target = "valueMap.scenicProductId", source = "valueMap.scenicSpotChargeItemBO.scenicSpotChargeItemId")
    @Mapping(target = "valueMap.productName", source = "valueMap.scenicSpotChargeItemBO.scenicSpotChargeItemName")
    @Mapping(target = "valueMap.ticketKindVid", source = "valueMap.ticketKindId")
    @Mapping(target = "valueMap.divisionId", source = "valueMap.division.divisionId")
    @Mapping(target = "valueMap.city", source = "valueMap.division.divisionName")
    ElementVO<TicketElementValueMapVO> toTicketElementValueMapVOElementVO(ElementBO<TicketElementValueMapBO> valueMap);


    @Mapping(target = "elementValueMapList", source = "hotelElementList")
    HotelElementGroupVO toHotelElementGroupVO(HotelElementGroupBO value);

    @Mapping(target = "numberStock", source = "breakfastCount")
    HotelElementValueMapVO hotelElementValueMapBOToHotelElementValueMapVO(HotelElementValueMapBO hotelElementValueMapBO);

    @Mapping(target = "valueMap.shId", source = "valueMap.hotelBO.hotelId")
    @Mapping(target = "valueMap.poiId", source = "valueMap.hotelBO.poi")
    @Mapping(target = "valueMap.roomTypeId", source = "valueMap.roomBO.id")
    @Mapping(target = "valueMap.name", source = "valueMap.hotelName")
    @Mapping(target = "valueMap.elementType", source = "valueMap.roomName")
    @Mapping(target = "valueMap.numberStock", source = "valueMap.breakfastCount", qualifiedByName = "toHotelBreakfast")
    @Mapping(target = "valueMap.divisionId", source = "valueMap.division.divisionId")
    @Mapping(target = "valueMap.city", source = "valueMap.division.divisionName")
    ElementVO<HotelElementValueMapVO> toHotelElementBO(ElementBO<HotelElementValueMapBO> valueMap);

    @Named("toHotelBreakfast")
    static String toNumberStock(Integer breakfastCount) {
        BreakFastTypeEnum breakFastTypeEnum = BreakFastTypeEnum.getByType(breakfastCount);
        if (Objects.isNull(breakFastTypeEnum)) {
            return "0";
        }
        return String.valueOf(breakFastTypeEnum.getType());
    }

    @Mapping(target = "elementValueMapList", source = "trafficElementList")
    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    TrafficElementGroupVO toTrafficElementGroupVO(TrafficElementGroupBO value);

    @Mapping(target = "elementValueMapList", source = "wifiElementList")
    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    WifiElementGroupVO toWifiElementGroupVO(WifiElementGroupBO value);

    @Mapping(target = "elementValueMapList", source = "phoneCardElementList")
    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    PhoneCardGroupVO toPhoneCardElementGroupVO(PhoneCardGroupBO value);

    @Mapping(target = "elementValueMapList", source = "caterElementList")
    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    CateringElementGroupVO toCateringElementGroupVO(CateringElementGroupBO value);

    @Mapping(target = "elementValueMapList", source = "specialActivityElementList")
    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    SpecialActivityElementGroupVO toSpecialActivityElementGroupVO(SpecialActivityElementGroupBO value);

    @Mapping(target = "elementValueMapList", source = "spaElementList")
    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    SpaElementGroupVO toSpaElementGroupVO(SpaElementGroupBO value);

    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    LocalTouristGuideElementGroupVO toTourElementGroupVO(LocalTouristGuideElementGroupBO value);


    @Mapping(target = "elementCategory", expression = "java(ElementCategoryEnum.getByType(value.getElementCategory()).getType())")
    PhotographyElementGroupVO toPhotographyElementGroupVO(PhotographyElementGroupBO value);

}
