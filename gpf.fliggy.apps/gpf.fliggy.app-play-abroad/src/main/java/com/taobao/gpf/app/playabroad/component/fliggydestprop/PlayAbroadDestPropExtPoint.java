package com.taobao.gpf.app.playabroad.component.fliggydestprop;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.EnumTextListPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 境外玩乐目的地组件扩展点
 */
@Slf4j
public class PlayAbroadDestPropExtPoint implements IInjectExtension {

    @PrepareForRender(key = "play-abroad-fliggyDestProp-set-required", desc = "设置目的地组件必填")
    public void setRequired(AbstractCompDO<List<TextValueModel>> compDO, @Category StdCategoryDO stdCategory) {
        if (compDO.getPagemodel() instanceof EnumTextListPageModel) {
            ((EnumTextListPageModel) compDO.getPagemodel()).setRequired(true);
        }
    }
} 