package com.taobao.gpf.app.playabroad.component;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.annotation.bind.User;
import com.alibaba.gpf.common.exception.ErrorCode;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.dataobject.ImageValueDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.utils.ImageUtil;
import com.taobao.uic.common.domain.BaseUserDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/25 00:29
 **/
public class PlayAbroadImagesExtPoint implements IInjectExtension {


    private static final Integer MAX_WIDTH = 800;
    private static final Integer MAX_HEIGHT = 800;

    @Check(key = "check-image-size", desc = "图片大小、数量校验")
    public CheckResult checkImageSize(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO category, @User BaseUserDO baseUserDO) {

        CheckResult result = new CheckResult();
        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID) {
            return result;
        }
        Object value = compDO.getValue();
        List<ImageValueDO> images = Lists.newArrayList();

        //安全操作
        if (!(value instanceof List)) {
            return result;
        }
        List<?> tempList = (List<?>) value;

        if (tempList.isEmpty() || tempList.get(0) instanceof ImageValueDO) {
            images = (List<ImageValueDO>) tempList;
        }
        List<String> imageUrls = images.stream().map(ImageValueDO::getUrl).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(imageUrls) || imageUrls.size() < 2) {
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("images",
                    "图片数量不得少于两张"));
            return result;
        }
        List<Boolean> imageCheckFailedBatchResult = ImageUtil.imageSizeLittleThanBatch(imageUrls, MAX_WIDTH, MAX_HEIGHT);
        boolean imageCheckFailed = imageCheckFailedBatchResult.stream().anyMatch(BooleanUtils::isTrue);
        if (imageCheckFailed) {
            ErrorCode errorCode = FliggyErrorEnum.IMAGE_SIZE_ERROR.getErrorCode();
            result.addErrorCode(errorCode);
        }
        return result;
    }


}
