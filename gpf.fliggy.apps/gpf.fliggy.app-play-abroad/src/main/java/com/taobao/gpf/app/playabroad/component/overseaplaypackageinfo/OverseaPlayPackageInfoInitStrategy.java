package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.domain.framework.compatibility.AbstractParentCompInitStrategy4Gpf3;

public class OverseaPlayPackageInfoInitStrategy extends AbstractParentCompInitStrategy4Gpf3<ParentCompDO<AbstractCompDO>, OverseaPlayPackageInfoListControlDO> {

    @Override
    public String getName() {
        return "overseaPlayPackageInfoInitStrategy";
    }

    @Override
    public ParentCompDO getInitCompDO() {
        ParentCompDO parentCompDO = new ParentCompDO<>();
        parentCompDO.setControlDO(new OverseaPlayPackageInfoListControlDO());
        return parentCompDO;
    }

}
