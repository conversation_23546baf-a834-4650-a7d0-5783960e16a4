package com.taobao.gpf.app.playabroad.component.packageelements.convert.vo2bo;

import com.alibaba.gpf.sdk.util.LogUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo.*;
import com.taobao.gpf.app.playabroad.constant.element.AvailableDateTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.CateringBusinessTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@Component
public class PlayElementVO2BOConvert {

    @Resource
    private PlayElementVO2BOConvertorHelper playElementVO2BOConvertorHelper;

    public List<BasePlayElementGroupBO> convert(List<BasePlayElementGroupVO> sourceData) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        List<BasePlayElementGroupBO> elementAfterParse = new ArrayList<>();
        for (BasePlayElementGroupVO value : sourceData) {
            Integer elementCategory = value.getElementCategory();
            ElementCategoryEnum elementCategoryEnum = ElementCategoryEnum.getByType(elementCategory);
            if (Objects.isNull(elementCategoryEnum)) {
                continue;
            }
            switch (elementCategoryEnum) {
                case TICKET:
                    elementAfterParse.add(this.toTicketElementGroupBO((TicketElementGroupVO) value));
                    break;
                case HOTEL:
                    elementAfterParse.add(this.toHotelElementGroupBO((HotelElementGroupVO) value));
                    break;
                case TRAFFIC:
                    elementAfterParse.add(this.toTrafficElementGroupBO((TrafficElementGroupVO) value));
                    break;
                case WIFI:
                    elementAfterParse.add(this.toWifiElementGroupBO((WifiElementGroupVO) value));
                    break;
                case PHONECARD:
                    elementAfterParse.add(this.toPhoneCardGroupBO((PhoneCardGroupVO) value));
                    break;
                case CATERING:
                    elementAfterParse.add(this.toCateringElementGroupBO((CateringElementGroupVO) value));
                    break;
                case SPECIAL_ACTIVITY:
                    elementAfterParse.add(this.toSpecialActivityElementGroupBO((SpecialActivityElementGroupVO) value));
                    break;
                case SPA:
                    elementAfterParse.add(this.toSpaElementGroupBO((SpaElementGroupVO) value));
                    break;
                case TOUR:
                    elementAfterParse.add(this.toLocalTouristGuideElementGroupBO((LocalTouristGuideElementGroupVO) value));
                    break;
                case PHOTOGRAPHY:
                    elementAfterParse.add(this.toPhotographyElementGroupBO((PhotographyElementGroupVO) value));
                    break;
                case DIVING:
                    elementAfterParse.add(this.toDivingElementGroupBO((DivingElementGroupVO) value));
                    break;
                case OTHER:
                    elementAfterParse.add(playElementVO2BOConvertorHelper.toBasePlayElementGroupBO(value));
                    break;
                default:
                    LogUtil.sysErrorLog("PlayElementVO2BOConvert.convert", "elementCategoryEnum is null");
                    break;
            }
        }
        return elementAfterParse;
    }

    private TicketElementGroupBO toTicketElementGroupBO(TicketElementGroupVO elementGroupVO) {
        TicketElementGroupBO ticketElementGroupBO = playElementVO2BOConvertorHelper.toTicketElementGroupBO(elementGroupVO);
        ticketElementGroupBO.setElementCategory(ElementCategoryEnum.TICKET.getType());

        if (CollectionUtils.isNotEmpty(ticketElementGroupBO.getTicketElementList())) {
            ticketElementGroupBO.getTicketElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> {
                        BeanUtils.copyProperties(elementGroupVO, bo);
                        bo.setDoesAppointEnterParkDate(NumberUtils.compare(elementGroupVO.getAvailableDateType(), AvailableDateTypeEnum.NO_NEED_CHOICE.getType()) == 0);
                    });
        }
        return ticketElementGroupBO;
    }

    private HotelElementGroupBO toHotelElementGroupBO(HotelElementGroupVO elementGroupVO) {
        HotelElementGroupBO hotelElementGroupBO = playElementVO2BOConvertorHelper.toHotelElementGroupBO(elementGroupVO);
        hotelElementGroupBO.setElementCategory(ElementCategoryEnum.HOTEL.getType());

        if (CollectionUtils.isNotEmpty(hotelElementGroupBO.getHotelElementList())) {
            hotelElementGroupBO.getHotelElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> {
                        BeanUtils.copyProperties(elementGroupVO, bo);
                        bo.setAdditionalRemarks(elementGroupVO.getAdditionalRule());
                    });
        }
        return hotelElementGroupBO;
    }

    private TrafficElementGroupBO toTrafficElementGroupBO(TrafficElementGroupVO elementGroupVO) {
        TrafficElementGroupBO trafficElementGroupBO = playElementVO2BOConvertorHelper.toTrafficElementGroupBO(elementGroupVO);
        trafficElementGroupBO.setElementCategory(ElementCategoryEnum.TRAFFIC.getType());

        if (CollectionUtils.isNotEmpty(trafficElementGroupBO.getTrafficElementList())) {
            trafficElementGroupBO.getTrafficElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> BeanUtils.copyProperties(elementGroupVO, bo));
        }
        return trafficElementGroupBO;
    }

    private WifiElementGroupBO toWifiElementGroupBO(WifiElementGroupVO elementGroupVO) {
        WifiElementGroupBO wifiElementGroupBO = playElementVO2BOConvertorHelper.toWifiElementGroupBO(elementGroupVO);
        wifiElementGroupBO.setElementCategory(ElementCategoryEnum.WIFI.getType());

        if (CollectionUtils.isNotEmpty(wifiElementGroupBO.getWifiElementList())) {
            wifiElementGroupBO.getWifiElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> bo.setAdditionalRemarks(elementGroupVO.getAdditionalRemarks()));
        }
        return wifiElementGroupBO;
    }

    private PhoneCardGroupBO toPhoneCardGroupBO(PhoneCardGroupVO elementGroupVO) {
        PhoneCardGroupBO phoneCardGroupBO = playElementVO2BOConvertorHelper.toPhoneCardElementGroupBO(elementGroupVO);
        phoneCardGroupBO.setElementCategory(ElementCategoryEnum.PHONECARD.getType());

        if (CollectionUtils.isNotEmpty(phoneCardGroupBO.getPhoneCardElementList())) {
            phoneCardGroupBO.getPhoneCardElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> BeanUtils.copyProperties(elementGroupVO, bo));
        }
        return phoneCardGroupBO;
    }

    private CateringElementGroupBO toCateringElementGroupBO(CateringElementGroupVO elementGroupVO) {
        CateringElementGroupBO cateringElementGroupBO = playElementVO2BOConvertorHelper.toCateringGroupBO(elementGroupVO);
        cateringElementGroupBO.setElementCategory(ElementCategoryEnum.CATERING.getType());

        if (Objects.equals(elementGroupVO.getBusinessType(), CateringBusinessTypeEnum.CATERING.getType())) {
            if (CollectionUtils.isNotEmpty(cateringElementGroupBO.getCaterElementList())) {
                cateringElementGroupBO.getCaterElementList().stream()
                        .filter(Objects::nonNull)
                        .map(ElementBO::getValueMap)
                        .forEach(bo -> BeanUtils.copyProperties(elementGroupVO, bo));
            }
        } else {
            ElementBO<CateringValueMapBO> elementBO = new ElementBO<>();
            List<ElementBO<CateringValueMapBO>> caterElementList = new ArrayList<>();

            CateringValueMapBO cateringValueMapBO = new CateringValueMapBO();
            cateringValueMapBO.setBusinessType(elementGroupVO.getBusinessType());
            cateringValueMapBO.setAdditionalRemarks(elementGroupVO.getAdditionalRemarks());
            if (Objects.nonNull(elementGroupVO.getPlayPoiVO())) {
                cateringValueMapBO.setPoiId(String.valueOf(elementGroupVO.getPlayPoiVO().getPoiId()));
                String poiName = Optional.ofNullable(elementGroupVO.getPlayPoiVO().getPoiValue())
                        .filter(t -> t.containsKey("poiName"))
                        .map(t -> t.get("poiName"))
                        .map(String::valueOf)
                        .orElse(null);
                cateringValueMapBO.setName(poiName);
                cateringValueMapBO.setPoiName(poiName);
            }

            elementBO.setValueMap(cateringValueMapBO);
            caterElementList.add(elementBO);
            cateringElementGroupBO.setCaterElementList(caterElementList);
        }
        return cateringElementGroupBO;
    }

    private SpaElementGroupBO toSpaElementGroupBO(SpaElementGroupVO elementGroupVO) {
        SpaElementGroupBO spaElementGroupBO = playElementVO2BOConvertorHelper.toSpaElementGroupBO(elementGroupVO);
        spaElementGroupBO.setElementCategory(ElementCategoryEnum.SPA.getType());

        if (CollectionUtils.isNotEmpty(spaElementGroupBO.getSpaElementList())) {
            spaElementGroupBO.getSpaElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> BeanUtils.copyProperties(elementGroupVO, bo));
        }
        return spaElementGroupBO;
    }

    private SpecialActivityElementGroupBO toSpecialActivityElementGroupBO(SpecialActivityElementGroupVO elementGroupVO) {
        SpecialActivityElementGroupBO specialActivityElementGroupBO = playElementVO2BOConvertorHelper.toSpecialActivityElementGroupBO(elementGroupVO);
        specialActivityElementGroupBO.setElementCategory(ElementCategoryEnum.SPECIAL_ACTIVITY.getType());

        if (CollectionUtils.isNotEmpty(specialActivityElementGroupBO.getSpecialActivityElementList())) {
            specialActivityElementGroupBO.getSpecialActivityElementList().stream()
                    .filter(Objects::nonNull)
                    .map(ElementBO::getValueMap)
                    .forEach(bo -> BeanUtils.copyProperties(elementGroupVO, bo));
        }
        return specialActivityElementGroupBO;
    }

    private LocalTouristGuideElementGroupBO toLocalTouristGuideElementGroupBO(LocalTouristGuideElementGroupVO elementGroupVO) {
        LocalTouristGuideElementGroupBO elementGroupBO = playElementVO2BOConvertorHelper.toLocalTouristGuideElementGroupBO(elementGroupVO);
        ElementBO<LocalTouristGuideValueMapBO> elementBO = new ElementBO<>();
        elementBO.setValueMap(playElementVO2BOConvertorHelper.toLocalTouristGuideValueMapBO(elementGroupVO));
        elementGroupBO.setLocalTouristGuideElementList(Lists.newArrayList(elementBO));
        elementGroupBO.setElementCategory(ElementCategoryEnum.TOUR.getType());
        return elementGroupBO;
    }

    private PhotographyElementGroupBO toPhotographyElementGroupBO(PhotographyElementGroupVO elementGroupVO) {
        PhotographyElementGroupBO elementGroupBO = playElementVO2BOConvertorHelper.toPhotographyElementGroupBO(elementGroupVO);
        ElementBO<PhotographyValueMapBO> elementBO = new ElementBO<>();
        elementGroupBO.setElementCategory(ElementCategoryEnum.PHOTOGRAPHY.getType());
        elementBO.setValueMap(playElementVO2BOConvertorHelper.toPhotographyValueMapBO(elementGroupVO));
        elementGroupBO.setPhotographyElementList(Lists.newArrayList(elementBO));
        return elementGroupBO;
    }

    private DivingElementGroupBO toDivingElementGroupBO(DivingElementGroupVO elementGroupVO) {
        DivingElementGroupBO divingElementGroupBO = new DivingElementGroupBO();
        List<DivingElementValueMapBO> divingElementBOList = new ArrayList<>();

        for (DivingElementVO divingElementVO : elementGroupVO.getDivingElementList()) {
            DivingElementValueMapBO elementGroupBO = playElementVO2BOConvertorHelper.toDivingElementGroupBO(elementGroupVO);
            elementGroupBO.setPoiRelated(String.valueOf(true));
            elementGroupBO.setValue(divingElementVO.getValue());
            elementGroupBO.setLabel(divingElementVO.getLabel());
            divingElementBOList.add(elementGroupBO);
        }
        divingElementGroupBO.setPlayTheme(elementGroupVO.getPlayTheme());
        divingElementGroupBO.setElementCategory(ElementCategoryEnum.DIVING.getType());
        divingElementGroupBO.setDivingElementList(divingElementBOList);
        return divingElementGroupBO;
    }

}