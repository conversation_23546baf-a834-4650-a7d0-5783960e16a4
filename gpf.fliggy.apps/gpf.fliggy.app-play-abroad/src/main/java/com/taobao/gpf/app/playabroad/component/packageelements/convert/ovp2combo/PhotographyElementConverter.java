package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.packageelements.PhotographyDeliveryTypeEnum;
import com.fliggy.vic.common.constant.packageelements.PhotographyFormEnum;
import com.fliggy.vic.common.constant.packageelements.PhotographyPromiseServiceTagEnum;
import com.fliggy.vic.common.constant.packageelements.PhotographyTypeEnum;
import com.fliggy.vic.common.shared.Specification;
import com.fliggy.vic.common.shared.TimeSpecification;
import com.fliggy.vic.common.util.StreamUtils;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.element.PhotographyElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.PhotographyNegativeNumberEnum;
import com.taobao.gpf.app.playabroad.constant.element.PhotographyTruingPhotoNumberEnum;
import com.taobao.gpf.app.playabroad.constant.element.PlayThemeEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/*
 * 旅拍
 */
@Component
public class PhotographyElementConverter extends AbstractElementConverter implements IElementConverter {

    // element key
    /**
     * 拍摄形式 ElementKey
     */
    public static final String PHOTOGRAPHY_FORM_ELEMENT_KEY = "PHOTOGRAPHY_FORM";

    /**
     * 拍摄风格 ElementKey
     */
    public static final String PHOTOGRAPHY_STYLE_ELEMENT_KEY = "PHOTOGRAPHY_STYLE";

    /**
     * 精修数量Desc ElementKey
     */
    public static final String TRUING_PHOTO_NUMBER_ELEMENT_DESC_KEY = "TRUING_PHOTO_NUMBER_DESC";

    /**
     * 精修数量Code ElementKey
     */
    public static final String TRUING_PHOTO_NUMBER_ELEMENT_CODE_KEY = "TRUING_PHOTO_NUMBER_CODE";

    /**
     * 拍摄天数 ElementKey
     */
    public static final String PHOTOGRAPHY_DAYS_ELEMENT_KEY = "PHOTOGRAPHY_DAYS";

    /**
     * 拍摄底片数量 ElementKey
     */
    public static final String NEGATIVE_NUMBER_ELEMENT_DESC_KEY = "NEGATIVE_NUMBER_DESC";

    /**
     * 拍摄底片数量 ElementKey
     */
    public static final String NEGATIVE_NUMBER_ELEMENT_CODE_KEY = "NEGATIVE_NUMBER_CODE";

    /**
     * 服务承诺list element key
     */
    public static final String PROMISE_SERVICE_TAG_LIST = "PROMISE_SERVICE_TAG_LIST";

    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.PHOTOGRAPHY.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.TRAVEL_PHOTOGRAPHY.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        PhotographyElement photographyElement = (PhotographyElement) pElement;
        Product product = super.fromOvpElement(photographyElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        // 拍摄地点
        Optional.ofNullable(photographyElement.getAddress()).ifPresent(address -> {
            elementFeatures.add(new ElementFeature("PHOTOGRAPHY_ADDRESS", address));
        });
        // 拍摄天数
        Optional.ofNullable(photographyElement.getSpecification()).map(Specification::getTimeSpecification).map(TimeSpecification::getNumber).ifPresent(number -> {
            elementFeatures.add(new ElementFeature("PHOTOGRAPHY_DAYS", String.valueOf(number)));
        });
        //outer_id
        Optional.ofNullable(photographyElement.getOuterAttributes())
                .map(outerAttributes -> outerAttributes.get("outerId"))
                .filter(StringUtils::isNotBlank)
                .ifPresent(outerCode -> {
                    elementFeatures.add(new ElementFeature("OUTER_ID", outerCode));
                    element.setOuterId(outerCode);
                });
        if (needBuildProductFeaturesFromPlayThemes(photographyElement)) {
            buildElementFeaturesFromPlayThemes(photographyElement.getPlayThemes(), elementFeatures, PlayThemeEnum.PHOTOGRAPHY_FORM);
            buildElementFeaturesFromPlayThemes(photographyElement.getPlayThemes(), elementFeatures, PlayThemeEnum.PHOTOGRAPHY_DELIVERY_TYPE);
            buildElementFeaturesFromPlayThemes(photographyElement.getPlayThemes(), elementFeatures, PlayThemeEnum.PHOTOGRAPHY_TYPE);
            buildElementFeaturesFromPlayThemes(photographyElement.getPlayThemes(), elementFeatures, PlayThemeEnum.NEGATIVE_NUMBER_DESC);
            buildElementFeaturesFromPlayThemes(photographyElement.getPlayThemes(), elementFeatures, PlayThemeEnum.TRUING_PHOTO_NUMBER_DES);
            buildElementFeaturesFromPlayThemes(photographyElement.getPlayThemes(), elementFeatures, PlayThemeEnum.PROMISE_SERVICE_TAG_LIST);
        } else {
            // 服务形式
            Optional.ofNullable(photographyElement.getPhotographyForm()).map(PhotographyFormEnum::getByCode).ifPresent(photographyFormEnum -> {
                elementFeatures.add(new ElementFeature(PHOTOGRAPHY_FORM_ELEMENT_KEY, photographyFormEnum.getDesc()));
                elementFeatures.add(new ElementFeature("ELEMENT_TYPE", photographyFormEnum.getDesc()));
            });
            // 出片效率
            Optional.ofNullable(photographyElement.getPhotographyDeliveryType()).map(PhotographyDeliveryTypeEnum::getByCode).ifPresent(photographyDeliveryTypeEnum -> {
                elementFeatures.add(new ElementFeature("PHOTOGRAPHY_DELIVERY_TYPE", photographyDeliveryTypeEnum.getDesc()));
            });
            // 旅拍类型
            Optional.ofNullable(photographyElement.getPhotographyType()).map(PhotographyTypeEnum::getByCode).ifPresent(photographyTypeEnum -> {
                elementFeatures.add(new ElementFeature("PHOTOGRAPHY_TYPE", photographyTypeEnum.getDesc()));
                elementFeatures.add(new ElementFeature("NAME", photographyTypeEnum.getDesc()));
            });
            // 拍摄底片
            Optional.ofNullable(photographyElement.getNegativeNumber())
                    .map(PhotographyNegativeNumberEnum::getByCode)
                    .ifPresent(e -> {
                        elementFeatures.add(new ElementFeature(NEGATIVE_NUMBER_ELEMENT_DESC_KEY, e.getDesc()));
                        elementFeatures.add(new ElementFeature(NEGATIVE_NUMBER_ELEMENT_CODE_KEY, String.valueOf(e.getType())));
                    });
            // 精修数量
            Optional.ofNullable(photographyElement.getTruingPhotoNumber())
                    .map(PhotographyTruingPhotoNumberEnum::getByCode)
                    .ifPresent(e -> {
                        elementFeatures.add(new ElementFeature(TRUING_PHOTO_NUMBER_ELEMENT_DESC_KEY, e.getDesc()));
                        elementFeatures.add(new ElementFeature(TRUING_PHOTO_NUMBER_ELEMENT_CODE_KEY, String.valueOf(e.getType())));
                    });
            // 服务承诺
            String promiseServiceTagList = StreamUtils.asStream(photographyElement.getPromiseServiceTags())
                    .filter(Objects::nonNull)
                    .map(PhotographyPromiseServiceTagEnum::getByCode)
                    .filter(Objects::nonNull)
                    .map(PhotographyPromiseServiceTagEnum::getDesc)
                    .collect(Collectors.joining(","));
            Optional.of(promiseServiceTagList).filter(StringUtils::isNotBlank).ifPresent(tags -> {
                elementFeatures.add(new ElementFeature(PROMISE_SERVICE_TAG_LIST, tags));
            });
        }

        // 服饰搭配
        Optional.ofNullable(photographyElement.getClothingNumber()).ifPresent(number -> {
            elementFeatures.add(new ElementFeature("CLOTHING_NUMBER", String.valueOf(number)));
        });

        // 拍摄风格
        Optional.ofNullable(photographyElement.getPhotographyStyle()).ifPresent(photographyStyle -> {
            elementFeatures.add(new ElementFeature(PHOTOGRAPHY_STYLE_ELEMENT_KEY, photographyStyle));
        });

        // 说明
        Optional.ofNullable(photographyElement.getAdditionalRemarks()).ifPresent(additionalRemarks -> {
            elementFeatures.add(new ElementFeature("DESC", additionalRemarks));
        });
        // 设置id
        setCustomProductIdAndElementId(product);
        return product;
    }
}
