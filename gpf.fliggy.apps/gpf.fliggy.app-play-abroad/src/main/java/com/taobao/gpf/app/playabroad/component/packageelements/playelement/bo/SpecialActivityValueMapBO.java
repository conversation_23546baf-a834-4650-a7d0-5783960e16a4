package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.constant.element.SpecialActivityTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpecialActivityValueMapBO extends BaseElementValueMapBO {

    /**
     * 活动类型
     * @see SpecialActivityTypeEnum
     */
    private Integer activityType;
}
