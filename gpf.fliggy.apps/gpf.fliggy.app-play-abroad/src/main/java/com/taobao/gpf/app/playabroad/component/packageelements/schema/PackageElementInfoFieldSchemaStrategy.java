package com.taobao.gpf.app.playabroad.component.packageelements.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.complexfield.ComplexFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.fliggy.travel.guide.param.playtype.PlayType;
import com.taobao.gpf.app.playabroad.constant.element.*;
import com.taobao.gpf.domain.constant.schema.SchemaFieldIdEnum;
import com.taobao.gpf.domain.repository.PlayTypeHelpServiceRepo;
import com.taobao.top.schema.depend.DependExpress;
import com.taobao.top.schema.depend.DependGroup;
import com.taobao.top.schema.enums.FieldTypeEnum;
import com.taobao.top.schema.enums.ValueTypeEnum;
import com.taobao.top.schema.factory.SchemaFactory;
import com.taobao.top.schema.field.*;
import com.taobao.top.schema.option.Option;
import com.taobao.top.schema.rule.DisableRule;
import com.taobao.top.schema.rule.RequiredRule;
import com.taobao.top.schema.rule.Rule;
import com.taobao.top.schema.rule.TipRule;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Component
public class PackageElementInfoFieldSchemaStrategy extends ComplexFieldSchemaStrategy<AbstractCompDO> {

    @Autowired
    private PlayTypeHelpServiceRepo playTypeHelpServiceRepo;

    @Override
    protected void setCompValue(AbstractCompDO compDO, ComplexField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

    }

    @Override
    protected void renderSchemaFieldValue(@NonNull ComplexField field, AbstractCompDO compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

    }

    @Override
    protected void customRenderField(ComplexField field, AbstractCompDO compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext config) {
        super.customRenderField(field, compDO, param, compConfig, config);

        //不同元素之间差异太大，需要每个元素都单独实现
        //酒店
        field.add(buildHotelProductFiled());

        //门票
        field.add(buildTicketProductFiled());

        //交通接驳
        field.add(buildTrafficProductFiled());

        //WI-FI
        field.add(buildWifiProductFiled());

        //电话卡
        field.add(buildPhoneCardProductFiled());

        //特色活动
        field.add(buildSpecialActivityProductFiled());

        //地陪元素
        field.add(buildTourProductFiled());

        //spa元素
        field.add(buildSpaProductFiled());

        //旅拍元素
        field.add(buildPhotographyProductFiled());

        //潜水元素
        field.add(buildDivingProductFiled());

        //美食元素
        field.add(buildCateringProductFiled());
    }

    private ComplexField buildCateringProductFiled() {
        ComplexField cateringProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        cateringProduct.setId(SchemaFieldIdEnum.CATERING_ELEMENT.getId());
        cateringProduct.setName(SchemaFieldIdEnum.CATERING_ELEMENT.getName());

        // 元素类型
        cateringProduct.add(buildElementCategory(ElementCategoryEnum.CATERING.getType()));

        // 补充说明
        cateringProduct.add(buildAdditionalRemarks());

        // 业务类型
        SingleCheckField businessType = (SingleCheckField) SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        businessType.setId(SchemaFieldIdEnum.CATERING_BUSINESS_TYPE.getId());
        businessType.setName(SchemaFieldIdEnum.CATERING_BUSINESS_TYPE.getName());
        List<Option> businessTypeOptions = Arrays.stream(CateringBusinessTypeEnum.values())
                .map(t -> {
                    Option option = new Option();
                    option.setValue(String.valueOf(t.getType()));
                    option.setDisplayName(t.getDesc());
                    return option;
                })
                .collect(Collectors.toList());
        businessType.setOptions(businessTypeOptions);
        cateringProduct.add(businessType);

        // POI信息 poiName本来应该是要放在poiValue里，这里简化一下schema
        ComplexField poiInfo = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        poiInfo.setId(SchemaFieldIdEnum.CATERING_POI.getId());
        poiInfo.setName(SchemaFieldIdEnum.CATERING_POI.getName());

        InputField poiId = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        poiId.setId(SchemaFieldIdEnum.CATERING_POI_ID.getId());
        poiId.setName(SchemaFieldIdEnum.CATERING_POI_ID.getName());
        poiId.addValueTypeRule(ValueTypeEnum.LONG);
        poiInfo.add(poiId);

        InputField poiName = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        poiName.setId(SchemaFieldIdEnum.CATERING_POI_NAME.getId());
        poiName.setName(SchemaFieldIdEnum.CATERING_POI_NAME.getName());
        poiName.addValueTypeRule(ValueTypeEnum.TEXT);
        poiInfo.add(poiName);

        cateringProduct.add(poiInfo);

        //添加元素，只有在业务类型为餐饮时才会有
        cateringProduct.add(buildCateringElementsField());

        // 添加玩法
        cateringProduct.add(buildPlayThemeField(ElementPlayThemeEnum.CATERING_ELEMENT_PLAY_THEME));

        return cateringProduct;
    }

    private ComplexField buildDivingProductFiled() {
        ComplexField divingProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        divingProduct.setId(SchemaFieldIdEnum.DIVING_ELEMENT.getId());
        divingProduct.setName(SchemaFieldIdEnum.DIVING_ELEMENT.getName());

        // 元素类型
        divingProduct.add(buildElementCategory(ElementCategoryEnum.DIVING.getType()));

        // 补充说明
        divingProduct.add(buildAdditionalRemarks());

        //潜店
        MultiComplexField divingElementList = (MultiComplexField) SchemaFactory.createField(FieldTypeEnum.MULTICOMPLEX);
        divingElementList.setId(SchemaFieldIdEnum.DIVING_ELEMENT_LIST.getId());
        divingElementList.setName(SchemaFieldIdEnum.DIVING_ELEMENT_LIST.getName());

        // 潜水元素名称
        InputField elementLabel = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        elementLabel.setId(SchemaFieldIdEnum.DIVING_ELEMENT_LABEL.getId());
        elementLabel.setName(SchemaFieldIdEnum.DIVING_ELEMENT_LABEL.getName());
        elementLabel.addValueTypeRule(ValueTypeEnum.TEXT);
        divingElementList.add(elementLabel);

        // 潜水元素POI ID
        InputField elementValue = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        elementValue.setId(SchemaFieldIdEnum.DIVING_ELEMENT_VALUE.getId());
        elementValue.setName(SchemaFieldIdEnum.DIVING_ELEMENT_VALUE.getName());
        elementValue.addValueTypeRule(ValueTypeEnum.LONG);
        divingElementList.add(elementValue);

        divingProduct.add(divingElementList);

        // 潜水次数
        InputField numberStock = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        numberStock.setId(SchemaFieldIdEnum.DIVING_NUMBER_STOCK.getId());
        numberStock.setName(SchemaFieldIdEnum.DIVING_NUMBER_STOCK.getName());
        numberStock.addValueTypeRule(ValueTypeEnum.INTEGER);
        divingProduct.add(numberStock);

        // 数量规格类型
        SingleCheckField type = (SingleCheckField) SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        type.setId(SchemaFieldIdEnum.DIVING_TYPE.getId());
        type.setName(SchemaFieldIdEnum.DIVING_TYPE.getName());
        List<Option> typeOptions = Arrays.stream(QuantitySpecificationEnum.values())
                .map(t -> {
                    Option option = new Option();
                    option.setValue(String.valueOf(t.getType()));
                    option.setDisplayName(t.getDesc());
                    return option;
                })
                .collect(Collectors.toList());
        type.setOptions(typeOptions);
        divingProduct.add(type);

        // 添加玩法
        divingProduct.add(buildPlayThemeField(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME));

        return divingProduct;
    }

    private ComplexField buildPhotographyProductFiled() {
        ComplexField photographyProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        photographyProduct.setId(SchemaFieldIdEnum.PHOTOGRAPHY_ELEMENT.getId());
        photographyProduct.setName(SchemaFieldIdEnum.PHOTOGRAPHY_ELEMENT.getName());

        // 元素类型
        photographyProduct.add(buildElementCategory(ElementCategoryEnum.PHOTOGRAPHY.getType()));

        // 补充说明
        photographyProduct.add(buildAdditionalRemarks());

        // 拍摄地点
        InputField address = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        address.setId(SchemaFieldIdEnum.PHOTOGRAPHY_ADDRESS.getId());
        address.setName(SchemaFieldIdEnum.PHOTOGRAPHY_ADDRESS.getName());
        address.addValueTypeRule(ValueTypeEnum.TEXT);
        photographyProduct.add(address);

        // 拍摄天数
        InputField days = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        days.setId(SchemaFieldIdEnum.PHOTOGRAPHY_DAYS.getId());
        days.setName(SchemaFieldIdEnum.PHOTOGRAPHY_DAYS.getName());
        days.addValueTypeRule(ValueTypeEnum.INTEGER);
        photographyProduct.add(days);

        // 服装数量
        InputField clothingNumber = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        clothingNumber.setId(SchemaFieldIdEnum.PHOTOGRAPHY_CLOTHING_NUMBER.getId());
        clothingNumber.setName(SchemaFieldIdEnum.PHOTOGRAPHY_CLOTHING_NUMBER.getName());
        clothingNumber.addValueTypeRule(ValueTypeEnum.INTEGER);
        photographyProduct.add(clothingNumber);

        // 外部ID
        InputField outerId = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        outerId.setId(SchemaFieldIdEnum.PHOTOGRAPHY_OUTER_ID.getId());
        outerId.setName(SchemaFieldIdEnum.PHOTOGRAPHY_OUTER_ID.getName());
        outerId.addValueTypeRule(ValueTypeEnum.TEXT);
        photographyProduct.add(outerId);

        // 拍摄风格
        InputField style = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        style.setId(SchemaFieldIdEnum.PHOTOGRAPHY_STYLE.getId());
        style.setName(SchemaFieldIdEnum.PHOTOGRAPHY_STYLE.getName());
        style.addValueTypeRule(ValueTypeEnum.TEXT);
        photographyProduct.add(style);

        // 添加玩法
        photographyProduct.add(buildPlayThemeField(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME));

        return photographyProduct;
    }

    private ComplexField buildTourProductFiled() {
        ComplexField tourProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        tourProduct.setId(SchemaFieldIdEnum.TOUR_ELEMENT.getId());
        tourProduct.setName(SchemaFieldIdEnum.TOUR_ELEMENT.getName());
        tourProduct.add(buildElementCategory(ElementCategoryEnum.TOUR.getType()));
        tourProduct.add(buildAdditionalRemarks());

        //语种
        MultiCheckField language = (MultiCheckField) SchemaFactory.createField(FieldTypeEnum.MULTICHECK);
        language.setId(SchemaFieldIdEnum.TOUR_LANGUAGE.getId());
        language.setName(SchemaFieldIdEnum.TOUR_LANGUAGE.getName());
        List<Option> languageOption = Arrays.stream(LanguageEnum.values())
                .map(t -> {
                    Option option = new Option();
                    option.setValue(t.getDesc());
                    option.setDisplayName(t.getDesc());
                    return option;
                })
                .collect(Collectors.toList());
        language.setOptions(languageOption);
        tourProduct.add(language);

        //数量
        InputField numberStock = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        numberStock.setId(SchemaFieldIdEnum.TOUR_NUMBER_STOCK.getId());
        numberStock.setName(SchemaFieldIdEnum.TOUR_NUMBER_STOCK.getName());
        numberStock.addValueTypeRule(ValueTypeEnum.INTEGER);
        tourProduct.add(numberStock);

        //数量规格类型
        SingleCheckField type = (SingleCheckField) SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        type.setId(SchemaFieldIdEnum.TOUR_TYPE.getId());
        type.setName(SchemaFieldIdEnum.TOUR_TYPE.getName());
        List<Option> typeOptions = Arrays.stream(QuantitySpecificationEnum.values())
                .map(t -> {
                    Option option = new Option();
                    option.setValue(String.valueOf(t.getType()));
                    option.setDisplayName(t.getDesc());
                    return option;
                })
                .collect(Collectors.toList());
        type.setOptions(typeOptions);
        tourProduct.add(type);

        //商家编码
        InputField outerId = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        outerId.setId(SchemaFieldIdEnum.TOUR_OUTER_ID.getId());
        outerId.setName(SchemaFieldIdEnum.TOUR_OUTER_ID.getName());
        outerId.addValueTypeRule(ValueTypeEnum.TEXT);
        tourProduct.add(outerId);

        //讲解类型
        SingleCheckField bizType = (SingleCheckField) SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        bizType.setId(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getId());
        bizType.setName(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getName());
        List<Option> bizTypeOptions = Arrays.stream(LocalTouristGuideBizTypeEnum.values())
                .map(t -> {
                    Option option = new Option();
                    option.setValue(t.name());
                    option.setDisplayName(t.name());
                    return option;
                })
                .collect(Collectors.toList());
        bizType.setOptions(bizTypeOptions);
        tourProduct.add(bizType);

        //dependRule
        Rule disableRule1 = new DisableRule("false");
        disableRule1.setDependGroup(new DependGroup());
        disableRule1.getDependGroup().addDependExpress(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getId(), LocalTouristGuideBizTypeEnum.EXPLAIN.name());

        Rule disableRule = new DisableRule("true");
        disableRule.setDependGroup(new DependGroup());
        DependExpress dependExpress = new DependExpress();
        dependExpress.setFieldId(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getId());
        dependExpress.setValue(LocalTouristGuideBizTypeEnum.EXPLAIN.name());
        dependExpress.setSymbol(DependExpress.SYMBOL_NOT_EQUALS);
        disableRule.getDependGroup().add(dependExpress);

        Rule requiredRule = new RequiredRule("true");
        requiredRule.setDependGroup(new DependGroup());
        requiredRule.getDependGroup().addDependExpress(SchemaFieldIdEnum.TOUR_BIZ_TYPE.getId(), LocalTouristGuideBizTypeEnum.EXPLAIN.name());

        //是否含门票
        SingleCheckField hasTicket = (SingleCheckField) SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        hasTicket.setId(SchemaFieldIdEnum.TOUR_HAS_TICKET.getId());
        hasTicket.setName(SchemaFieldIdEnum.TOUR_HAS_TICKET.getName());
        List<Option> hasTicketOptions = new ArrayList<>(2);
        Option yesOption = new Option();
        yesOption.setValue("true");
        yesOption.setDisplayName("含票");
        Option noOption = new Option();
        noOption.setValue("false");
        noOption.setDisplayName("不含票");
        hasTicketOptions.add(yesOption);
        hasTicketOptions.add(noOption);
        hasTicket.setOptions(hasTicketOptions);

        hasTicket.add(disableRule1);
        hasTicket.add(disableRule);
        hasTicket.add(requiredRule);

        tourProduct.add(hasTicket);

        //讲师ID
        InputField lecturerId = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        lecturerId.setId(SchemaFieldIdEnum.TOUR_RELATION_LECTURER_ID.getId());
        lecturerId.setName(SchemaFieldIdEnum.TOUR_RELATION_LECTURER_ID.getName());
        lecturerId.addValueTypeRule(ValueTypeEnum.TEXT);
        lecturerId.add(disableRule1);
        lecturerId.add(disableRule);
        tourProduct.add(lecturerId);

        //讲师昵称
        InputField lecturerNickName = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        lecturerNickName.setId(SchemaFieldIdEnum.TOUR_LECTURER_NICK_NAME.getId());
        lecturerNickName.setName(SchemaFieldIdEnum.TOUR_LECTURER_NICK_NAME.getName());
        lecturerNickName.addValueTypeRule(ValueTypeEnum.TEXT);
        lecturerNickName.add(disableRule1);
        lecturerNickName.add(disableRule);
        tourProduct.add(lecturerNickName);

        //讲师名字
        InputField lecturerRealName = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        lecturerRealName.setId(SchemaFieldIdEnum.TOUR_LECTURER_REAL_NAME.getId());
        lecturerRealName.setName(SchemaFieldIdEnum.TOUR_LECTURER_REAL_NAME.getName());
        lecturerRealName.addValueTypeRule(ValueTypeEnum.TEXT);
        lecturerRealName.add(disableRule1);
        lecturerRealName.add(disableRule);
        tourProduct.add(lecturerRealName);

        return tourProduct;
    }

    private ComplexField buildTrafficProductFiled() {
        ComplexField trafficProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        trafficProduct.setId(SchemaFieldIdEnum.TRAFFIC_ELEMENT.getId());
        trafficProduct.setName(SchemaFieldIdEnum.TRAFFIC_ELEMENT.getName());
        trafficProduct.add(buildElementCategory(ElementCategoryEnum.TRAFFIC.getType()));
        trafficProduct.add(buildAdditionalRemarks());
        trafficProduct.add(buildElementsField());
        return trafficProduct;
    }

    private ComplexField buildTicketProductFiled() {
        ComplexField ticketProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        ticketProduct.setId(SchemaFieldIdEnum.TICKET_ELEMENT.getId());
        ticketProduct.setName(SchemaFieldIdEnum.TICKET_ELEMENT.getName());

        //元素类型
        ticketProduct.add(buildElementCategory(ElementCategoryEnum.TICKET.getType()));

        //补充说明
        ticketProduct.add(buildAdditionalRemarks());

        //门票元素
        ticketProduct.add(buildElementsField());

        return ticketProduct;
    }

    private ComplexField buildHotelProductFiled() {
        ComplexField hotelProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        hotelProduct.setId(SchemaFieldIdEnum.HOTEL_ELEMENT.getId());
        hotelProduct.setName(SchemaFieldIdEnum.HOTEL_ELEMENT.getName());

        //元素类型
        hotelProduct.add(buildElementCategory(ElementCategoryEnum.HOTEL.getType()));

        //补充说明
        hotelProduct.add(buildAdditionalRemarks());

        //最早入住时间
        InputField earliestCheckInTime = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        earliestCheckInTime.setId(SchemaFieldIdEnum.EARLIEST_CHECK_IN_TIME.getId());
        earliestCheckInTime.setName(SchemaFieldIdEnum.EARLIEST_CHECK_IN_TIME.getName());
        earliestCheckInTime.addValueTypeRule(ValueTypeEnum.TEXT);
        earliestCheckInTime.add(new TipRule("值格式：HH:mm"));
        hotelProduct.add(earliestCheckInTime);

        //最晚退房时间
        InputField latestCheckOutTime = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        latestCheckOutTime.setId(SchemaFieldIdEnum.LATEST_CHECK_OUT_TIME.getId());
        latestCheckOutTime.setName(SchemaFieldIdEnum.LATEST_CHECK_OUT_TIME.getName());
        latestCheckOutTime.addValueTypeRule(ValueTypeEnum.TEXT);
        latestCheckOutTime.add(new TipRule("值格式：HH:mm"));
        hotelProduct.add(latestCheckOutTime);

        //酒店元素
        hotelProduct.add(buildElementsField());

        return hotelProduct;
    }

    private ComplexField buildWifiProductFiled() {
        ComplexField wifiProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        wifiProduct.setId(SchemaFieldIdEnum.WIFI_ELEMENT.getId());
        wifiProduct.setName(SchemaFieldIdEnum.WIFI_ELEMENT.getName());
        wifiProduct.add(buildElementCategory(ElementCategoryEnum.WIFI.getType()));
        wifiProduct.add(buildAdditionalRemarks());
        wifiProduct.add(buildElementsField());
        return wifiProduct;
    }

    private ComplexField buildPhoneCardProductFiled() {
        ComplexField phoneCardProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        phoneCardProduct.setId(SchemaFieldIdEnum.PHONECARD_ELEMENT.getId());
        phoneCardProduct.setName(SchemaFieldIdEnum.PHONECARD_ELEMENT.getName());
        phoneCardProduct.add(buildElementCategory(ElementCategoryEnum.PHONECARD.getType()));
        phoneCardProduct.add(buildAdditionalRemarks());
        phoneCardProduct.add(buildElementsField());
        return phoneCardProduct;
    }

    private ComplexField buildSpecialActivityProductFiled() {
        ComplexField specialActivityProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        specialActivityProduct.setId(SchemaFieldIdEnum.SPECIAL_ACTIVITY_ELEMENT.getId());
        specialActivityProduct.setName(SchemaFieldIdEnum.SPECIAL_ACTIVITY_ELEMENT.getName());
        specialActivityProduct.add(buildElementCategory(ElementCategoryEnum.SPECIAL_ACTIVITY.getType()));
        specialActivityProduct.add(buildAdditionalRemarks());
        specialActivityProduct.add(buildElementsField());
        return specialActivityProduct;
    }

    /**
     * 构建SPA元素Field
     *
     * @return SPA元素ComplexField
     */
    private ComplexField buildSpaProductFiled() {
        ComplexField spaProduct = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        spaProduct.setId(SchemaFieldIdEnum.SPA_ELEMENT.getId());
        spaProduct.setName(SchemaFieldIdEnum.SPA_ELEMENT.getName());

        // 元素类型
        spaProduct.add(buildElementCategory(ElementCategoryEnum.SPA.getType()));

        // 补充说明
        spaProduct.add(buildAdditionalRemarks());

        // SPA店用途范围
        SingleCheckField spaUsageScope = (SingleCheckField) SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        spaUsageScope.setId(SchemaFieldIdEnum.SPA_USAGE_SCOPE.getId());
        spaUsageScope.setName(SchemaFieldIdEnum.SPA_USAGE_SCOPE.getName());
        List<Option> spaUsages = Arrays.stream(SpaUsageScopeEnum.values())
                .map(t -> {
                    Option option = new Option();
                    option.setValue(String.valueOf(t.getType()));
                    option.setDisplayName(t.getDesc());
                    return option;
                })
                .collect(Collectors.toList());
        spaUsageScope.setOptions(spaUsages);
        spaProduct.add(spaUsageScope);

        // SPA元素列表
        spaProduct.add(buildElementsField());

        //玩法
        spaProduct.add(buildPlayThemeField(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME));

        return spaProduct;
    }

    private static InputField buildElementCategory(int elementCategory) {
        InputField elementTypeField = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        elementTypeField.setId(SchemaFieldIdEnum.ELEMENT_CATEGORY.getId());
        elementTypeField.setName(SchemaFieldIdEnum.ELEMENT_CATEGORY.getName());
        elementTypeField.addValueTypeRule(ValueTypeEnum.INTEGER);
        elementTypeField.setValue(String.valueOf(elementCategory));
        elementTypeField.add(new TipRule("元素类型, 有默认value值，直接使用不允许修改"));
        return elementTypeField;
    }

    private MultiComplexField buildElementsField() {
        MultiComplexField elementField = (MultiComplexField) SchemaFactory.createField(FieldTypeEnum.MULTICOMPLEX);
        elementField.setId(SchemaFieldIdEnum.ELEMENT_LIST.getId());
        elementField.setName(SchemaFieldIdEnum.ELEMENT_LIST.getName());

        //元素id
        elementField.add(buildElementIdField());

        //元素类型
        elementField.add(buildElementTypeField());

        //元素特征
        elementField.add(buildElementFeatures());
        return elementField;
    }

    private MultiComplexField buildCateringElementsField() {

        //dependRule
        Rule disableRule1 = new DisableRule("false");
        disableRule1.setDependGroup(new DependGroup());
        disableRule1.getDependGroup().addDependExpress(SchemaFieldIdEnum.CATERING_BUSINESS_TYPE.getId(), String.valueOf(CateringBusinessTypeEnum.CATERING.getType()));

        Rule disableRule = new DisableRule("true");
        disableRule.setDependGroup(new DependGroup());
        DependExpress dependExpress = new DependExpress();
        dependExpress.setFieldId(SchemaFieldIdEnum.CATERING_BUSINESS_TYPE.getId());
        dependExpress.setValue(String.valueOf(CateringBusinessTypeEnum.CATERING.getType()));
        dependExpress.setSymbol(DependExpress.SYMBOL_NOT_EQUALS);
        disableRule.getDependGroup().add(dependExpress);

        MultiComplexField cateringElementField = buildElementsField();

        cateringElementField.add(disableRule);
        cateringElementField.add(disableRule1);
        return cateringElementField;
    }

    private static InputField buildElementIdField() {
        InputField elementId = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        elementId.setId(SchemaFieldIdEnum.ELEMENT_ID.getId());
        elementId.setName(SchemaFieldIdEnum.ELEMENT_ID.getName());
        elementId.addValueTypeRule(ValueTypeEnum.LONG);
        return elementId;
    }

    private static InputField buildElementTypeField() {
        InputField elementType = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        elementType.setId(SchemaFieldIdEnum.ELEMENT_TYPE.getId());
        elementType.setName(SchemaFieldIdEnum.ELEMENT_TYPE.getName());
        elementType.addValueTypeRule(ValueTypeEnum.INTEGER);
        return elementType;
    }

    private MultiComplexField buildElementFeatures() {
        MultiComplexField elementFeatures = (MultiComplexField) SchemaFactory.createField(FieldTypeEnum.MULTICOMPLEX);

        elementFeatures.setId(SchemaFieldIdEnum.ELEMENT_FEATURES.getId());
        elementFeatures.setName(SchemaFieldIdEnum.ELEMENT_FEATURES.getName());

        InputField featureKey = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        featureKey.setId(SchemaFieldIdEnum.FEATURE_KEY.getId());
        featureKey.setName(SchemaFieldIdEnum.FEATURE_KEY.getName());
        featureKey.addValueTypeRule(ValueTypeEnum.TEXT);
        elementFeatures.add(featureKey);

        InputField featureValue = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        featureValue.setId(SchemaFieldIdEnum.FEATURE_VALUE.getId());
        featureValue.setName(SchemaFieldIdEnum.FEATURE_VALUE.getName());
        featureValue.addValueTypeRule(ValueTypeEnum.TEXT);
        elementFeatures.add(featureValue);

        return elementFeatures;
    }

    /**
     * 元素的玩法
     *
     * @return
     */
    private ComplexField buildPlayThemeField(ElementPlayThemeEnum byElementId) {
        ComplexField playTheme = (ComplexField) SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        playTheme.setId(SchemaFieldIdEnum.PLAY_THEME.getId());
        playTheme.setName(SchemaFieldIdEnum.PLAY_THEME.getName());

        //TODO 玩法playThemeProps，先不给，因为值是后端固定的，在schema setCompDO的时候赋值上

        PlayType playType = playTypeHelpServiceRepo.getPlayType(byElementId.getPlayThemeId());

        //玩法属性内容
        MultiComplexField playThemeValue = (MultiComplexField) SchemaFactory.createField(FieldTypeEnum.MULTICOMPLEX);
        playThemeValue.setId(SchemaFieldIdEnum.PLAY_THEME_VALUE.getId());
        playThemeValue.setName(SchemaFieldIdEnum.PLAY_THEME_VALUE.getName());
        playTheme.add(playThemeValue);

        //key
        InputField playThemeValueKey = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        playThemeValueKey.setId(SchemaFieldIdEnum.PLAY_THEME_VALUE_KEY.getId());
        playThemeValueKey.setName(SchemaFieldIdEnum.PLAY_THEME_VALUE_KEY.getName());
        playThemeValueKey.addValueTypeRule(ValueTypeEnum.LONG);
        playThemeValueKey.setValue(playType.getId().toString());
        playThemeValueKey.add(new TipRule("玩法id, 有默认value值，直接使用不允许修改"));
        playThemeValue.add(playThemeValueKey);

        //tab
        InputField tab = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        tab.setId(SchemaFieldIdEnum.PLAY_THEME_VALUE_TAB.getId());
        tab.setName(SchemaFieldIdEnum.PLAY_THEME_VALUE_TAB.getName());
        tab.addValueTypeRule(ValueTypeEnum.TEXT);
        tab.setValue(playType.getName());
        tab.add(new TipRule("玩法名称, 有默认value值，直接使用不允许修改"));
        playThemeValue.add(tab);

        //玩法属性值
        playThemeValue.add(buildProperties());
        return playTheme;
    }

    private MultiComplexField buildProperties() {
        MultiComplexField properties = new MultiComplexField();
        properties.setId(SchemaFieldIdEnum.PLAY_PROPERTIES.getId());
        properties.setName(SchemaFieldIdEnum.PLAY_PROPERTIES.getName());

        InputField id = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        id.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_ID.getId());
        id.setName(SchemaFieldIdEnum.PLAY_ATTRIBUTE_ID.getName());
        id.addValueTypeRule(ValueTypeEnum.LONG);
        properties.add(id);

        InputField name = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        name.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_NAME.getId());
        name.setName(SchemaFieldIdEnum.PLAY_ATTRIBUTE_NAME.getName());
        name.addValueTypeRule(ValueTypeEnum.TEXT);
        properties.add(name);

        InputField type = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        type.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_TYPE.getId());
        type.setName(SchemaFieldIdEnum.PLAY_ATTRIBUTE_TYPE.getName());
        type.addValueTypeRule(ValueTypeEnum.INTEGER);
        properties.add(type);

        InputField value = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        value.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_VALUE.getId());
        value.setName(SchemaFieldIdEnum.PLAY_ATTRIBUTE_VALUE.getName());
        value.addValueTypeRule(ValueTypeEnum.TEXT);
        properties.add(value);

        InputField isOther = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        isOther.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_IS_OTHER.getId());
        isOther.setName(SchemaFieldIdEnum.PLAY_ATTRIBUTE_IS_OTHER.getName());
        isOther.addValueTypeRule(ValueTypeEnum.INTEGER);
        properties.add(isOther);

        InputField otherVal = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        otherVal.setId(SchemaFieldIdEnum.PLAY_ATTRIBUTE_OTHER_VAL.getId());
        otherVal.setName(SchemaFieldIdEnum.PLAY_ATTRIBUTE_OTHER_VAL.getName());
        otherVal.addValueTypeRule(ValueTypeEnum.TEXT);
        properties.add(otherVal);

        return properties;
    }

    /**
     * 每个元素都有
     *
     * @return
     */
    private Field buildAdditionalRemarks() {
        //补充说明
        InputField additionalRemarks = (InputField) SchemaFactory.createField(FieldTypeEnum.INPUT);
        additionalRemarks.setId(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getId());
        additionalRemarks.setName(SchemaFieldIdEnum.ADDITIONAL_REMARKS.getName());
        additionalRemarks.addValueTypeRule(ValueTypeEnum.TEXT);

        return additionalRemarks;
    }

    @Override
    public String getName() {
        return "packageElementInfoFieldSchemaStrategy";
    }
}
