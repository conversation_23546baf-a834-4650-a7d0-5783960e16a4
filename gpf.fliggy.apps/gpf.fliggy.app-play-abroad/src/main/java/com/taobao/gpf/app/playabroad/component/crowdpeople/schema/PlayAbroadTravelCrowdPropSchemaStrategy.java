package com.taobao.gpf.app.playabroad.component.crowdpeople.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.singlecheckfield.SingleCheckFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.top.schema.field.SingleCheckField;
import com.taobao.top.schema.option.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 境外玩乐业务身份下travelCrowdProp组件的schema策略实现
 */
@Slf4j
@Component
public class PlayAbroadTravelCrowdPropSchemaStrategy extends SingleCheckFieldSchemaStrategy<BasicCompDO<Long>> {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public String getName() {
        return "playAbroadTravelCrowdPropSchemaStrategy";
    }

    @Override
    protected void setCompValue(BasicCompDO<Long> compDO, SingleCheckField field, CompExtParam param, 
                               AdapterCompConfig compConfig, SchemaParseContext context) {
        if (field.getValue() == null || StringUtils.isBlank(field.getValue().getValue())) {
            return;
        }
        
        try {
            Long value = Long.parseLong(field.getValue().getValue());
            compDO.setValue(value);
        } catch (NumberFormatException e) {
            log.error("Parse travelCrowdProp value error, value: {}", field.getValue().getValue(), e);
        }
    }

    @Override
    protected void renderSchemaFieldValue(SingleCheckField field, BasicCompDO<Long> compDO, CompExtParam param, 
                                         AdapterCompConfig compConfig, SchemaParseContext context) {
        if (compDO.getValue() == null) {
            return;
        }
        
        field.setValue(String.valueOf(compDO.getValue()));
    }

    @Override
    protected void customRenderField(SingleCheckField field, BasicCompDO<Long> compDO, CompExtParam param, 
                                    AdapterCompConfig compConfig, SchemaParseContext context) {
        super.customRenderField(field, compDO, param, compConfig, context);
        
        // 获取人群选项
        long categoryId = FliggyParamUtil.getCategoryId(param);
        List<Option> options = getOptions(categoryId);
        if (CollectionUtils.isNotEmpty(options)) {
            field.setOptions(options);
        }
    }
    
    /**
     * 获取出游人群选项
     */
    private List<Option> getOptions(long categoryId) {
        List<Option> options = new ArrayList<>();
        
        try {
            // 获取出游人群属性值列表
            Map<Long, String> valueMap = travelSellConfig.getTravelForestCache().getAllPropsVidToVnameMap()
                .get(categoryId).get(travelSellConfig.getSuitablePeopleId());
            
            if (valueMap != null && !valueMap.isEmpty()) {
                for (Map.Entry<Long, String> entry : valueMap.entrySet()) {
                    Option option = new Option();
                    option.setDisplayName(entry.getValue());
                    option.setValue(String.valueOf(entry.getKey()));
                    options.add(option);
                }
            }
        } catch (Exception e) {
            log.error("Get travelCrowdProp options error, categoryId: {}", categoryId, e);
        }
        
        return options;
    }
} 