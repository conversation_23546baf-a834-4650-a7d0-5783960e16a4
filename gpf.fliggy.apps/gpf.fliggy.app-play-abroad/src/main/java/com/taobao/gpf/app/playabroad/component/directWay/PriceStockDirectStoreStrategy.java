package com.taobao.gpf.app.playabroad.component.directWay;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.constant.item.AlitripFeatureConstants;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

import java.util.Objects;

public class PriceStockDirectStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<Integer>> {

    @Override
    public String getName() {
        return "priceStockDirectStoreStrategy";
    }

    @Override
    public void parseStore(BasicCompDO<Integer> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        // 根据transferStore的逻辑反解析商品数据到组件value中
        // 1. 解析AlitripFeatureConstants.PRICE_STOCK_DIRECT字段，若为"1"则为直连，否则为非直连
        // 2. 组件值为1表示系统直连，0表示非直连
        String priceStockDirect = storeDO.getAlitripFeatures().get(AlitripFeatureConstants.PRICE_STOCK_DIRECT);
        if ("1".equals(priceStockDirect) || Boolean.TRUE.equals(storeDO.getSystemVendorDirectConfiguration().getDoesOpenItemDirect())) {
            compDO.setValue(1);
        } else {
            compDO.setValue(0);
        }
    }

    @Override
    public void transferStore(BasicCompDO<Integer> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
       storeDO.getAlitripFeatures().put(AlitripFeatureConstants.PRICE_STOCK_DIRECT, "1");
       storeDO.getSystemVendorDirectConfiguration().setDoesOpenItemDirect(Objects.equals(compDO.getValue(), 1));
    }


    
}
