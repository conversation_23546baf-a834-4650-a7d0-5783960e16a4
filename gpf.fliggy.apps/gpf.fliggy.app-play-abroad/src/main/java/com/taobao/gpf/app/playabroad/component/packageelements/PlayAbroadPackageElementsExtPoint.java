package com.taobao.gpf.app.playabroad.component.packageelements;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.Enable;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.EnumValueListPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.model.pagemodel.PageValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.app.playabroad.config.FliggyPlayAbroadSwitchConfig;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.repository.PlayTypeHelpServiceRepo;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 境外玩乐套餐搭配元素组件扩展点
 */
@Slf4j
public class PlayAbroadPackageElementsExtPoint implements IInjectExtension {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Resource
    private PlayTypeHelpServiceRepo playTypeHelpServiceRepo;

    /**
     * 套餐元素发布提示
     */
    private static final List<String> ELEMENT_PUBLISH_TIPS = Lists
        .newArrayList("<span style=\"color:#ff1818\">住宿、玩乐、餐饮三种元素会在搜索结果页展示出来，有助于提升转化，请认真配置</span>");
    
    @PrepareForRender(key = "play-abroad-set-packageElements-required", desc = "设置套餐搭配元素组件必填")
    public void setRequired(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (compDO.getPagemodel() instanceof BasicPageModel) {
            BasicPageModel pageModel = (BasicPageModel) compDO.getPagemodel();
            pageModel.setRequired(true);
            
            // 添加提示信息
            InfoModel infoModel = pageModel.getInfo();
            if (infoModel == null) {
                infoModel = new InfoModel();
                pageModel.setInfo(infoModel);
            }
            infoModel.setBottom(ELEMENT_PUBLISH_TIPS);
        }
    }
    
    @Enable(key = "play-abroad-packageElements-enable", desc = "套餐搭配元素组件启用")
    public boolean enableComponent(CompExtParam param, @Category StdCategoryDO categoryDO) {
        // 获取类目的ID
        long catId = categoryDO.getCategoryId();
        Long sellerId = FliggyParamUtil.getUserDO(param).getUserId();
        //境外玩乐套餐  *********
        if(!travelSellConfig.isJingwaiWanletaocan(catId)) {
            return false;
        }
        // 对于境外玩乐类目，启用套餐搭配元素组件
        return true;
    }

    @PrepareForRender(key = "play-abroad-set-packageElements-datasource", desc = "设置套餐搭配元素数据源")
    public void setDataSource(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (compDO.getPagemodel() instanceof EnumValueListPageModel) {
            EnumValueListPageModel pageModel = (EnumValueListPageModel) compDO.getPagemodel();
            pageModel.setDataSource(buildDataSource());
        }
    }

    private List<PageValueModel> buildDataSource() {
        return FliggyPlayAbroadSwitchConfig.packageElements.stream()
                .map(e -> {
                    PageValueModel pageValueModel = new PageValueModel();
                    ElementCategoryEnum elementCategoryEnum = ElementCategoryEnum.getByType(e);
                    if (elementCategoryEnum != null) {

                        pageValueModel.setValue(elementCategoryEnum.getType().longValue());
                        pageValueModel.setText(elementCategoryEnum.getValue());
                        return pageValueModel;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

} 