package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.packagedirect.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.complexfield.ComplexFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.pricestock.schema.PlayAbroadCalendarPriceStockSchemaStrategy;
import com.taobao.top.schema.field.ComplexField;
import com.taobao.top.schema.rule.TipRule;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
public class PlayAbroadPackageDirectInfoSchemaStrategy extends ComplexFieldSchemaStrategy<BasicCompDO<String>> {


    @Override
    public String getName() {
        return "playAbroadPackageDirectInfoSchemaStrategy";
    }

    @Override
    protected void customRenderField(ComplexField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext config) {
        super.customRenderField(field, compDO, param, compConfig, config);
        field.add(new TipRule("当 sellType=4 且 (priceStockDirect=1 || orderDirect=1 ) 时，该字段必填, 其他情况不必填"));

        PlayAbroadCalendarPriceStockSchemaStrategy.addDirectInfo(field);
    }

    @Override
    protected void setCompValue(BasicCompDO<String> compDO, ComplexField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

    }

    @Override
    protected void renderSchemaFieldValue(@NonNull ComplexField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

    }
}
