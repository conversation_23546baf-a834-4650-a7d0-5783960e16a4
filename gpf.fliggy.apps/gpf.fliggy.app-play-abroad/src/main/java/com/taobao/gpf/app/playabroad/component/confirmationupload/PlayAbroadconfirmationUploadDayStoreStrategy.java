package com.taobao.gpf.app.playabroad.component.confirmationupload;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.compdo.TabbedCompDO;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.common.constants.CommonConstants;
import com.taobao.gpf.domain.component.confirmationuploadday.ConfirmationUploadDayStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Objects;

public class PlayAbroadconfirmationUploadDayStoreStrategy extends ConfirmationUploadDayStoreStrategy{

    @Override
    public String getName() {
        return "playAbroadconfirmationUploadDayStoreStrategy";
    }

    @Override
    public void parseStore(TabbedCompDO<List<TextValueModel>> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        AbstractCompDO selectCompDO = compDO.getNavCompDO();
        if (selectCompDO == null) {
            return;
        }

        String dayStr = storeDO.getAlitripFeatures().get(CommonConstants.CONFIRMATION_DAY);
        if (NumberUtils.isDigits(dayStr)) {
            Integer day = Integer.parseInt(dayStr);
            if (day == 5) {
                selectCompDO.setValue("trip5Days");
            } else {
                selectCompDO.setValue("customUpload");
                setUploadTimeDay(compDO, day);
            }
        } else {
            selectCompDO.setValue("noUpload");
        }
    }

    @Override
    public void transferStore(TabbedCompDO<List<TextValueModel>> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        AbstractCompDO selectCompDO = compDO.getNavCompDO();
        if (selectCompDO == null || selectCompDO.getValue() == null) {
            return;
        }
        String value = String.valueOf(selectCompDO.getValue());
        if (StringUtils.equals(value, "trip5Days")) {
            storeDO.getAlitripFeatures().put(CommonConstants.CONFIRMATION_DAY, "5");
        } else if (StringUtils.equals(value,"customUpload")) {
            
            if(selectCompDO instanceof ParentCompDO){
                List<BasicCompDO> children = ((ParentCompDO)selectCompDO).getChildren();
                if(CollectionUtils.isNotEmpty(children)){
                    if(Objects.nonNull(children.get(0).getValue())){
                        storeDO.getAlitripFeatures().put(CommonConstants.CONFIRMATION_DAY, String.valueOf(children.get(0).getValue()));
                    }
                }
            }
            
        }
    }

    public void setUploadTimeDay(TabbedCompDO<List<TextValueModel>> compDO, int day) {
        if (compDO != null && CollectionUtils.isNotEmpty(compDO.getChildren())) {
            for (AbstractCompDO abstractCompDO : compDO.getChildren()) {
                if (!(abstractCompDO instanceof ParentCompDO)) {
                    continue;
                }
                ParentCompDO parentCompDO = (ParentCompDO) abstractCompDO;
                if (!"customUpload".equals(parentCompDO.getCompName()) || CollectionUtils.isEmpty(parentCompDO.getChildren())) {
                    continue;
                }

                BasicCompDO<String> customDayCompDO = (BasicCompDO<String>) parentCompDO.getChildren().get(0);
                
                customDayCompDO.setValue(String.valueOf(day));
            }
        }
    }
    
}
