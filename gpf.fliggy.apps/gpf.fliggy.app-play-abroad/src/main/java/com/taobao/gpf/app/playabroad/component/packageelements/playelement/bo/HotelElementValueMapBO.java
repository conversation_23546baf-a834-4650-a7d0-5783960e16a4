package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotelElementValueMapBO extends BaseElementValueMapBO {
    /**
     * 酒店
     */
    private HotelBO hotelBO;
    /**
     * 酒店房型
     */
    private RoomBO roomBO;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 酒店地址
     */
    private String address;

    /**
     * 早餐数量
     */
    private Integer breakfastCount;

    /**
     * 最早入住时间
     */
    private String earliestCheckInTime;

    /**
     * 最晚入住时间
     */
    private String latestCheckOutTime;

    /**
     * 间夜数
     */
    private Integer nightNumber;
}
