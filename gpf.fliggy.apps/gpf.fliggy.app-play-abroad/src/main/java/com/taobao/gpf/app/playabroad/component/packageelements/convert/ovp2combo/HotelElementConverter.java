package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.shared.Specification;
import com.fliggy.vic.common.shared.TimeSpecification;
import com.fliggy.vpp.client.dto.response.line.element.HotelElement;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import com.taobao.travel.client.domain.dataobject.structured.ProductFeature;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * author: 指瑕
 */
@Component
public class HotelElementConverter extends AbstractElementConverter implements IElementConverter {

    private static final String HOTEL_BREAKFAST = "HOTEL_BREAKFAST";
    private static final String LATEST_CHECK_OUT_TIME = "LATEST_CHECK_OUT_TIME";
    private static final String EARLIEST_CHECK_IN_TIME = "EARLIEST_CHECK_IN_TIME";

    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.HOTEL.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.HOTEL.getType();
    }

    @Override
    public HotelElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        Element element = product.getElement();
        HotelElement hotelElement = new HotelElement();

        return hotelElement;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        Product product = super.fromOvpElement(pElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        HotelElement hotelElement = (HotelElement) pElement;
        //城市
        if (Objects.nonNull(hotelElement.getDivision())) {
            elementFeatures.add(new ElementFeature("CITY", hotelElement.getDivision().getDivisionName()));
            elementFeatures.add(new ElementFeature("DIVISION_ID", hotelElement.getDivision().getDivisionId()));
        }
        // 酒店标准库信息
        if (Objects.nonNull(hotelElement.getHotel())) {
            elementFeatures.add(new ElementFeature("SHID", String.valueOf(hotelElement.getHotel().getHotelId())));
            elementFeatures.addAll(fromPoiDTO(hotelElement.getHotel().getPoi()));
        }
        if (Objects.nonNull(hotelElement.getRoom())) {
            elementFeatures.add(new ElementFeature("ROOM_TYPE_ID", String.valueOf(hotelElement.getRoom().getId())));
        }
        elementFeatures.add(new ElementFeature("ELEMENT_TYPE", hotelElement.getRoomName()));
        //NAME
        elementFeatures.add(new ElementFeature("NAME", hotelElement.getHotelName()));
        //商家编码
        elementFeatures.add(new ElementFeature("OUTER_ID", hotelElement.getOuterCode()));

        List<ProductFeature> productFeatures = product.getProductFeatures();
        Optional.ofNullable(hotelElement.getLatestCheckOutTime()).ifPresent(k -> productFeatures.add(new ProductFeature(LATEST_CHECK_OUT_TIME, k)));
        Optional.ofNullable(hotelElement.getEarliestCheckInTime()).ifPresent(k -> productFeatures.add(new ProductFeature(EARLIEST_CHECK_IN_TIME, k)));
        Optional.ofNullable(hotelElement.getBreakfastCount()).ifPresent(k -> productFeatures.add(new ProductFeature(HOTEL_BREAKFAST, String.valueOf(k))));

        combo.getComboFeatures().put("hotelService", "");
        return product;
    }

    @Override
    protected void buildProductNum(Product product, PackageElement pElement) {
        // 酒店晚数兜底设置为1
        product.setNum(Optional.ofNullable(pElement.getSpecification())
                .map(Specification::getTimeSpecification)
                .map(TimeSpecification::getNumber)
                .orElse(1));
        product.getProductFeatures().add(new ProductFeature("num", String.valueOf(product.getNum())));

    }
}
