package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HotelElementValueMapVO extends BaseElementValueMapVO {
    /**
     * 城市
     */
    @JsonProperty("CITY")
    private String city;

    /**
     * 房型
     */
    @JsonProperty("ROOM_TYPE_ID")
    private String roomTypeId;

    /**
     * 星级
     */
    private String grade;

    /**
     * 库存
     */
    private String numberStock;

    /**
     * 间夜数
     */
    private Integer nightNumber;

    /**
     * 酒店标准库id
     */
    @JsonProperty("SHID")
    private String shId;

    /**
     * 区域id
     */
    @JsonProperty("DIVISION_ID")
    private String divisionId;
}
