package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.constant.packageelements.MasseurGenderTypeEnum;
import com.fliggy.vic.common.constant.packageelements.SpaTypeEnum;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.element.SpaElement;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.PlayThemeEnum;
import com.taobao.gpf.app.playabroad.constant.element.SpaRoomTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.SpaUsageScopeEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import com.taobao.travel.client.domain.dataobject.structured.ProductFeature;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SpaElementConverter extends AbstractElementConverter implements IElementConverter {
    public static final String SPA_BRAND_NAME_PRODUCT_KEY = "SPA_BRAND_NAME";
    public static final String SPA_USAGE_SCOPE_PRODUCT_KEY = "SPA_USAGE_SCOPE";
    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.SPA.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.SPA.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {

        SpaElement spaElement = (SpaElement) pElement;
        Product product = super.fromOvpElement(pElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        if (Objects.nonNull(spaElement.getDivision())) {
            elementFeatures.add(new ElementFeature("CITY", spaElement.getDivision().getDivisionName()));
        }
        // POI
        elementFeatures.addAll(fromPoiDTO(spaElement.getPoi()));
        elementFeatures.add(new ElementFeature("NAME", spaElement.getName()));
        //商家编码
        elementFeatures.add(new ElementFeature("OUTER_ID", spaElement.getOuterCode()));
        if(needBuildProductFeaturesFromPlayThemes(spaElement)) {
            buildProductFeaturesFromPlayThemes(spaElement.getPlayThemes(), product.getProductFeatures(), PlayThemeEnum.SPA_TYPE);
            buildProductFeaturesFromPlayThemes(spaElement.getPlayThemes(), product.getProductFeatures(), PlayThemeEnum.SPA_ROOM_TYPE);
            buildProductFeaturesFromPlayThemes(spaElement.getPlayThemes(), product.getProductFeatures(), PlayThemeEnum.MASSEUR_GENDER_TYPE);
            buildProductFeaturesFromPlayThemes(spaElement.getPlayThemes(), product.getProductFeatures(), PlayThemeEnum.SPA_BRAND_NAME);
        } else {
            Optional.ofNullable(spaElement.getSpaType()).map(SpaTypeEnum::getByCode).map(SpaTypeEnum::getDesc).ifPresent(spaType -> {
                product.getProductFeatures().add(new ProductFeature("SPA_TYPE", spaType));
            });
            Optional.ofNullable(spaElement.getSpaRoomType()).map(SpaRoomTypeEnum::getByCode).map(SpaRoomTypeEnum::getDesc).ifPresent(spaRoomType -> {
                product.getProductFeatures().add(new ProductFeature("SPA_ROOM_TYPE", spaRoomType));
            });
            Optional.ofNullable(spaElement.getMasseurGenderType()).map(MasseurGenderTypeEnum::getByCode).map(MasseurGenderTypeEnum::getDesc).ifPresent(gender -> {
                product.getProductFeatures().add(new ProductFeature("MASSEUR_GENDER_TYPE", gender));
            });
            Optional.ofNullable(spaElement.getSpaBrandName()).ifPresent(brandName -> {
                product.getProductFeatures().add(new ProductFeature(SPA_BRAND_NAME_PRODUCT_KEY, brandName));
            });
        }
        Optional.ofNullable(spaElement.getSpaUsageScope()).map(SpaUsageScopeEnum::getByCode).map(SpaUsageScopeEnum::getDesc).ifPresent(usageScope -> {
            product.getProductFeatures().add(new ProductFeature(SPA_USAGE_SCOPE_PRODUCT_KEY, usageScope));
        });

        return product;
    }

}
