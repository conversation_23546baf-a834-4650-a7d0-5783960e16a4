package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
public interface Combo2PlayBOConvert {

    /**
     * 解析商品套餐到controldo
     *
     * @param storeDO 旅行商品存储对象
     * @param param   组件扩展参数
     * @return 景酒套餐信息列表控制对象
     */
    OverseaPlayPackageInfoListControlDO convertStoreDO2ControlDO(TravelItemStoreDO storeDO, CompExtParam param);

}
