package com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2ovp;

import com.alibaba.gpf.sdk.exception.GpfException;
import com.alibaba.trip.tripdata.common.domain.page.Page;
import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alibaba.trip.tripdivision.client.domain.TrdiDivisionSearchParam;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.fliggy.vic.common.constant.packageelements.TimeUnitEnum;
import com.fliggy.vic.common.shared.Division;
import com.fliggy.vic.common.shared.Specification;
import com.fliggy.vic.common.shared.TimeSpecification;
import com.fliggy.vpp.client.dto.response.line.element.*;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.constant.FliggyPlayAbroadErrorCodeEnum;
import com.taobao.gpf.app.playabroad.constant.element.CateringTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.LanguageEnum;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.common.constants.CompConstants;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.testng.collections.Lists;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class PlayElementBO2OVPConvertor {

    @Resource
    PlayElementBO2OVPConvertorHelper playElementBO2OVPConvertorHelper;

    @Resource
    private TrdiDivisionReadService trdiDivisionReadService;

    @Resource
    PlayThemeBOConvertor playThemeBOConvertor;

    public TicketElement toTicketElement(ElementBO<TicketElementValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }
        TicketElement ticketElement = playElementBO2OVPConvertorHelper.toTicketElement(value.getValueMap());
        this.dealWithPackageElement(value, ticketElement, ElementCategoryEnum.TICKET);
        return ticketElement;
    }

    public HotelElement toHotelElement(ElementBO<HotelElementValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }

        HotelElement hotelElement = playElementBO2OVPConvertorHelper.toHotelElement(value.getValueMap());
        /*设置间夜时间规格*/
        Specification specification = new Specification();
        specification.setTimeSpecification(TimeSpecification.builder().timeUnit(TimeUnitEnum.NIGHTS.name()).number(value.getValueMap().getNightNumber()).build());
        hotelElement.setSpecification(specification);

        this.dealWithPackageElement(value, hotelElement, ElementCategoryEnum.HOTEL);
        return hotelElement;
    }

    public TrafficElement toTrafficElement(ElementBO<TrafficValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }
        TrafficElement trafficElement = playElementBO2OVPConvertorHelper.toTrafficElement(value.getValueMap());
        this.dealWithPackageElement(value, trafficElement, ElementCategoryEnum.TRAFFIC);
        return trafficElement;
    }

    public PhoneCardElement toPhoneCardElement(ElementBO<PhoneCardValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }
        PhoneCardElement phoneCardElement = playElementBO2OVPConvertorHelper.toPhoneCardElement(value.getValueMap());
        /*处理使用地区*/
        phoneCardElement.setDivision(this.dealWithDivision(value.getValueMap().getName()));
        this.dealWithPackageElement(value, phoneCardElement, ElementCategoryEnum.PHONECARD);
        return phoneCardElement;
    }

    public CateringElement toCateringElement(ElementBO<CateringValueMapBO> value, BasePlayElementGroupBO basePlayElementGroupBO) {
        if (Objects.isNull(value)) {
            return null;
        }
        CateringElement cateringElement = playElementBO2OVPConvertorHelper.toCateringElement(value.getValueMap());
        // 设置元素类型
        Optional.ofNullable(value.getValueMap().getElementType())
                .map(CateringTypeEnum::getByDesc).map(CateringTypeEnum::getType)
                .ifPresent(cateringElement::setCateringType);
        this.dealWithPackageElement(value, cateringElement, ElementCategoryEnum.CATERING);
        this.dealWithPlayTheme(cateringElement, basePlayElementGroupBO);
        return cateringElement;
    }

    public SpecialActivityElement toSpecialActivityElement(ElementBO<SpecialActivityValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }
        SpecialActivityElement specialActivityElement = playElementBO2OVPConvertorHelper.toSpecialActivityElement(value.getValueMap());
        this.dealWithPackageElement(value, specialActivityElement, ElementCategoryEnum.SPECIAL_ACTIVITY);
        return specialActivityElement;
    }

    public SpaElement toSpaElement(ElementBO<SpaValueMapBO> value, BasePlayElementGroupBO basePlayElementGroupBO) {
        if (Objects.isNull(value)) {
            return null;
        }
        /*处理spa房间类型信息*/
        SpaElement spaElement = playElementBO2OVPConvertorHelper.toSpaElement(value.getValueMap());
        this.dealWithPackageElement(value, spaElement, ElementCategoryEnum.SPA);
        this.dealWithPlayTheme(spaElement, basePlayElementGroupBO);
        return spaElement;
    }

    private void dealWithPlayTheme(PackageElement packageElement, BasePlayElementGroupBO basePlayElementGroupBO) {
        if (Objects.nonNull(basePlayElementGroupBO)) {
            PlayThemeBO playThemeBO = basePlayElementGroupBO.getPlayTheme();
            if (Objects.nonNull(playThemeBO)) {
                packageElement.setPlayThemes(playThemeBOConvertor.handleSubmitPlayThemes(playThemeBO));
            }
        }
    }

    public PhotographyElement toPhotographyElement(ElementBO<PhotographyValueMapBO> value, BasePlayElementGroupBO basePlayElementGroupBO) {
        if (Objects.isNull(value)) {
            return null;
        }
        PhotographyElement photographyElement = playElementBO2OVPConvertorHelper.toPhotographyElement(value.getValueMap());
        TimeSpecification timeSpecification = new TimeSpecification();

        /*旅拍天数规格说明*/
        if (Objects.nonNull(value.getValueMap().getDays())) {
            timeSpecification.setTimeUnit(TimeUnitEnum.DAYS.name());
            timeSpecification.setNumber(value.getValueMap().getDays());
        }
        photographyElement.setSpecification(new Specification(null, null, timeSpecification, null));

        Map<String, String> outerAttributes = new HashMap<>();
        outerAttributes.put("outerId", value.getValueMap().getOuterId());
        photographyElement.setOuterAttributes(outerAttributes);

        this.dealWithPackageElement(value, photographyElement, ElementCategoryEnum.PHOTOGRAPHY);
        this.dealWithPlayTheme(photographyElement, basePlayElementGroupBO);
        return photographyElement;
    }

    public DivingElement toDivingElement(DivingElementValueMapBO value, BasePlayElementGroupBO elementGroupBO) {
        if (Objects.isNull(value)) {
            return null;
        }
        DivingElement divingElement = playElementBO2OVPConvertorHelper.toDivingElement(value);
        divingElement.setElementCategoryEnum(ElementCategoryEnum.DIVING.getType());
        this.dealWithPlayTheme(divingElement, elementGroupBO);
        return divingElement;
    }

    public LocalTouristGuideElement toLocalTouristGuideElement(ElementBO<LocalTouristGuideValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }
        LocalTouristGuideElement localTouristGuideElement = playElementBO2OVPConvertorHelper.localTouristGuideElement(value);

        /*处理语种信息*/
        if (CollectionUtils.isNotEmpty(value.getValueMap().getLanguage())) {
            List<Integer> languageList = new ArrayList<>();
            value.getValueMap().getLanguage().forEach(language -> {
                LanguageEnum languageEnum = LanguageEnum.getByDesc(language);
                Optional.ofNullable(languageEnum).map(LanguageEnum::getType).ifPresent(languageList::add);
            });
            localTouristGuideElement.setLanguages(languageList);
        }

        Map<String, String> outerAttributes = new HashMap<>();
        outerAttributes.put("outerId", value.getValueMap().getOuterId());
        localTouristGuideElement.setOuterAttributes(outerAttributes);
        this.dealWithPackageElement(value, localTouristGuideElement, ElementCategoryEnum.TOUR);
        return localTouristGuideElement;
    }

    public PackageElement toWifiElement(ElementBO<WifiValueMapBO> value) {
        if (Objects.isNull(value)) {
            return null;
        }
        PackageElement packageElement = new PackageElement();

        /*处理所在地信息*/
        packageElement.setDivision(this.dealWithDivision(value.getValueMap().getName()));
        packageElement.setAdditionalRemarks(value.getValueMap().getAdditionalRemarks());
        this.dealWithPackageElement(value, packageElement, ElementCategoryEnum.WIFI);
        return packageElement;
    }

    private <T extends BaseElementValueMapBO> void dealWithPackageElement(ElementBO<T> sourceData, PackageElement element, ElementCategoryEnum elementCategoryEnum) {
        element.setPackageElementId(sourceData.getId());
        element.setElementCategoryEnum(elementCategoryEnum.getType());
        if (Objects.nonNull(sourceData.getId())) {
            element.setOuterCode(String.valueOf(sourceData.getId()));
        }
    }

    public Division dealWithDivision(String keyWord) {
        TrdiDivisionSearchParam searchParam = new TrdiDivisionSearchParam();
        searchParam.setKeywords(keyWord);
        searchParam.setIncludeGAT(true);
        searchParam.setAbroad(true);

        List<TrdiDivisionDO> divisionDOList = searchDivision(searchParam);
        if (CollectionUtils.isNotEmpty(divisionDOList)) {
            TrdiDivisionDO trdiDivisionDO = CollectionUtils.isEmpty(divisionDOList) ? null : divisionDOList.get(0);
            Division division = new Division();
            if (Objects.nonNull(trdiDivisionDO)) {
                division.setDivisionId(String.valueOf(trdiDivisionDO.getId()));
            }
            return division;
        }
        return null;
    }

    public List<TrdiDivisionDO> searchDivision(TrdiDivisionSearchParam param) {
        List<TrdiDivisionDO> divisionDOList = Lists.newArrayList();
        try {
            Page<TrdiDivisionDO> page = trdiDivisionReadService.searchDivision(param);
            divisionDOList.addAll(page.getData());
            while (divisionDOList.size() < 100 && page.getData().size() >= param.getRows()) {
                param.setOffset(param.getOffset() + page.getData().size());
                page = trdiDivisionReadService.searchDivision(param);
                divisionDOList.addAll(page.getData());
            }
        } catch (Exception e) {
            throw new GpfException(FliggyPlayAbroadErrorCodeEnum.QUERY_ELEMENT_DIVISION_ERROR.getErrorCode(CompConstants.OVERSEA_PLAY_PACKAGE_COMP_NAME, e.getMessage()));
        }
        return divisionDOList;
    }
}
