package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/3
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScenicSpotChargeItemBO {

    /**
     * 景点收费项目ID
     */
    private Long scenicSpotChargeItemId;

    /**
     * 景点收费项目名称 产品名称
     */
    private String scenicSpotChargeItemName;

    /**
     * 景点收费项目描述
     */
    private String scenicSpotChargeItemDescription;

    /**
     * 景点列表
     */
    private List<ScenicSpotBO> scenicSpots;
}
