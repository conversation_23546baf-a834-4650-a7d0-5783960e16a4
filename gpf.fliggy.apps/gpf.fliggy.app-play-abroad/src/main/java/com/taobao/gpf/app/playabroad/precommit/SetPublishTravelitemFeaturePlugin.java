package com.taobao.gpf.app.playabroad.precommit;

import com.alibaba.gpf.sdk.extpoint.commit.IPreCommitPlugin;
import com.alibaba.gpf.sdk.param.Param;
import com.taobao.gpf.domain.constant.item.AlitripFeatureConstants;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
@Component
public class SetPublishTravelitemFeaturePlugin implements IPreCommitPlugin<TravelItemStoreDO> {
    @Override
    public void preCommit(Param param, TravelItemStoreDO storeDO, Object obj) {
        storeDO.getAlitripFeatures().put(AlitripFeatureConstants.PLAY_PUBLISH_TRAVEL_ITEM_NEW, "1");
    }

    @Override
    public String getName() {
        return "setPublishTravelitemFeaturePlugin";
    }
}
