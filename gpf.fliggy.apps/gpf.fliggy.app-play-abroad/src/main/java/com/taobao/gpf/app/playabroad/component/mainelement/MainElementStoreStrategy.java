package com.taobao.gpf.app.playabroad.component.mainelement;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.travelitems.util.ProductUtils;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.Combo2OverseaPlayBOConvert;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

public class MainElementStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<Integer>> {

    @Override
    public void parseStore(BasicCompDO<Integer> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        Product product = storeDO.getCombos().stream()
                .map(ProductUtils::assembleProducts)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(e -> Objects.nonNull(e) && Boolean.TRUE.equals(e.isMain()))
                .filter(e -> Objects.nonNull(Combo2OverseaPlayBOConvert.getElementType(e)))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(product)) {
            ElementCategoryEnum elementCategoryEnum = ElementCategoryEnum.getByTravelitemValue(Combo2OverseaPlayBOConvert.getElementType(product));
            if (Objects.nonNull(elementCategoryEnum)) {
                compDO.setValue(elementCategoryEnum.getType());
            }
        }
    }

    @Override
    public void transferStore(BasicCompDO<Integer> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        // TODO Auto-generated method stub
        super.transferStore(compDO, storeDO, param);
    }

    @Override
    public String getName() {
        return "mainElementStoreStrategy";
    }

}
