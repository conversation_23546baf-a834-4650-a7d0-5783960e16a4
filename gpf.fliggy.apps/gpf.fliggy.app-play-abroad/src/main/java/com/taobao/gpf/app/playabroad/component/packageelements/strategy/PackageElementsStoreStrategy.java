package com.taobao.gpf.app.playabroad.component.packageelements.strategy;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

/**
 * 套餐搭配元素存储策略
 */
public class PackageElementsStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<PackageElementsControlDO>> {

    @Override
    public String getName() {
        return "packageElementsStoreStrategy";
    }

    @Override
    public void transferStore(BasicCompDO<PackageElementsControlDO> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        // 将控制对象中的数据转换为存储对象
        // 这里实现具体的存储逻辑
        PackageElementsControlDO controlDO = compDO.getControlDO();
        if (controlDO == null) {
            return;
        }
        
        // 将套餐搭配元素数据存储到商品对象中
        // 例如：storeDO.setPackageElements(...)
    }
} 