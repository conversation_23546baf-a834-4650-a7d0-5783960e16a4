package com.taobao.gpf.app.playabroad.component.sellpointpacakge;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.gpf.base.top.domain.extpoint.compparser.multicomplexfield.MultiComplexFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.ICompParseSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.dataobject.ImageValueDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.ExtensionRouterFatory;
import com.alitrip.travel.travelitems.platform.common.model.apps.line.journey.resource.JourneySellPointResource;
import com.taobao.gpf.domain.component.sellpointpackage.SellPointPackageControlDO;
import com.taobao.gpf.domain.constant.schema.SchemaConstant;
import com.taobao.gpf.domain.utils.ProductHighlightSchemaUtil;
import com.taobao.top.schema.field.Field;
import com.taobao.top.schema.field.MultiComplexField;
import com.taobao.top.schema.value.ComplexValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 境外玩乐业务身份下sellPointPackageInfo组件的schema策略实现
 */
@Slf4j
public class PlayAbroadSellPointPackageSchemaStrategy extends MultiComplexFieldSchemaStrategy<ParentCompDO<AbstractCompDO>> {

    @Override
    public String getName() {
        return "playAbroadSellPointPackageSchemaStrategy";
    }

    @Override
    protected void customRenderField(MultiComplexField field, ParentCompDO<AbstractCompDO> compDO, CompExtParam param,
            AdapterCompConfig compConfig, SchemaParseContext context) {
        if (compDO == null || CollectionUtils.isEmpty(compDO.getChildComps())) {
            return;
        }

        // 遍历处理子组件，将子组件的schema添加到父组件的schema中
        compDO.getChildComps().forEach(childComp -> {
            AdapterCompConfig childConfig = compConfig.getChildren().stream()
                    .filter(config -> StringUtils.equals(config.getCompname(), childComp.getCompName()))
                    .findFirst().orElse(null);

            if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                return;
            }

            ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(
                    ICompParseSchemaStrategy.class, childConfig.getStrategy());
            if (schemaStrategy == null) {
                return;
            }

            // 创建子组件的schema
            SchemaParseContext cloneContext = context.clone();
            cloneContext.setShowValue(true);
            Field childField = schemaStrategy.transferSchema(childComp, param, childConfig, cloneContext);
            if (childField != null) {
                field.add(childField);
            }
        });
    }

    @Override
    protected void setCompValue(ParentCompDO<AbstractCompDO> compDO, MultiComplexField field, CompExtParam param,
            AdapterCompConfig compConfig, SchemaParseContext context) {
        if (field == null || CollectionUtils.isEmpty(field.getComplexValues())) {
            return;
        }

        List<JourneySellPointResource> journeySellPointResourceList = new ArrayList<>();
        // 遍历复杂字段中的值，转换为JSON对象
        List<ComplexValue> complexValues = field.getComplexValues();

        complexValues.forEach(complexValue -> {
            JourneySellPointResource journeySellPointResource = new JourneySellPointResource();

            // 处理每个子组件的值
            for (AbstractCompDO childrenCompDO : compDO.getChildren()) {
                AdapterCompConfig childConfig = compConfig.getChildren().stream()
                        .filter(e -> StringUtils.equals(childrenCompDO.getCompName(), e.getCompname())).findFirst()
                        .orElse(null);

                if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                    continue;
                }

                ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(
                        ICompParseSchemaStrategy.class, childConfig.getStrategy());
                if (schemaStrategy == null) {
                    continue;
                }

                // 从复杂值中获取子字段
                Field childField = complexValue.getValueField(getFieldId(childConfig, null));
                if (childField == null) {
                    continue;
                }

                // 解析子字段的值
                SchemaParseContext childContext = context.clone();
                schemaStrategy.parseSchema(childrenCompDO, childField, param, childConfig, childContext);

                if (childrenCompDO.getCompName().equals("sellPointTitle")) {
                    journeySellPointResource.setTitle(childrenCompDO.getValue().toString());
                } else if (childrenCompDO.getCompName().equals("sellPointDesc")) {
                    journeySellPointResource.setDesc(childrenCompDO.getValue().toString());
                } else if (childrenCompDO.getCompName().equals("sellPointPicture")) {
                    if (CollectionUtils.isNotEmpty((List<ImageValueDO>) childrenCompDO.getValue())) {
                        journeySellPointResource.setPicture(((List<ImageValueDO>) childrenCompDO.getValue()).get(0).getUrl());
                    }
                }
            }

            journeySellPointResourceList.add(journeySellPointResource);

        });

        if (CollectionUtils.isNotEmpty(journeySellPointResourceList)) {
            ((SellPointPackageControlDO) compDO.getControlDO()).setResourceList(journeySellPointResourceList);
        }
    }

    @Override
    protected void renderSchemaFieldValue(MultiComplexField field, ParentCompDO<AbstractCompDO> compDO,
            CompExtParam param, AdapterCompConfig compConfig,
            SchemaParseContext context) {
        // 如果没有值或者组件不存在则返回
        if (compDO == null || CollectionUtils.isEmpty(compDO.getChildComps())) {
            return;
        }

        List<ComplexValue> valueList = Lists.newArrayList();

        // 直接从compDO获取值
        List<JourneySellPointResource> resourceList = null;
        if (compDO.getControlDO() instanceof SellPointPackageControlDO) {
            resourceList = ((SellPointPackageControlDO) compDO.getControlDO()).getResourceList();
        }

        if (CollectionUtils.isEmpty(resourceList)) {
            return;
        }

        // 清空现有的复杂值
        field.getComplexValues().clear();

        // 遍历资源列表，为每个元素创建一个ComplexValue
        for (JourneySellPointResource resource : resourceList) {
            if (resource == null) {
                continue;
            }

            // 创建一个新的复杂值
            ComplexValue complexValue = new ComplexValue();

            for (AbstractCompDO childereCompDO : compDO.getChildComps()) {
                AdapterCompConfig childConfig = compConfig.getChildren().stream().filter(e -> StringUtils
                        .equals(childereCompDO.getCompName(), e.getCompname())).findFirst().orElse(null);
                if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                    continue;
                }
                ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory
                        .getPlugin(ICompParseSchemaStrategy.class, childConfig.getStrategy());
                if (schemaStrategy == null) {
                    continue;
                }
                SchemaParseContext cloneConfig = context.clone();
                cloneConfig.setOnlyShowValue();
                cloneConfig.getFeature().put(SchemaConstant.COMPVALUE_KEY, ProductHighlightSchemaUtil.getCompNameValue(childereCompDO.getCompName(), resource));
                childereCompDO.setValue(ProductHighlightSchemaUtil.getCompNameValue(childereCompDO.getCompName(), resource));
                Field childField = schemaStrategy.transferSchema(childereCompDO, param, childConfig, cloneConfig);
                complexValue.put(childField);
            }
            valueList.add(complexValue);

        }
        // 将复杂值添加到字段中
        field.setComplexValues(valueList);
    }
}