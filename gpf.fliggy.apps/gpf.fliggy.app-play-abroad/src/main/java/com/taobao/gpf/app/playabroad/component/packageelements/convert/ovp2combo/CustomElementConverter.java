package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.ItemPVPair;
import com.alitrip.travel.travelitems.model.TravelItemsSkuExt;
import com.fliggy.vic.common.util.StreamUtils;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;
import com.taobao.gpf.app.playabroad.config.FliggyPlayAbroadSwitchConfig;
import com.taobao.gpf.app.playabroad.constant.ForestConstants;
import com.taobao.gpf.app.playabroad.constant.element.PhotographyNegativeNumberEnum;
import com.taobao.gpf.app.playabroad.constant.element.PhotographyTruingPhotoNumberEnum;
import com.taobao.gpf.app.playabroad.util.TravelItemElementHelper;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.travel.client.domain.dataobject.TravelItemPropDO;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import com.taobao.travel.client.domain.dataobject.structured.ProductFeature;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CustomElementConverter {

    @Resource
    private TravelSellConfig travelSellConfig;

    // 拍摄天数 value id list
    public static List<Long> PHOTOGRAPHY_DAYS_LIST = new ArrayList<>();
    static {
        // 其他
        PHOTOGRAPHY_DAYS_LIST.add(20213L);
        // 1天
        PHOTOGRAPHY_DAYS_LIST.add(10518540L);
        // 2天
        PHOTOGRAPHY_DAYS_LIST.add(7262011L);
        // 3天
        PHOTOGRAPHY_DAYS_LIST.add(7019407L);
        // 4天
        PHOTOGRAPHY_DAYS_LIST.add(11809862L);
    }

    // 精修数量value id列表
    public static Map<String, Long> TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP = new HashMap<>();
    static {
        // 不含精修 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.NO_EDIT.getType()), 5518689820L);
        // 精修数 30张以内 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.LESS_THAN_30.getType()), 803426706L);
        // 精修数 31-40张 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_31_AND_40.getType()), 803440621L);
        // 精修数 41-50张 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_41_AND_50.getType()), 803456536L);
        // 精修数 51-60张 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_51_AND_60.getType()), 803430690L);
        // 精修数 61-70张 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_61_AND_70.getType()), 803450475L);
        // 精修数 71-80张
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_71_AND_80.getType()), 803414851L);
        // 精修数 81-90张 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_81_AND_90.getType()), 803460469L);
        // 精修数 91-100张 vid
        TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyTruingPhotoNumberEnum.BETWEEN_91_AND_100.getType()), 803522002L);
    }

    /**
     * 根据产品新建含拓展元素的sku拓展字段
     */
    public List<TravelItemsSkuExt> buildTravelItemSkuExtList(List<Product> products) {
        return StreamUtils.asStream(products)
                .filter(Objects::nonNull)
                .map(this::buildTravelItemSkuExt)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private TravelItemsSkuExt buildTravelItemSkuExt(Product product) {
        ResourceType resourceType = ResourceType.getByType(product.getElement().getType());
        if (Objects.nonNull(resourceType)) {
            TravelItemsSkuExt travelItemsSkuExt = new TravelItemsSkuExt();
            travelItemsSkuExt.setName(String.valueOf(resourceType).toLowerCase());
            travelItemsSkuExt.setValue(JSON.toJSONString(product));
            return travelItemsSkuExt;
        } else {
            LogUtil.sysInfoLog("CustomElementConverter.buildTravelItemSkuExt", "没有找到类buildTravelItemSkuExt");
            return null;
        }
    }

    @AppSwitch(des = "底片数量property value id列表", level = Switch.Level.p2)
    public static Map<String, Long> NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP = new HashMap<>();

    static {
        // 底片数 100张以内 vid
        NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyNegativeNumberEnum.LESS_THAN_100.getType()), 432798802L);
        //  底片数 101-200张 vid
        NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyNegativeNumberEnum.BETWEEN_101_AND_200.getType()), 664806550L);
        // 底片数 201-300张 vid
        NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyNegativeNumberEnum.BETWEEN_201_AND_300.getType()), 67865076L);
        // 底片数 301-400张 vid
        NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyNegativeNumberEnum.BETWEEN_301_AND_400.getType()), 67865079L);
        // 底片数 401-500张 vid
        NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyNegativeNumberEnum.BETWEEN_401_AND_500.getType()), 67865082L);
        // 底片数 500张以上 vid
        NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.put(String.valueOf(PhotographyNegativeNumberEnum.OVER_500.getType()), 73532808L);

    }

    private static final Long PHOTOGRAPHY_SCENE_PID = *********L;
    private static final Long PHOTOGRAPHY_SCENE_VID = 228738148L;
    /**
     * 根据拓展元素新建pv对，因为拓展元素是之前对类目，搜索侧依赖之前类目的pv对。
     * 1 拿到forest的对应类目的所有pv对
     * 2 在constant定义pid
     * 3 拿到pid对应的所有vid
     * 4 根据vname找到对应的vid
     * 5 设置sku层级的pv对
     */
    public List<ItemPVPair> buildItemPVPairFromCustomProducts(List<Product> customProducts) {
        List<ItemPVPair> itemPvPairList = new ArrayList<>();
        if (CollectionUtils.isEmpty(customProducts)) {
            return itemPvPairList;
        }
        StreamUtils.asStream(customProducts)
                .filter(product -> Objects.nonNull(product.getElement()))
                .forEach(product -> {
                    if (Objects.equals(ResourceType.LOCAL_GUIDE.getType(), product.getElement().getType())) {
                        itemPvPairList.addAll(buildItemPvPairFromLocalGuideElement(product));
                    } else if (Objects.equals(ResourceType.TRAVEL_PHOTOGRAPHY.getType(), product.getElement().getType())) {
                        if (BooleanUtils.isTrue(FliggyPlayAbroadSwitchConfig.BUILD_PV_FROM_PHOTOGRAPHY)) {
                            itemPvPairList.addAll(buildItemPvPairFromPhotographyElement(product));
                        }
                    } else if (Objects.equals(ResourceType.SPA.getType(), product.getElement().getType())) {
                        itemPvPairList.addAll(buildItemPvPairFromSpaElement(product));
                    } else if (Objects.equals(ResourceType.FOOD.getType(), product.getElement().getType())) {
                        itemPvPairList.addAll(buildItemPvPairFromFoodElement(product));
                    }
                });
        return itemPvPairList;
    }

    /**
     * 设置美食元素cpv，cpv类型：
     * 1. 业务类型 *********
     */
    private List<ItemPVPair> buildItemPvPairFromFoodElement(Product product) {
        List<ItemPVPair> itemPvPairList = new ArrayList<>();
        ElementFeature businessTypeFeature = TravelItemElementHelper.getElementFeature(CateringElementConverter.BUSINESS_TYPE_ELEMENT_KEY, product.getElement());
        Optional.ofNullable(businessTypeFeature).map(ElementFeature::getValue)
                .map(v -> derivePvPair(v, 0L, ForestConstants.CATERING_BUSINESS_TYPE))
                .ifPresent(itemPvPairList::add);
        return itemPvPairList;
    }

    /**
     * 设置spa元素cpv，cpv类型：
     * 1. 服务商 122216567L
     */
    private List<ItemPVPair> buildItemPvPairFromSpaElement(Product product) {
        List<ItemPVPair> itemPvPairList = new ArrayList<>();
        ProductFeature spaBrandNameProductFeature = TravelItemElementHelper.getProductFeature(SpaElementConverter.SPA_BRAND_NAME_PRODUCT_KEY, product.getProductFeatures());
        Optional.ofNullable(spaBrandNameProductFeature).map(ProductFeature::getValue)
                .map(v -> derivePvPair(v, 0L, ForestConstants.SPA_SERVICE_MERCHANT))
                .ifPresent(itemPvPairList::add);

        ProductFeature spaUsageScopeProductFeature = TravelItemElementHelper.getProductFeature(SpaElementConverter.SPA_USAGE_SCOPE_PRODUCT_KEY, product.getProductFeatures());
        Optional.ofNullable(spaUsageScopeProductFeature).map(ProductFeature::getValue)
                .map(v -> derivePvPair(v, 0L, ForestConstants.SPA_USAGE_SCOPE))
                .ifPresent(itemPvPairList::add);

        return itemPvPairList;
    }

    /**
     * 设置旅拍元素cpv，cpv类型：
     * 1. 服务形式 18803094
     * 2. 拍摄风格 17259886 新的是文本框输入，可能映射不上
     * 3. 拍摄场景 ********* 新的没有
     * 4. 增值服务 *********
     * 5. 拍摄底片数 *********
     * 6. 精修数量 *********
     * 7. 拍摄天数 *********
     */
    private List<ItemPVPair> buildItemPvPairFromPhotographyElement(Product product) {
        List<ItemPVPair> itemPvPairList = new ArrayList<>();
        // 服务形式
        ElementFeature photographyFormElementFeature = TravelItemElementHelper.getElementFeature(PhotographyElementConverter.PHOTOGRAPHY_FORM_ELEMENT_KEY, product.getElement());
        Optional.ofNullable(photographyFormElementFeature).map(ElementFeature::getValue)
                .map(v -> derivePvPair(v, 0L, ForestConstants.PHOTOGRAPHY_FORM))
                .ifPresent(itemPvPairList::add);

        // 拍摄风格
        ElementFeature photographyStyleElementFeature = TravelItemElementHelper.getElementFeature(PhotographyElementConverter.PHOTOGRAPHY_STYLE_ELEMENT_KEY, product.getElement());
        if (Objects.nonNull(photographyStyleElementFeature) && Objects.nonNull(photographyStyleElementFeature.getValue())) {
            String[] photographyStyleElementFeatureList = StringUtils.split(photographyStyleElementFeature.getValue(), ",");
            Arrays.stream(photographyStyleElementFeatureList).filter(StringUtils::isNotBlank)
                    .map(v -> derivePvPair(v, 0L, ForestConstants.PHOTOGRAPHY_STYLE))
                    .filter(Objects::nonNull)
                    .forEach(itemPvPairList::add);
        }


        // 拍摄底片数
        ElementFeature photographyBottomElementFeature = TravelItemElementHelper.getElementFeature(PhotographyElementConverter.NEGATIVE_NUMBER_ELEMENT_CODE_KEY, product.getElement());
        Optional.ofNullable(photographyBottomElementFeature).map(ElementFeature::getValue)
                .map(k -> NEGATIVE_NUMBER_PROPERTY_VALUE_ID_MAP.get(k))
                .map(vid -> derivePvPair("", vid, ForestConstants.NEGATIVE_NUMBER))
                .ifPresent(itemPvPairList::add);

        // 精修数量
        ElementFeature truingPhotoNumberElementFeature = TravelItemElementHelper.getElementFeature(PhotographyElementConverter.TRUING_PHOTO_NUMBER_ELEMENT_CODE_KEY, product.getElement());
        Optional.ofNullable(truingPhotoNumberElementFeature).map(ElementFeature::getValue)
                .map(k -> TRUING_PHOTO_NUMBER_PROPERTY_VALUE_ID_MAP.get(k))
                .map(vid -> derivePvPair("", vid, ForestConstants.TRUING_PHOTO_NUMBER))
                .ifPresent(itemPvPairList::add);

        // 拍摄天数
        ElementFeature photographyDaysElementFeature = TravelItemElementHelper.getElementFeature(PhotographyElementConverter.PHOTOGRAPHY_DAYS_ELEMENT_KEY, product.getElement());
        Optional.ofNullable(photographyDaysElementFeature).map(ElementFeature::getValue)
                .filter(StringUtils::isNumeric)
                .map(Integer::parseInt)
                .map(this::getPhotographyDaysVid)
                .map(vid -> derivePvPair("", vid, ForestConstants.PHOTOGRAPHY_DAYS))
                .ifPresent(itemPvPairList::add);

        // 增值服务
        ElementFeature addValueElementFeature = TravelItemElementHelper.getElementFeature(PhotographyElementConverter.PROMISE_SERVICE_TAG_LIST, product.getElement());
        if (Objects.nonNull(addValueElementFeature) && Objects.nonNull(addValueElementFeature.getValue())) {
            String[] promiseServices = StringUtils.split(addValueElementFeature.getValue(), ",");
            Arrays.stream(promiseServices).filter(StringUtils::isNotBlank)
                    .map(s -> derivePvPair(s, 0L, ForestConstants.PHOTOGRAPHY_PROMISE_SERVICE))
                    .filter(Objects::nonNull)
                    .forEach(itemPvPairList::add);
        }

        // 拍摄场景，默认一个值。
        ItemPVPair photographySceneItemPvPair = new ItemPVPair();
        photographySceneItemPvPair.setPropertyId(PHOTOGRAPHY_SCENE_PID);
        photographySceneItemPvPair.setValueId(PHOTOGRAPHY_SCENE_VID);
        itemPvPairList.add(photographySceneItemPvPair);
        return itemPvPairList;
    }

    /**
     * 设置旅拍元素cpv，cpv类型：
     * 1. 导游语种 7604915L
     */
    private List<ItemPVPair> buildItemPvPairFromLocalGuideElement(Product product) {
        List<ItemPVPair> itemPvPairList = new ArrayList<>();
        // 导游语种
        ElementFeature languageFeature = TravelItemElementHelper.getElementFeature(LocalTouristGuideElementConverter.LANGUAGES_ELEMENT_KEY, product.getElement());
        if (Objects.nonNull(languageFeature) && Objects.nonNull(languageFeature.getValue())) {
            String[] languages = StringUtils.split(languageFeature.getValue(), ",");
            Arrays.stream(languages).filter(StringUtils::isNotBlank)
                    .map(s -> derivePvPair(s, 0L, ForestConstants.LANGUAGE))
                    .filter(Objects::nonNull)
                    .forEach(itemPvPairList::add);
        }
        return itemPvPairList;
    }

    /**
     * 使用pid获取全部pv对，再根据alias text或者vid获取确切对vid。
     *
     * @param aliasText 元素属性值
     * @param vid       类目平台vid
     * @param pid       类目平台pid
     */
    private ItemPVPair derivePvPair(String aliasText, Long vid, Long pid) {
        if ((StringUtils.isBlank(aliasText) && Objects.equals(0L, vid)) || Objects.equals(0L, pid)) {
            return null;
        }
        String trimmedAliasText = aliasText.trim();
        TravelItemPropDO fItemPropertyDo = travelSellConfig.getTravelItemPropDO(CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID, pid);
        if (Objects.nonNull(fItemPropertyDo) && CollectionUtils.isNotEmpty(fItemPropertyDo.getTravelItemPropValueDOs())) {
            return StreamUtils.asStream(fItemPropertyDo.getTravelItemPropValueDOs())
                    .filter(p ->
                            StringUtils.equals(travelSellConfig.getTravelForestCache().getValueName(p.getCid(), p.getPid(), p.getVid()), trimmedAliasText) ||
                            Objects.equals(p.getVid(), vid))
                    .findFirst()
                    .map(fItemPvPair -> {
                        ItemPVPair itemPvPair = new ItemPVPair();
                        itemPvPair.setPropertyId(fItemPvPair.getPid());
                        itemPvPair.setValueId(fItemPvPair.getVid());
                        itemPvPair.setPropertyText(travelSellConfig.getTravelForestCache().getPropName(fItemPvPair.getCid(), fItemPvPair.getPid()));
                        itemPvPair.setValueText(travelSellConfig.getTravelForestCache().getValueName(fItemPvPair.getCid(), fItemPvPair.getPid(), fItemPvPair.getVid()));
                        itemPvPair.setValueAliasText(trimmedAliasText);
                        return itemPvPair;
                    }).orElse(null);
        }
        return null;
    }


    private Long getPhotographyDaysVid(int photographyDays) {
        // 其他天数
        if (photographyDays < 1 || photographyDays > 4) {
            return PHOTOGRAPHY_DAYS_LIST.get(0);
        }
        return PHOTOGRAPHY_DAYS_LIST.get(photographyDays);
    }
}
