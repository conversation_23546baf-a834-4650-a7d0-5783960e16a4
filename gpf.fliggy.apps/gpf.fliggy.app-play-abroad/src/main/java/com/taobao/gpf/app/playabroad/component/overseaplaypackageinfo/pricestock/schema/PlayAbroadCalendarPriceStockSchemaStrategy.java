package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.pricestock.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.multicomplexfield.MultiComplexFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.google.common.collect.Lists;
import com.taobao.gpf.domain.constant.schema.SchemaFieldIdEnum;
import com.taobao.top.schema.enums.FieldTypeEnum;
import com.taobao.top.schema.enums.ValueTypeEnum;
import com.taobao.top.schema.factory.SchemaFactory;
import com.taobao.top.schema.field.*;
import com.taobao.top.schema.option.Option;
import com.taobao.top.schema.rule.TipRule;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
public class PlayAbroadCalendarPriceStockSchemaStrategy extends MultiComplexFieldSchemaStrategy<BasicCompDO<String>> {

    @Override
    protected void setCompValue(BasicCompDO<String> compDO, MultiComplexField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

    }

    @Override
    protected void customRenderField(MultiComplexField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext config) {
        super.customRenderField(field, compDO, param, compConfig, config);

        //skuId
        addSkuId(field);

        //priceType
        addPriceType(field);

        //币种
        addCurrency(field);

        //不可用日期
        addUnavailableDate(field);

        //直连配置
        addDirectInfo(field);

        //日历价库
        addCalendarPriceStock(field);
    }

    private void addCalendarPriceStock(MultiComplexField field) {

        MultiComplexField calendarPriceStock = (MultiComplexField)SchemaFactory.createField(FieldTypeEnum.MULTICOMPLEX);
        calendarPriceStock.setId(SchemaFieldIdEnum.CALENDAR_PRICE_STOCK_DATA.getId());
        calendarPriceStock.setName(SchemaFieldIdEnum.CALENDAR_PRICE_STOCK_DATA.getName());
        calendarPriceStock.setFieldRequired();

        InputField date = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        date.setId(SchemaFieldIdEnum.DATE.getId());
        date.setName(SchemaFieldIdEnum.DATE.getName());
        date.addValueTypeRule(ValueTypeEnum.TEXT);
        date.add(new TipRule("value格式是 yyyy-MM-dd"));
        calendarPriceStock.add(date);

        InputField price = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        price.setId(SchemaFieldIdEnum.PRICE.getId());
        price.setName(SchemaFieldIdEnum.PRICE.getName());
        price.addValueTypeRule(ValueTypeEnum.DOUBLE);
        price.add(new TipRule("价格单位是元，例如：12.52 "));
        calendarPriceStock.add(price);

        InputField stock = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        stock.setId(SchemaFieldIdEnum.STOCK.getId());
        stock.setName(SchemaFieldIdEnum.STOCK.getName());
        stock.addValueTypeRule(ValueTypeEnum.INTEGER);
        calendarPriceStock.add(stock);

        //编码
        InputField outerCode = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        outerCode.setId(SchemaFieldIdEnum.OUTER_CODE.getId());
        outerCode.setName(SchemaFieldIdEnum.OUTER_CODE.getName());
        outerCode.addValueTypeRule(ValueTypeEnum.TEXT);
        calendarPriceStock.add(outerCode);

        field.add(calendarPriceStock);
    }

    public static void addDirectInfo(MultiComplexField field) {
        ComplexField directInfo = (ComplexField)SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        directInfo.setId(SchemaFieldIdEnum.DIRECT_INFO.getId());
        directInfo.setName(SchemaFieldIdEnum.DIRECT_INFO.getName());

        addDirectInfoInner(directInfo);

        field.add(directInfo);
    }

    public static void addDirectInfo(ComplexField field) {
        addDirectInfoInner(field);
    }

    private static void addDirectInfoInner(ComplexField directInfo) {

        InputField spId = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        spId.setId(SchemaFieldIdEnum.SPID.getId());
        spId.setName(SchemaFieldIdEnum.SPID.getName());
        spId.addValueTypeRule(ValueTypeEnum.LONG);
        directInfo.add(spId);

        InputField supplierProductCode = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        supplierProductCode.setId(SchemaFieldIdEnum.SUPPLIER_PRODUCT_CODE.getId());
        supplierProductCode.setName(SchemaFieldIdEnum.SUPPLIER_PRODUCT_CODE.getName());
        supplierProductCode.addValueTypeRule(ValueTypeEnum.TEXT);
        directInfo.add(supplierProductCode);

        InputField syncType = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        syncType.setId(SchemaFieldIdEnum.SYNC_TYPE.getId());
        syncType.setName(SchemaFieldIdEnum.SYNC_TYPE.getName());
        syncType.addValueTypeRule(ValueTypeEnum.INTEGER);
        syncType.add(new TipRule("默认值为3，暂时不能为其他值"));
        directInfo.add(syncType);

        InputField crowdCode = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        crowdCode.setId(SchemaFieldIdEnum.CROWD_CODE.getId());
        crowdCode.setName(SchemaFieldIdEnum.CROWD_CODE.getName());
        crowdCode.addValueTypeRule(ValueTypeEnum.TEXT);
        directInfo.add(crowdCode);

        //加价规则
        ComplexField addPriceRuler = (ComplexField)SchemaFactory.createField(FieldTypeEnum.COMPLEX);
        addPriceRuler.setId(SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getId());
        addPriceRuler.setName(SchemaFieldIdEnum.PACKAGE_SKU_ADD_PRICE_RULER.getName());
        addPremiumRules(addPriceRuler);

        directInfo.add(addPriceRuler);
    }

    public static void addPremiumRules(ComplexField addPriceRuler) {

        InputField productId = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        productId.setId(SchemaFieldIdEnum.PRODUCT_ID.getId());
        productId.setName(SchemaFieldIdEnum.PRODUCT_ID.getName());
        productId.addValueTypeRule(ValueTypeEnum.LONG);
        productId.add(new TipRule("供应商产品Id, 不必填写"));
        addPriceRuler.add(productId);

        SingleCheckField type = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        type.setId(SchemaFieldIdEnum.PRICE_RULE_TYPE.getId());
        type.setName(SchemaFieldIdEnum.PRICE_RULE_TYPE.getName());
        type.addValueTypeRule(ValueTypeEnum.INTEGER);
        List<Option> typeOptions = Lists.newArrayList();
        typeOptions.add(createOption("1", "固定金额"));
        typeOptions.add(createOption("2", "按金额"));
        typeOptions.add(createOption("3", "按比例"));
        typeOptions.add(createOption("4", "按系统售价"));
        type.setOptions(typeOptions);
        addPriceRuler.add(type);

        InputField value = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        value.setId(SchemaFieldIdEnum.ADD_PRICE_RULE_MAIN_VALUE.getId());
        value.setName(SchemaFieldIdEnum.ADD_PRICE_RULE_MAIN_VALUE.getName());
        value.addValueTypeRule(ValueTypeEnum.DOUBLE);
        value.add(new TipRule("该字段和 addPriceRuler.type 字段配合使用，规则具体值，当addPriceRuler.type=4时，可不填"));
        addPriceRuler.add(value);

        MultiComplexField specialRule = (MultiComplexField)SchemaFactory.createField(FieldTypeEnum.MULTICOMPLEX);
        specialRule.setId(SchemaFieldIdEnum.SPECIAL_RULE.getId());
        specialRule.setName(SchemaFieldIdEnum.SPECIAL_RULE.getName());

        SingleCheckField specialType = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        specialType.setId(SchemaFieldIdEnum.PRICE_RULE_TYPE.getId());
        specialType.setName(SchemaFieldIdEnum.PRICE_RULE_TYPE.getName());
        specialType.addValueTypeRule(ValueTypeEnum.INTEGER);
        List<Option> specialTypeOptions = Lists.newArrayList();
        specialTypeOptions.add(createOption("1", "固定金额"));
        specialTypeOptions.add(createOption("2", "按金额"));
        specialTypeOptions.add(createOption("3", "按比例"));
        specialTypeOptions.add(createOption("4", "按系统售价"));
        specialType.setOptions(specialTypeOptions);
        specialRule.add(specialType);

        InputField specialValue = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        specialValue.setId(SchemaFieldIdEnum.ADD_PRICE_RULE_SPECIAL_DETAIL_VALUE.getId());
        specialValue.setName(SchemaFieldIdEnum.ADD_PRICE_RULE_SPECIAL_DETAIL_VALUE.getName());
        specialValue.addValueTypeRule(ValueTypeEnum.DOUBLE);
        specialValue.add(new TipRule("该字段和 addPriceRuler.specialRule.type 字段配合使用，规则具体值，当addPriceRuler.specialRule.type=4时，可不填"));
        specialRule.add(specialValue);

        InputField startDate = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        startDate.setId(SchemaFieldIdEnum.START_DATE.getId());
        startDate.setName(SchemaFieldIdEnum.START_DATE.getName());
        startDate.addValueTypeRule(ValueTypeEnum.LONG);
        startDate.add(new TipRule("日期，时间戳格式"));
        specialRule.add(startDate);

        InputField endDate = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        endDate.setId(SchemaFieldIdEnum.END_DATE.getId());
        endDate.setName(SchemaFieldIdEnum.END_DATE.getName());
        endDate.addValueTypeRule(ValueTypeEnum.LONG);
        endDate.add(new TipRule("日期，时间戳格式"));
        specialRule.add(endDate);

        MultiInputField availableWeedDays = (MultiInputField)SchemaFactory.createField(FieldTypeEnum.MULTIINPUT);
        availableWeedDays.setId(SchemaFieldIdEnum.AVAILABLE_WEEK_DAYS.getId());
        availableWeedDays.setName(SchemaFieldIdEnum.AVAILABLE_WEEK_DAYS.getName());
        availableWeedDays.addValueTypeRule(ValueTypeEnum.INTEGER);
        availableWeedDays.add(new TipRule("(1-7 分别代表周一到周日)"));
        specialRule.add(availableWeedDays);

        addPriceRuler.add(specialRule);
    }
    
    private static Option createOption(String value, String displayName) {
        Option option = new Option();
        option.setValue(value);
        option.setDisplayName(displayName);
        return option;
    }

    private void addUnavailableDate(MultiComplexField field) {
        InputField currency = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        currency.setId(SchemaFieldIdEnum.UNAVAILABLE_DATE.getId());
        currency.setName(SchemaFieldIdEnum.UNAVAILABLE_DATE.getName());
        currency.addValueTypeRule(ValueTypeEnum.TEXT);
        currency.add(new TipRule("value格式是 yyyy-MM-dd , 多个日期用逗号分隔"));
        field.add(currency);
    }

    private void addCurrency(MultiComplexField field) {
        InputField currency = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        currency.setId(SchemaFieldIdEnum.CURRENCY.getId());
        currency.setName(SchemaFieldIdEnum.CURRENCY.getName());
        currency.addValueTypeRule(ValueTypeEnum.TEXT);
        field.add(currency);
    }

    private static void addSkuId(MultiComplexField field) {
        InputField skuId = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        skuId.setId(SchemaFieldIdEnum.SKUID.getId());
        skuId.setName(SchemaFieldIdEnum.SKUID.getName());
        skuId.addValueTypeRule(ValueTypeEnum.LONG);
        skuId.add(new TipRule("值如果不存在不用自己生成，如果存在则保留"));
        field.add(skuId);
    }

    private static void addPriceType(MultiComplexField field) {
        SingleCheckField priceType = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        priceType.setId(SchemaFieldIdEnum.PRICE_TYPE.getId());
        priceType.setName(SchemaFieldIdEnum.PRICE_TYPE.getName());
        priceType.addValueTypeRule(ValueTypeEnum.INTEGER);

        List<Option> options = Lists.newArrayList();
        Option fixedPrice = new Option();
        fixedPrice.setValue("0");
        fixedPrice.setDisplayName("一口价价库");
        options.add(fixedPrice);
        Option adultPrice = new Option();
        adultPrice.setValue("1");
        adultPrice.setDisplayName("成人价库");
        options.add(adultPrice);
        Option childPrice = new Option();
        childPrice.setValue("2");
        childPrice.setDisplayName("儿童价库");
        options.add(childPrice);
        Option diffPrice = new Option();
        diffPrice.setValue("3");
        diffPrice.setDisplayName("单房差库");
        options.add(diffPrice);
        priceType.setOptions(options);

        field.add(priceType);
    }

    @Override
    protected void renderSchemaFieldValue(@NonNull MultiComplexField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {

    }

    @Override
    public String getName() {
        return "playAbroadCalendarPriceStockSchemaStrategy";
    }
}
