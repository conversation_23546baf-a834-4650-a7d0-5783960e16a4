package com.taobao.gpf.app.playabroad.component.mainelement;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.util.ParamUtil;
import com.alibaba.gpf.sdk.annotation.ext.ProcessForRender;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.EnumValuePageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.model.pagemodel.PageValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.travelitems.constants.ResourceType;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.app.playabroad.config.FliggyPlayAbroadSwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.helper.SelectTrafficHelper;
import com.taobao.gpf.domain.utils.FliggyParamUtil;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class PlayAbraodMainElementExtPoint implements IInjectExtension {

    /**
     * 套餐主元素更改提示
     */
    private static final List<String> MAIN_ELEMENTS_CHANGE_TIPS = Lists.newArrayList(
            "<span style=\"color:#ff1818\">您更改主元素类型，所有套餐的主元素类型都会更新。</span>&nbsp;&nbsp;&nbsp;"
                    + "<a href='https://seller.fliggy.com/#/travel-seller-scenic/resource_base?elementCat=default' target='_blank'>元素库管理</a>");

    @Resource
    private TravelSellConfig travelSellConfig;

    @Resource
    private SelectTrafficHelper selectTrafficHelper;

    @ProcessForRender(key = "playabroad-mainElements-addDataSource-rule", desc = "主元素初始化")
    public void addDataSource(CompExtParam param, @Category StdCategoryDO categoryDO, BasicCompDO<Integer> compDO) {
        if (null == compDO.getPagemodel()) {
            return;
        }
        EnumValuePageModel enumValuePageModel = (EnumValuePageModel) compDO.getPagemodel();
        List<PageValueModel> dataSource = Lists.newArrayList();

        long catId = ParamUtil.getCategoryDO(param).getCategoryId();
        long userId = FliggyParamUtil.getUserDO(param).getUserId();
        buildDataSource(dataSource, catId, userId, param);
        enumValuePageModel.setDataSource(dataSource);

        InfoModel infoModel = compDO.getPagemodel().getInfo() == null ? new InfoModel()
                : compDO.getPagemodel().getInfo();
        infoModel.setTop(Lists.newArrayList(MAIN_ELEMENTS_CHANGE_TIPS));

        compDO.getPagemodel().setInfo(infoModel);
    }

    public void buildDataSource(List<PageValueModel> dataSource, long catId, long userId, CompExtParam param) {
        List<ResourceType> mainTypes = Lists.newArrayList();

        FliggyPlayAbroadSwitchConfig.mainElements.stream().map(ResourceType::getByType).filter(Objects::nonNull).forEach(mainTypes::add);

        List<ConfirmMainElementPageModel> tempDataSource = mainTypes.stream().map(e -> buildPageValueModel(e, param)).filter(Objects::nonNull).collect(Collectors.toList());

        dataSource.addAll(tempDataSource);
    }

    public ConfirmMainElementPageModel buildPageValueModel(ResourceType resourceType, CompExtParam param) {
        ConfirmMainElementPageModel pageValueModel = new ConfirmMainElementPageModel();
        pageValueModel.setValue(resourceType.getType());
        if (ResourceType.RESTAURANT.equals(resourceType)) {
            pageValueModel.setText("美食卡券");
        } else {
            pageValueModel.setText(resourceType.getName());
        }

        if (!selectTrafficHelper.isTour(param) && Objects.equals(resourceType.getType(), 5)) {
            // 删除交通接驳
            return null;
        }
        pageValueModel.setConfirm("您更改主元素类型，所有套餐的主元素类型都会更新");
        return pageValueModel;
    }
}
