package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

/**
 * 套餐转换器接口
 */
public interface PlayBO2ComboConvertor {

    /**
     * 解析controldo到商品套餐
     *
     * @param controlDO
     * @param storeDO
     * @param param
     */
    void convertControlDO2StoreDO(OverseaPlayPackageInfoListControlDO controlDO, TravelItemStoreDO storeDO, CompExtParam param);
} 