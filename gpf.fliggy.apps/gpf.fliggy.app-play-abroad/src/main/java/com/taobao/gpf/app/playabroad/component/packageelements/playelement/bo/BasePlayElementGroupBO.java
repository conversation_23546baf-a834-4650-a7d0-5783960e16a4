package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementTypeEnum;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BasePlayElementGroupBO {

    /**
     * 元素名称
     */
    private String elementName;

    /**
     * 元素类型
     * @see ElementCategoryEnum
     */
    private Integer elementCategory;

    /**
     * 元素类别
     * @see ElementTypeEnum
     */
    private Integer elementType;

    /**
     * 玩法信息
     */
    private PlayThemeBO playTheme;
}
