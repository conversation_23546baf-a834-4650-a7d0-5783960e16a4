package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.exception.GpfException;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.constants.SellType;
import com.alitrip.travel.travelitems.model.ItemPVPair;
import com.alitrip.travel.travelitems.model.TravelItemSku;
import com.alitrip.travel.travelitems.model.person.TravelPerson;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.alitrip.travel.travelitems.model.systemvendordirect.SystemVendorDirectCode;
import com.alitrip.travel.travelitems.model.systemvendordirect.SystemVendorDirectCodes;
import com.alitrip.travel.travelitems.util.ProductUtils;
import com.alitrip.traveltrade.util.MoneyUtils;
import com.fliggy.travel.good.client.cp.SellPublishService;
import com.fliggy.travel.good.client.cp.model.CalendarPriceStockDTO;
import com.fliggy.travel.good.client.cp.model.GetPriceStockRequest;
import com.fliggy.travel.good.client.cp.model.GetPriceStockResponse;
import com.fliggy.travel.good.commons.Result;
import com.fliggy.travel.guide.param.TravelGuideResult;
import com.fliggy.travel.guide.param.playtype.PlayType;
import com.fliggy.travel.guide.param.playtype.PlayTypeAttributes;
import com.fliggy.travel.guide.service.playtype.PlayTypeHelpService;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.constant.element.*;
import com.taobao.gpf.share.directItem.DirectInfoConvert;
import com.taobao.gpf.app.playabroad.model.CalendarPriceStockBO;
import com.taobao.gpf.domain.itemdirect.model.PlayAbroadPackageDirectInfo;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodValueModel;
import com.taobao.gpf.domain.component.playmethod.model.PlayProperty;
import com.taobao.gpf.domain.itemdirect.DirectConstants;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import com.taobao.gpf.domain.utils.common.DateUtil;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import com.taobao.travel.client.domain.dataobject.structured.ProductFeature;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
@Component
public class Combo2OverseaPlayBOConvert implements Combo2PlayBOConvert {

    @Resource
    private PlayTypeHelpService playTypeHelpService;

    @Resource
    private DirectInfoConvert directInfoConvert;

    @Resource
    private SellPublishService sellPublishService;

    @Override
    public OverseaPlayPackageInfoListControlDO convertStoreDO2ControlDO(TravelItemStoreDO storeDO, CompExtParam param) {
        OverseaPlayPackageInfoListControlDO controlDO = new OverseaPlayPackageInfoListControlDO();
        controlDO.setComboDOList(storeDO.getCombos());
        controlDO.setPackageInfos(convertPackages(storeDO, param));

        return controlDO;

    }

    public List<OverseaPlayPackageInfoControlDO> convertPackages(TravelItemStoreDO storeDO, CompExtParam param) {
        List<OverseaPlayPackageInfoControlDO> playPackageInfoBOS = new ArrayList<>();
        List<Combo> combos = storeDO.getCombos();
        for (Combo combo : combos) {
            playPackageInfoBOS.add(convertPackage(combo, storeDO, param));
        }
        return playPackageInfoBOS;
    }

    private OverseaPlayPackageInfoControlDO convertPackage(Combo combo, TravelItemStoreDO item, CompExtParam param) {
        OverseaPlayPackageInfoControlDO playPackageInfoBO = new OverseaPlayPackageInfoControlDO();
        Map<String, String> comboFeatures = combo.getComboFeatures();
        TravelItemSku travelItemSku = CollectionUtils.isEmpty(combo.getSkuList()) ? null : combo.getSkuList().get(0);
        playPackageInfoBO.setPackageId(combo.getPackageId());
        playPackageInfoBO.setPackageName(combo.getComboName());
        //未使用字段
        playPackageInfoBO.setActivityHour(combo.getComboFeatures().get("activityDuration") == null ? null : Integer.valueOf(combo.getComboFeatures().get("activityDuration")));
        playPackageInfoBO.setOutId(combo.getOuterId());
        playPackageInfoBO.setPackageDesc(
                Optional.ofNullable(travelItemSku)
                        .map(TravelItemSku::getPackageDesc)
                        .orElse(null)
        );
        playPackageInfoBO.setMainElementType(buildMainElement(combo, param));
        playPackageInfoBO.setOtherElementTypes(buildOtherElement(combo, param));
        playPackageInfoBO.setPlayThemeBO(buildPlayTheme(combo));
        //todo
        playPackageInfoBO.setFeeExclude(
                Optional.ofNullable(travelItemSku)
                        .map(TravelItemSku::getFeeExclude)
                        .orElse(null)
        );
        //只有一个sku就是一口价
        playPackageInfoBO.setFixedPrice(Boolean.FALSE.toString().equals(comboFeatures.get("doesIndependentStockForDifferentCrowds")));

        playPackageInfoBO.setPackagePrice(
                Optional.ofNullable(travelItemSku)
                        .map(TravelItemSku::getPrice)
                        .map(t -> MoneyUtils.getYuanFromCent(Long.valueOf(t)))
                        .orElse(null)
        );
        playPackageInfoBO.setPackageStock(
                Optional.ofNullable(travelItemSku)
                        .map(TravelItemSku::getQuantity)
                        .orElse(null)
        );
        playPackageInfoBO.setFeeInclude(
                Optional.ofNullable(travelItemSku)
                        .map(TravelItemSku::getFeeInclude)
                        .orElse(null)
        );
        playPackageInfoBO.setPlayCanlendePriceStockControlDO(convertSkuPriceStock(item, combo));
        playPackageInfoBO.setPackageElementsControlDO(convertElementAfterParses(combo));
        playPackageInfoBO.setTravelPerson(convert2TravelPerson(combo));
        playPackageInfoBO.setPlayAbroadPackageDirectInfo(buildPackageDirectInfo(combo, item));
        return playPackageInfoBO;
    }

    private List<PlaySkuPriceStockControlDO> convertSkuPriceStock(TravelItemStoreDO item, Combo combo) {
        List<PlaySkuPriceStockControlDO> skuPriceStockBOS = new ArrayList<>();
        for (TravelItemSku sku : combo.getSkuList()) {
            PlaySkuPriceStockControlDO skuPriceStockBO = new PlaySkuPriceStockControlDO();
            skuPriceStockBO.setSkuId(sku.getSkuId());
            skuPriceStockBO.setPriceType(getPriceType(sku, combo.getComboFeatures()));
            Optional.ofNullable(sku.getSkuFeatures())
                    .map(skuFeatures -> skuFeatures.get(DirectConstants.UNAVAILABLE_DATES))
                    .filter(StringUtils::isNotBlank)
                    .map(t -> Arrays.asList(t.split(",")))
                    .ifPresent(skuPriceStockBO::setUnavailableDate);
            skuPriceStockBO.setCalendarPriceStock(new ArrayList<>());
            Map<Date, CalendarPriceStockDTO> calendarPriceStockDTOMap = new HashMap<>();
            if (sku.getSkuFeatures().containsKey(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO) && StringUtils.isNotBlank(sku.getSkuFeatures().get(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO))) {
                PlayAbroadPackageDirectInfo playAbroadPackageDirectInfo = JSON.parseObject(sku.getSkuFeatures().get(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO), PlayAbroadPackageDirectInfo.class);
                skuPriceStockBO.setPlayAbroadPackageSkuDirectInfo(playAbroadPackageDirectInfo);

                if (CollectionUtils.isNotEmpty(skuPriceStockBO.getUnavailableDate())) {
                    //不可用日期不为空需要从直连查询一下
                    if (sku.getSkuFeatures().containsKey(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO) && sku.getSkuFeatures().containsKey(DirectConstants.PLAY_DIRECT)) {
                        //查询直连价库
                        GetPriceStockRequest getPriceStockRequest = new GetPriceStockRequest();
                        getPriceStockRequest.setSpId(playAbroadPackageDirectInfo.getSpId());
                        getPriceStockRequest.setAreaId(playAbroadPackageDirectInfo.getCrowdCode());
                        getPriceStockRequest.setAddPriceRuler(directInfoConvert.convert(playAbroadPackageDirectInfo.getAddPriceRuler()));
                        //这里一定要设置为空数组，为了展示不可用日期的价库
                        getPriceStockRequest.setUnavailableDate(new ArrayList<>());

                        Result<GetPriceStockResponse> priceStockAndConfig = sellPublishService.getPriceStock(getPriceStockRequest);
                        if (!priceStockAndConfig.isSuccess() || priceStockAndConfig.getData().getPriceStock() == null) {
                            throw new GpfException(priceStockAndConfig.getMessage());
                        }
                        calendarPriceStockDTOMap = priceStockAndConfig.getData().getPriceStock().getCalendarPriceStock().stream()
                                .collect(Collectors.toMap(CalendarPriceStockDTO::getDate, t -> t, (a, b) -> a));
                    }
                }
            }

            SystemVendorDirectCodes systemVendorDirectCodes = sku.getSystemVendorDirectCodes();

            if (MapUtils.isNotEmpty(sku.getPrices())) {
                for (Map.Entry<Date, Long> entry : sku.getPrices().entrySet()) {
                    if (MapUtils.isNotEmpty(sku.getQuantitys()) && sku.getQuantitys().containsKey(entry.getKey())) {
                        CalendarPriceStockBO calendarPriceStockBO = new CalendarPriceStockBO();
                        calendarPriceStockBO.setPrice(MoneyUtils.getYuanFromCent(entry.getValue()));
                        calendarPriceStockBO.setStock(Long.valueOf(sku.getQuantitys().get(entry.getKey())));
                        calendarPriceStockBO.setDate(DateUtil.formatToyyyMMdd(entry.getKey()));
                        calendarPriceStockBO.setOuterCode(Optional.ofNullable(systemVendorDirectCodes)
                                .map(SystemVendorDirectCodes::getCalendarSystemVendorDirectCodes)
                                .map(map -> map.get(entry.getKey()))
                                .map(SystemVendorDirectCode::getSystemVendorCode)
                                .orElse(null));
                        skuPriceStockBO.getCalendarPriceStock().add(calendarPriceStockBO);
                    }
                }

                //设置不可用日期
                if (MapUtils.isNotEmpty(calendarPriceStockDTOMap)) {
                    for (Map.Entry<Date, CalendarPriceStockDTO> dateEntry : calendarPriceStockDTOMap.entrySet()) {
                        if (CollectionUtils.isNotEmpty(skuPriceStockBO.getUnavailableDate())
                                && skuPriceStockBO.getUnavailableDate().contains(DateUtil.formatToyyyMMdd(dateEntry.getKey()))
                                //在今天之前
                                && !dateEntry.getKey().before(Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()))) {
                            CalendarPriceStockBO calendarPriceStockBO = new CalendarPriceStockBO();
                            calendarPriceStockBO.setPrice(MoneyUtils.getYuanFromCent(dateEntry.getValue().getPrice()));
                            calendarPriceStockBO.setStock(dateEntry.getValue().getStock());
                            calendarPriceStockBO.setDate(DateUtil.formatToyyyMMdd(dateEntry.getKey()));
                            calendarPriceStockBO.setOuterCode(dateEntry.getValue().getOuterCode());
                            calendarPriceStockBO.setUnavailable(true);
                            skuPriceStockBO.getCalendarPriceStock().add(calendarPriceStockBO);
                        }
                    }
                }
            }
            skuPriceStockBOS.add(skuPriceStockBO);
        }
        return skuPriceStockBOS;
    }

    private PackageElementsControlDO convertElementAfterParses(Combo combo) {
        PackageElementsControlDO packageElementsControlDO= new PackageElementsControlDO();
        List<BasePlayElementGroupBO> elementAfterParse = new ArrayList<>();
        packageElementsControlDO.setElementInfoList(elementAfterParse);

        List<Product> products = ProductUtils.assembleProducts(combo);
        if (CollectionUtils.isEmpty(products)) {
            return packageElementsControlDO;
        }
        Map<Integer, List<Product>> typeProductMap = products.stream()
                .filter(product -> Objects.nonNull(product.getElement()))
                .collect(Collectors.groupingBy(Combo2OverseaPlayBOConvert::getElementType));
        for (Map.Entry<Integer, List<Product>> entry : typeProductMap.entrySet()) {
            ElementCategoryEnum type = ElementCategoryEnum.getByTravelitemValue(entry.getKey());
            if (Objects.isNull(type)) {
                continue;
            }
            elementAfterParse.add(buildElementAfterParse(type, entry.getValue()));
        }

        return packageElementsControlDO;
    }

    private BasePlayElementGroupBO buildElementAfterParse(ElementCategoryEnum type, List<Product> products) {
        BasePlayElementGroupBO elementGroupBO;

        switch (type) {
            case TICKET:
                elementGroupBO = buildTicketElement(type, products);
                break;
            case HOTEL:
                elementGroupBO = buildHotelElement(type, products);
                break;
            case TRAFFIC:
                elementGroupBO = buildTrafficElement(type, products);
                break;
            case WIFI:
                elementGroupBO = buildWifiElement(type, products);
                break;
            case PHONECARD:
                elementGroupBO = buildPhoneCardElement(type, products);
                break;
            case CATERING:
                elementGroupBO = buildCateringElement(type, products);
                break;
            case SPECIAL_ACTIVITY:
                elementGroupBO = buildSpecialActivityElement(type, products);
                break;
            case SPA:
                elementGroupBO = buildSpaElement(type, products);
                break;
            case TOUR:
                elementGroupBO = buildTourElement(type, products);
                break;
            case PHOTOGRAPHY:
                elementGroupBO = buildPhotographyElement(type, products);
                break;
            case DIVING:
                elementGroupBO = buildDivingElement(type, products);
                break;
            default:
                return null;
        }
        elementGroupBO.setElementCategory(type.getTravelitemValue());
        elementGroupBO.setElementName(type.getValue());
        if (products.get(0).isMain()) {
            elementGroupBO.setElementType(ElementTypeEnum.MAIN.getType());
        } else {
            elementGroupBO.setElementType(ElementTypeEnum.SUPPORT.getType());
        }
        return elementGroupBO;
    }

    private DivingElementGroupBO buildDivingElement(ElementCategoryEnum type, List<Product> products) {
        DivingElementGroupBO elementGroupBO = new DivingElementGroupBO();
        List<DivingElementValueMapBO> elementBOS = new ArrayList<>();
        for (Product product : products) {
            DivingElementValueMapBO valueMap = new DivingElementValueMapBO();
            valueMap.setLabel(getElementFeatureString("POI_NAME", product));
            valueMap.setValue(getElementFeatureLong("POIID", product));
            QuantitySpecificationEnum specification = getSpecification(getElementFeatureString("SPECIFICATION", product));
            if (Objects.nonNull(specification)) {
                valueMap.setType(String.valueOf(specification.getType()));
            }
            valueMap.setNumberStock(product.getNum());
            coverValueMap(valueMap, product);
            elementBOS.add(valueMap);
        }
        elementGroupBO.setDivingElementList(elementBOS);
        Product product = products.get(0);
        PlayThemeBO playThemeBO = getPlayThemeProps(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME);
        List<PlayProperty> attributes = playThemeBO.getPlayThemeValue().get(0).getProperties();
        String divingType = getElementFeatureString("DIVING_TYPE, ELEMENT_TYPE", product);
        if (StringUtils.isNotBlank(divingType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME, 361L, divingType));
        }
        String certificationType = getElementFeatureString("DIVING_CERTIFICATION_TYPE", product);
        if (StringUtils.isNotBlank(certificationType)) {
            PlayProperty playThemeByName = getPlayThemeByName(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME, 362L, certificationType);
            if (!Arrays.asList("PADI-OW", "PADI-AOW", "PADI-自由潜", "AIDA", "SSI").contains(certificationType)) {
                playThemeByName.setValue("other");
                playThemeByName.setOtherVal(certificationType);
            }
            attributes.add(playThemeByName);
        }
        String divingSpecialType = getElementFeatureString("DIVING_SPECIAL_TYPE", product);
        if (StringUtils.isNotBlank(divingSpecialType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME, 363L, divingSpecialType));
        }
        String coachRatio = getElementFeatureString("COACH_RATIO", product);
        if (StringUtils.isNotBlank(coachRatio)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME, 367L, coachRatio));
        }
        String divingEquipmentList = getElementFeatureString("DIVING_EQUIPMENT_LIST", product);
        if (StringUtils.isNotBlank(divingEquipmentList)) {
            String other = findOther(divingEquipmentList, Arrays.asList("潜水面镜", "一次性咬嘴", "脚蹼", "气瓶", "潜水服", "呼吸管"));
            PlayProperty attributesBO = getPlayThemeByName(ElementPlayThemeEnum.DIVING_ELEMENT_PLAY_THEME, 368L, divingEquipmentList);
            if (StringUtils.isNotBlank(other)) {
                attributesBO.setValue(divingEquipmentList + ",other");
                attributesBO.setIsOther(1);
                attributesBO.setOtherVal(other);
            }
            attributes.add(attributesBO);
        }
        elementGroupBO.setPlayTheme(playThemeBO);
        return elementGroupBO;
    }

    private PhotographyElementGroupBO buildPhotographyElement(ElementCategoryEnum type, List<Product> products) {
        PhotographyElementGroupBO elementGroupBO = new PhotographyElementGroupBO();
        List<ElementBO<PhotographyValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<PhotographyValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            PhotographyValueMapBO valueMap = new PhotographyValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setAddress(getElementFeatureString("PHOTOGRAPHY_ADDRESS", product));
            valueMap.setDays(getElementFeatureInt("PHOTOGRAPHY_DAYS", product));
            valueMap.setClothingNumber(getElementFeatureInt("CLOTHING_NUMBER", product));
            valueMap.setPhotographyStyle(getElementFeatureString("PHOTOGRAPHY_STYLE", product));
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setPhotographyElementList(elementBOS);
        Product product = products.get(0);
        PlayThemeBO playThemeBO = getPlayThemeProps(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME);
        List<PlayProperty> attributes = playThemeBO.getPlayThemeValue().get(0).getProperties();
        String photographyType = getElementFeatureString("PHOTOGRAPHY_TYPE, NAME", product);
        if (StringUtils.isNotBlank(photographyType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME, 420L, photographyType));
        }
        String photographyFrom = getElementFeatureString("PHOTOGRAPHY_FORM, ELEMENT_TYPE", product);
        if (StringUtils.isNotBlank(photographyFrom)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME, 421L, photographyFrom));
        }

        String negativeNumber = getElementFeatureString("NEGATIVE_NUMBER_DESC", product);
        if (StringUtils.isNotBlank(negativeNumber)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME, 422L, negativeNumber));
        }


        String truingPhotoNumber = getElementFeatureString("TRUING_PHOTO_NUMBER_DESC", product);
        if (StringUtils.isNotBlank(truingPhotoNumber)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME, 423L, truingPhotoNumber));
        }


        String photographyDeliveryType = getElementFeatureString("PHOTOGRAPHY_DELIVERY_TYPE", product);
        if (StringUtils.isNotBlank(photographyDeliveryType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME, 424L, photographyDeliveryType));
        }

        String promiseServiceTagList = getElementFeatureString("PROMISE_SERVICE_TAG_LIST", product);
        if (StringUtils.isNotBlank(promiseServiceTagList)) {
            String other = findOther(promiseServiceTagList, Arrays.asList("无隐形消费", "不满意重拍", "送微电影", "底片全送"));
            PlayProperty attributesBO = getPlayThemeByName(ElementPlayThemeEnum.PHOTOGRAPHY_ELEMENT_PLAY_THEME, 425L, promiseServiceTagList);
            if (StringUtils.isNotBlank(other)) {
                attributesBO.setValue(promiseServiceTagList + ",other");
                attributesBO.setIsOther(1);
                attributesBO.setOtherVal(other);
            }
            attributes.add(attributesBO);

        }
        elementGroupBO.setPlayTheme(playThemeBO);
        return elementGroupBO;
    }

    /**
     * 查找不在list里面的字符串
     *
     * @param promiseServiceTagList
     * @param list
     * @return
     */
    private String findOther(String promiseServiceTagList, List<String> list) {
        for (String s : promiseServiceTagList.split(",")) {
            if (!list.contains(s)) {
                return s;
            }
        }
        return null;
    }

    private LocalTouristGuideElementGroupBO buildTourElement(ElementCategoryEnum type, List<Product> products) {
        LocalTouristGuideElementGroupBO elementGroupBO = new LocalTouristGuideElementGroupBO();
        List<ElementBO<LocalTouristGuideValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<LocalTouristGuideValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            LocalTouristGuideValueMapBO valueMap = new LocalTouristGuideValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setNumberStock(product.getNum());
            QuantitySpecificationEnum specification = getSpecification(getElementFeatureString("SPECIFICATION", product));
            if (Objects.nonNull(specification)) {
                valueMap.setType(String.valueOf(specification.getType()));
            }
            String languages = getElementFeatureString("LANGUAGES", product);
            if (StringUtils.isNotBlank(languages)) {
                valueMap.setLanguage(Arrays.stream(languages.split(",")).collect(Collectors.toList()));
            }

            LocalTouristGuideBizTypeEnum bizTypeEnum = getTourBizType(getElementFeatureString("ELEMENT_TYPE", product));
            if (Objects.nonNull(bizTypeEnum)) {
                valueMap.setBizType(bizTypeEnum.name());
            }

            String doesIncludeScenicSpotTicket = getElementFeatureString("DOES_INCLUDE_SCENIC_SPOT_TICKET", product);
            if (Objects.equals(Boolean.TRUE.toString(), doesIncludeScenicSpotTicket)) {
                valueMap.setHasTicket(true);
            } else if (Objects.equals(Boolean.FALSE.toString(), doesIncludeScenicSpotTicket)) {
                valueMap.setHasTicket(false);
            }

            String expertId = getElementFeatureString("EXPERT_ID", product);
            if (StringUtils.isNotBlank(expertId)) {
                valueMap.setRelationLecturerId(expertId);
            }

            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setLocalTouristGuideElementList(elementBOS);
        return elementGroupBO;
    }

    private LocalTouristGuideBizTypeEnum getTourBizType(String elementType) {
        return Arrays.stream(LocalTouristGuideBizTypeEnum.values()).filter(bizTypeEnum -> Objects.equals(bizTypeEnum.name(), elementType)).findFirst().orElse(null);
    }

    private QuantitySpecificationEnum getSpecification(String specification) {
        if (StringUtils.isBlank(specification)) {
            return null;
        }
        for (QuantitySpecificationEnum enums : QuantitySpecificationEnum.values()) {
            if (enums.getDesc().startsWith(specification)) {
                return enums;
            }
        }
        return null;
    }

    private SpaElementGroupBO buildSpaElement(ElementCategoryEnum type, List<Product> products) {
        SpaElementGroupBO elementGroupBO = new SpaElementGroupBO();
        List<ElementBO<SpaValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<SpaValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            SpaValueMapBO valueMap = new SpaValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setName(getElementFeatureString("NAME", product));
            valueMap.setPoiName(getElementFeatureString("POI_NAME", product));
            SpaUsageScopeEnum spaUsageScope = getSpaUsageScope(getProductFeatureString("SPA_USAGE_SCOPE", product));
            if (Objects.nonNull(spaUsageScope)) {
                valueMap.setSpaUsageScope(spaUsageScope.getType());
            }
            valueMap.setNumberStock(product.getNum());
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setSpaElementList(elementBOS);
        Product product = products.get(0);
        PlayThemeBO playThemeBO = getPlayThemeProps(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME);
        List<PlayProperty> attributes = playThemeBO.getPlayThemeValue().get(0).getProperties();
        String spaType = getProductFeatureString("SPA_TYPE", product);
        if (StringUtils.isNotBlank(spaType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME, 409L, spaType));
        }
        String roomType = getProductFeatureString("SPA_ROOM_TYPE", product);
        if (StringUtils.isNotBlank(roomType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME, 410L, roomType));
        }
        String masseurGenderType = getProductFeatureString("MASSEUR_GENDER_TYPE", product);
        if (StringUtils.isNotBlank(masseurGenderType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME, 411L, masseurGenderType));
        }
        String brandName = getProductFeatureString("SPA_BRAND_NAME", product);
        if (StringUtils.isNotBlank(brandName)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.SPA_ELEMENT_PLAY_THEME, 439L, brandName));
        }
        elementGroupBO.setPlayTheme(playThemeBO);
        return elementGroupBO;
    }

    public SpaUsageScopeEnum getSpaUsageScope(String desc) {
        for (SpaUsageScopeEnum enums : SpaUsageScopeEnum.values()) {
            if (Objects.equals(enums.getDesc(), desc)) {
                return enums;
            }
        }
        return null;
    }

    private SpecialActivityElementGroupBO buildSpecialActivityElement(ElementCategoryEnum type, List<Product> products) {
        SpecialActivityElementGroupBO elementGroupBO = new SpecialActivityElementGroupBO();
        List<ElementBO<SpecialActivityValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<SpecialActivityValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            SpecialActivityValueMapBO valueMap = new SpecialActivityValueMapBO();
            elementBO.setValueMap(valueMap);
            //获取不到
            valueMap.setActivityType(16);
            valueMap.setDesc(getElementFeatureString("DESC", product));
            valueMap.setName(getElementFeatureString("NAME", product));
            valueMap.setNumberStock(product.getNum());
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setSpecialActivityElementList(elementBOS);
        return elementGroupBO;
    }

    private CateringElementGroupBO buildCateringElement(ElementCategoryEnum type, List<Product> products) {
        CateringElementGroupBO elementGroupBO = new CateringElementGroupBO();
        List<ElementBO<CateringValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<CateringValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            CateringValueMapBO valueMap = new CateringValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setPoiName(getElementFeatureString("POI_NAME", product));
            valueMap.setPoiId(getElementFeatureString("POIID", product));
            if (Objects.equals(product.getElement().getType(), ResourceType.FOOD.getType())) {
                //food
                CateringBusinessTypeEnum businessType = convertCateringBusinessType(getElementFeatureString("ELEMENT_TYPE", product));
                if (Objects.nonNull(businessType)) {
                    valueMap.setBusinessType(businessType.getType());
                }
            } else {
                //catering
                CateringTypeEnum cateringType = CateringTypeEnum.getByDesc(getElementFeatureString("ELEMENT_TYPE", product));
                if (Objects.nonNull(cateringType)) {
                    valueMap.setCateringType(cateringType.getType());
                }
                //默认为餐饮因为餐饮可能写不到商品上去
                valueMap.setBusinessType(CateringBusinessTypeEnum.CATERING.getType());

            }
            valueMap.setElementType(getElementFeatureString("ELEMENT_TYPE", product));
            valueMap.setName(getElementFeatureString("NAME", product));
            valueMap.setNumberStock(product.getNum());
            valueMap.setPoiName(getElementFeatureString("POI_NAME", product));
            valueMap.setHtml4(getElementFeatureString("HTML4", product));
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setCaterElementList(elementBOS);
        Product product = products.get(0);
        PlayThemeBO playThemeBO = getPlayThemeProps(ElementPlayThemeEnum.CATERING_ELEMENT_PLAY_THEME);
        List<PlayProperty> attributes = playThemeBO.getPlayThemeValue().get(0).getProperties();
        String foodType = getProductFeatureString("FOOD_TYPE", product);
        if (StringUtils.isNotBlank(foodType)) {
            attributes.add(getPlayThemeByName(ElementPlayThemeEnum.CATERING_ELEMENT_PLAY_THEME, 434L, foodType));
        }
        elementGroupBO.setPlayTheme(playThemeBO);
        return elementGroupBO;
    }

    /**
     * 根据id生成 PlayThemeBO
     *
     * @param playThemeEnum
     * @param attributeId
     * @param playThemeName
     * @return
     */
    private PlayProperty getPlayThemeByName(ElementPlayThemeEnum playThemeEnum, Long attributeId, String playThemeName) {
        TravelGuideResult<PlayType> playTypeResult = playTypeHelpService.getPlayType(playThemeEnum.getPlayThemeId());
        if (Objects.isNull(playTypeResult) || Objects.isNull(playTypeResult.getData())
                || CollectionUtils.isEmpty(playTypeResult.getData().getAttributes())) {
            return null;
        }
        PlayType parent = playTypeResult.getData();
        for (PlayTypeAttributes attribute : parent.getAttributes()) {
            if (attribute.getId().equals(attributeId)) {
                return new PlayProperty(
                        attributeId,
                        attribute.getName(),
                        attribute.getType(),
                        playThemeName,
                        0,
                        null
                );
            }
        }
        return null;
    }

    private PlayThemeBO getPlayThemeProps(ElementPlayThemeEnum playThemeEnum) {
        PlayThemeBO playThemeBO = new PlayThemeBO();
        playThemeBO.setPlayThemeProps(Arrays.asList(Arrays.asList(playThemeEnum.getParentPlayThemeId(), playThemeEnum.getPlayThemeId())));
        PlayMethodValueModel newLinePlayThemeBO = new PlayMethodValueModel();
        newLinePlayThemeBO.setKey(playThemeEnum.getPlayThemeId() == null ? null : playThemeEnum.getPlayThemeId().toString());
        newLinePlayThemeBO.setProperties(new ArrayList<>());
        playThemeBO.setPlayThemeValue(Arrays.asList(newLinePlayThemeBO));
        return playThemeBO;
    }

    CateringBusinessTypeEnum convertCateringBusinessType(String businessType) {
        for (CateringBusinessTypeEnum enums : CateringBusinessTypeEnum.values()) {
            if (Objects.equals(enums.getDesc(), businessType)) {
                return enums;
            }
        }
        return null;
    }

    private PhoneCardGroupBO buildPhoneCardElement(ElementCategoryEnum type, List<Product> products) {
        PhoneCardGroupBO elementGroupBO = new PhoneCardGroupBO();
        List<ElementBO<PhoneCardValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<PhoneCardValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            PhoneCardValueMapBO valueMap = new PhoneCardValueMapBO();
            elementBO.setValueMap(valueMap);

            PhoneCardTypeEnum size = PhoneCardTypeEnum.getByDesc(getElementFeatureString("SIZE", product));
            if (Objects.nonNull(size)) {
                valueMap.setCardType(size.getType());
            }
            CellularNetworkTypeEnum simNet = CellularNetworkTypeEnum.getByDesc(getElementFeatureString("SIM_NET", product));
            if (Objects.nonNull(simNet)) {
                valueMap.setCellularNetworkType(simNet.getType());
            }
            valueMap.setDesc(getElementFeatureString("DESC", product));
            valueMap.setName(getElementFeatureString("NAME", product));
            //获取不到
            valueMap.setDivision(null);
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setPhoneCardElementList(elementBOS);
        return elementGroupBO;
    }

    private WifiElementGroupBO buildWifiElement(ElementCategoryEnum type, List<Product> products) {
        WifiElementGroupBO elementGroupBO = new WifiElementGroupBO();
        List<ElementBO<WifiValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<WifiValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            WifiValueMapBO valueMap = new WifiValueMapBO();
            elementBO.setValueMap(valueMap);
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setWifiElementList(elementBOS);
        return elementGroupBO;
    }

    private TrafficElementGroupBO buildTrafficElement(ElementCategoryEnum type, List<Product> products) {
        TrafficElementGroupBO elementGroupBO = new TrafficElementGroupBO();
        List<ElementBO<TrafficValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<TrafficValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            TrafficValueMapBO valueMap = new TrafficValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setName(getElementFeatureString("NAME", product));
            valueMap.setElementType(getElementFeatureString("ELEMENT_TYPE", product));
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setTrafficElementList(elementBOS);
        return elementGroupBO;
    }

    private HotelElementGroupBO buildHotelElement(ElementCategoryEnum type, List<Product> products) {
        HotelElementGroupBO elementGroupBO = new HotelElementGroupBO();
        ArrayList<ElementBO<HotelElementValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<HotelElementValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            HotelElementValueMapBO valueMap = new HotelElementValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setHotelBO(HotelBO.builder()
                    .hotelId(getElementFeatureLong("SHID", product))
                    .hotelName(getElementFeatureString("NAME", product))
                    .poi(getElementFeatureString("POIID", product))
                    .build());
            valueMap.setRoomBO(RoomBO.builder()
                    .id(getElementFeatureLong("ROOM_TYPE_ID", product))
                    .name(getElementFeatureString("ELEMENT_TYPE", product))
                    .build());
            valueMap.setHotelName(getElementFeatureString("NAME", product));
            valueMap.setRoomName(getElementFeatureString("ELEMENT_TYPE", product));
            //暂时取不到
            valueMap.setAddress(null);
            valueMap.setBreakfastCount(getProductFeatureInteger("HOTEL_BREAKFAST", product));
            valueMap.setEarliestCheckInTime(getProductFeatureString("EARLIEST_CHECK_IN_TIME", product));
            valueMap.setLatestCheckOutTime(getProductFeatureString("LATEST_CHECK_OUT_TIME", product));
            valueMap.setNightNumber(product.getNum());
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setHotelElementList(elementBOS);
        return elementGroupBO;
    }


    private TicketElementGroupBO buildTicketElement(ElementCategoryEnum type, List<Product> products) {
        TicketElementGroupBO elementGroupBO = new TicketElementGroupBO();
        List<ElementBO<TicketElementValueMapBO>> elementBOS = new ArrayList<>();
        for (Product product : products) {
            ElementBO<TicketElementValueMapBO> elementBO = new ElementBO<>();
            elementBO.setId(product.getElement().getId());
            elementBO.setElementType(type.getType());
            TicketElementValueMapBO valueMap = new TicketElementValueMapBO();
            elementBO.setValueMap(valueMap);
            valueMap.setScenicSpotChargeItemBO(ScenicSpotChargeItemBO.builder()
                    .scenicSpotChargeItemId(getElementFeatureLong("SCENIC_PRODUCT_ID", product))
                    .scenicSpotChargeItemName(getElementFeatureString("SCENIC_PRODUCT_NAME", product))
                    .scenicSpotChargeItemDescription(null)
                    .scenicSpots(Arrays.asList(ScenicSpotBO.builder()
                            .scenicSpotId(getElementFeatureLong("SCENIC_ID", product))
                            .scenicSpotName(getElementFeatureString("NAME", product))
                            .poiId(getElementFeatureString("POIID", product))
                            .build()))
                    .build());
            valueMap.setTicketKindId(getElementFeatureLong("TICKET_KIND_VID", product));
            valueMap.setTicketKindName(getElementFeatureString("ELEMENT_TYPE", product));
            valueMap.setTicketSalePolicies(null);
            //会在后面处理是否需要指定入园 和 独立价库
            valueMap.setDoesAppointEnterParkDate(false);
            valueMap.setDoesInDependentPriceStock(false);
            valueMap.setAdmissionDates(null);
            valueMap.setNumberStock(product.getNum());
            coverValueMap(valueMap, product);
            elementBOS.add(elementBO);
        }
        elementGroupBO.setTicketElementList(elementBOS);
        return elementGroupBO;
    }

    private void coverValueMap(BaseElementValueMapBO valueMap, Product product) {
        //todo 默认需要赋值
        valueMap.setAdditionalRemarks(getProductFeatureString("DETAIL_DESC", product));
        valueMap.setOuterId(product.getElement().getOuterId());
        return;
    }

    private Long getElementFeatureLong(String key, Product product) {
        return product.getElement().getElementFeatures().stream()
                .filter(it -> Objects.equals(it.getFeatureKey(), key))
                .map(ElementFeature::getValue)
                .map(this::tryParseLong)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private Integer getElementFeatureInt(String key, Product product) {
        return product.getElement().getElementFeatures().stream()
                .filter(it -> Objects.equals(it.getFeatureKey(), key))
                .map(ElementFeature::getValue)
                .map(this::tryParseInteger)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private String getElementFeatureString(String key, Product product) {
        return product.getElement().getElementFeatures().stream()
                .filter(it -> Objects.equals(it.getFeatureKey(), key))
                .map(ElementFeature::getValue)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private String getProductFeatureString(String key, Product product) {
        return product.getProductFeatures().stream()
                .filter(it -> Objects.equals(it.getFeatureKey(), key))
                .map(ProductFeature::getValue)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private Integer getProductFeatureInteger(String key, Product product) {
        return product.getProductFeatures().stream()
                .filter(it -> Objects.equals(it.getFeatureKey(), key))
                .map(ProductFeature::getValue)
                .map(this::tryParseInteger)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private TravelPerson convert2TravelPerson(Combo combo) {
        Map<String, String> comboFeatures = combo.getComboFeatures();
        if (Objects.isNull(comboFeatures) || !comboFeatures.containsKey("tI")) {
            return null;
        }
        TravelPerson travelPerson = new TravelPerson();
        travelPerson.setTemplateId(tryParseLong(comboFeatures.get("tI")));
        return travelPerson;
    }

    private PlayAbroadPackageDirectInfo buildPackageDirectInfo(Combo combo, TravelItemStoreDO item) {

        if (item.getInventoryType() == 1 && Objects.equals(item.getSellType(), SellType.UNCALENDAR)) {
            //普通商品
            return Optional.ofNullable(combo.getSkuList())
                    .filter(CollectionUtils::isNotEmpty)
                    .map(skus -> skus.get(0))
                    .map(TravelItemSku::getSkuFeatures)
                    .filter(t -> t.containsKey(DirectConstants.PLAY_DIRECT) && t.containsKey(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO))
                    .map(t -> t.get(DirectConstants.DIRECT_SUPPLIER_PRODUCT_INFO))
                    .filter(StringUtils::isNotBlank)
                    .map(t -> JSON.parseObject(t, PlayAbroadPackageDirectInfo.class))
                    .orElse(null);
        }

        return null;
    }

    private Integer getPriceType(TravelItemSku sku, Map<String, String> comboFeatures) {
        if (CollectionUtils.isEmpty(sku.getPropertyList())) {
            return null;
        }
        Long value = sku.getPropertyList().stream()
                .filter(property -> Objects.equals(property.getPropertyId(), 8558246L))
                .map(ItemPVPair::getValueId)
                .findFirst().orElse(null);
        if (Objects.isNull(value)) {
            return null;
        }
        if (Boolean.FALSE.toString().equals(comboFeatures.get("doesIndependentStockForDifferentCrowds"))) {
            //如果是一口价
            return 0;
        }
        if (value == 3622544L) {
            return 1;
        } else if (value == 27845L) {
            return 2;
        } else if (value == 139829014L) {
            return 3;
        }
        return null;
    }

    private PlayThemeBO buildPlayTheme(Combo combo) {
        final String PLAY_PROP = "playProp";
        PlayThemeBO playThemeBO = new PlayThemeBO();
        String playProp = combo.getComboFeatures().get(PLAY_PROP);
        if (StringUtils.isNotBlank(playProp)) {
            try {
                return JSON.parseObject(playProp, PlayThemeBO.class);
            } catch (Exception e) {

            }
        }
        return playThemeBO;
    }

    private List<Integer> buildOtherElement(Combo combo, CompExtParam param) {
        List<Product> products = ProductUtils.assembleProducts(combo);
        if (products == null) {
            return new ArrayList<>();
        }
        List<Integer> elementsInside = new ArrayList<>();
        for (Product product : products) {
            if (product.isMain()) {
                continue;
            }
            ElementCategoryEnum byType = ElementCategoryEnum.getByTravelitemValue(getElementType(product));
            //转换成travelitem 类型, 后续给到前端还会转回来
            if (Objects.nonNull(byType)) {

                if (!elementsInside.contains(byType.getType())) {
                    //去重
                    elementsInside.add(byType.getType());
                }
            }
        }
        return elementsInside;
    }

    private Integer buildMainElement(Combo combo, CompExtParam param) {
        BasicCompDO<Integer> dependCompDO = param.getDependCompDO(CompConstants.MAIN_ELEMENT_COMP_NAME);
        return dependCompDO == null ? null : dependCompDO.getValue();
    }

    public static Integer getElementType(Product product) {
        if (Objects.isNull(product.getElement())) {
            return null;
        }
        //替换食物到美食
        return Objects.equals(product.getElement().getType(), ResourceType.FOOD.getType())
                ? ResourceType.RESTAURANT.getType()
                : product.getElement().getType();
    }

    private Long tryParseLong(String str) {
        try {
            return Long.parseLong(str);
        } catch (Exception e) {
            return 0L;
        }
    }

    private Integer tryParseInteger(String str) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            return null;
        }
    }
}
