package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.constants.ResourceType;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.travel.liteswitch.sp.play.enums.DivingEquipmentTypeEnum;
import com.fliggy.travel.liteswitch.sp.play.enums.DivingSpecialTypeEnum;
import com.fliggy.vic.common.constant.packageelements.DivingCertificationTypeEnum;
import com.fliggy.vic.common.constant.packageelements.DivingCoachRatioTypeEnum;
import com.fliggy.vic.common.constant.packageelements.DivingTypeEnum;
import com.fliggy.vic.common.shared.Specification;
import com.fliggy.vic.common.util.StreamUtils;
import com.fliggy.vpp.client.dto.response.line.element.DivingElement;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.poi.PoiDTO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.PlayThemeEnum;
import com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DivingElementConverter extends AbstractElementConverter implements IElementConverter {

    @Override
    public Integer getElementCategory() {
        return ElementCategoryEnum.DIVING.getType();
    }

    @Override
    public Integer getTravelItemElementCategory() {
        return ResourceType.DIVING.getType();
    }

    @Override
    public PackageElement toOvpElement(TravelItem travelItem, Combo combo, Product product) {
        return null;
    }

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        Product product = super.fromOvpElement(pElement, combo, playPackageInfoControlDO);
        Element element = product.getElement();
        List<ElementFeature> elementFeatures = element.getElementFeatures();

        DivingElement divingElement = (DivingElement) pElement;
        // 设置POI信息
        elementFeatures.addAll(fromPoiDTO(divingElement.getPoi()));
        // 潜水类型
        if (needBuildProductFeaturesFromPlayThemes(divingElement)) {
            buildElementFeaturesFromPlayThemes(divingElement.getPlayThemes(), elementFeatures, PlayThemeEnum.DIVING_TYPE);
            buildElementFeaturesFromPlayThemes(divingElement.getPlayThemes(), elementFeatures, PlayThemeEnum.DIVING_CERTIFICATION_TYPE);
            buildElementFeaturesFromPlayThemes(divingElement.getPlayThemes(), elementFeatures, PlayThemeEnum.DIVING_EQUIPMENT_LIST);
            buildElementFeaturesFromPlayThemes(divingElement.getPlayThemes(), elementFeatures, PlayThemeEnum.DIVING_SPECIAL_TYPE);
            buildElementFeaturesFromPlayThemes(divingElement.getPlayThemes(), elementFeatures, PlayThemeEnum.COACH_RATIO);
        } else {
            Optional.ofNullable(divingElement.getDivingType())
                    .map(DivingTypeEnum::getByCode)
                    .ifPresent(divingTypeEnum -> {
                        elementFeatures.add(new ElementFeature("DIVING_TYPE", divingTypeEnum.getDesc()));
                        elementFeatures.add(new ElementFeature("ELEMENT_TYPE", divingTypeEnum.getDesc()));
                    });
            Optional.ofNullable(divingElement.getDivingCertificationType()).map(DivingCertificationTypeEnum::getByCode).ifPresent(
                    divingCertificationTypeEnum -> elementFeatures.add(new ElementFeature("DIVING_CERTIFICATION_TYPE", divingCertificationTypeEnum.getDesc()))
            );
            // 特色装备
            if (CollectionUtils.isNotEmpty(divingElement.getDivingEquipmentTypes())) {
                String divingEquipmentList = StreamUtils.asStream(divingElement.getDivingEquipmentTypes()).map(DivingEquipmentTypeEnum::getByCode)
                        .filter(Objects::nonNull).map(DivingEquipmentTypeEnum::getDesc).collect(Collectors.joining(","));
                Optional.of(divingEquipmentList).filter(StringUtils::isNotBlank)
                        .ifPresent(equipments-> elementFeatures.add(new ElementFeature("DIVING_EQUIPMENT_LIST", equipments)));
            }
            // 特色潜水
            Optional.ofNullable(divingElement.getDivingSpecialType()).map(DivingSpecialTypeEnum::getByCode).ifPresent(
                    divingSpecialTypeEnum -> elementFeatures.add(new ElementFeature("DIVING_SPECIAL_TYPE", divingSpecialTypeEnum.getDesc()))
            );
            // 教练配比
            Optional.ofNullable(divingElement.getDivingCoachRatioType()).map(DivingCoachRatioTypeEnum::getByCode).ifPresent(
                    divingCoachRatioTypeEnum -> elementFeatures.add(new ElementFeature("COACH_RATIO", divingCoachRatioTypeEnum.getDesc()))
            );
        }


        // 数量规格
        Optional.ofNullable(divingElement.getSpecification())
                .map(Specification::getDescription)
                .filter(StringUtils::isNumeric)
                .map(Integer::parseInt)
                .map(QuantitySpecificationEnum::getByType)
                .map(QuantitySpecificationEnum::getLabel)
                .ifPresent(desc -> elementFeatures.add(new ElementFeature("SPECIFICATION", desc)));

        // element name，下游需要
        Optional.ofNullable(divingElement.getPoi())
                .map(PoiDTO::getPoiName)
                .ifPresent(name -> elementFeatures.add(new ElementFeature("NAME", name)));

        // 说明, 使用产品维度的
        Optional.ofNullable(divingElement.getAdditionalRemarks())
                .ifPresent(desc -> elementFeatures.add(new ElementFeature("DESC", desc)));
        // product Id和comboId
        setCustomProductIdAndElementId(product);

        return product;
    }
}
