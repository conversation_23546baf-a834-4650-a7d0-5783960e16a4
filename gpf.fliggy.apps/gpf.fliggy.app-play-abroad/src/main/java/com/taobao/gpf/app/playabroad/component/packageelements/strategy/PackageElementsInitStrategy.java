package com.taobao.gpf.app.playabroad.component.packageelements.strategy;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.compPlugin.ICompInitStrategy;
import com.taobao.gpf.app.playabroad.component.packageelements.model.PackageElementsControlDO;

/**
 * 套餐搭配元素初始化策略
 */
public class PackageElementsInitStrategy implements ICompInitStrategy<BasicCompDO<PackageElementsControlDO>> {

    @Override
    public BasicCompDO<PackageElementsControlDO> getInitCompDO() {
        BasicCompDO<PackageElementsControlDO> compDO = new BasicCompDO<>();
        compDO.setControlDO(new PackageElementsControlDO());
        return compDO;
    }

    @Override
    public PackageElementsControlDO getInitControlDO() {
        return new PackageElementsControlDO();
    }

    @Override
    public String getName() {
        return "packageElementInfoInitStrategy";
    }
} 