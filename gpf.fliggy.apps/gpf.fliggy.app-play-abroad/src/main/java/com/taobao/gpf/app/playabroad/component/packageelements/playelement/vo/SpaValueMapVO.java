package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpaValueMapVO extends BaseElementValueMapVO {

    /**
     * poi名称
     */
    @JsonProperty("POI_NAME")
    private String poiName;

    /**
     * 城市
     */
    @JsonProperty("CITY")
    private String city;

    /**
     * 库存
     */
    private Integer numberStock;
}
