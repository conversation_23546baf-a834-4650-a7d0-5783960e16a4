package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CateringElementGroupBO extends BasePlayElementGroupBO {

    /**
     * 餐饮元素列表
     */
    private List<ElementBO<CateringValueMapBO>> caterElementList;
}
