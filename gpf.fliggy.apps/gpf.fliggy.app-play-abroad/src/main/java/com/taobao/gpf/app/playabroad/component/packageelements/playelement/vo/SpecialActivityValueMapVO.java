package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpecialActivityValueMapVO extends BaseElementValueMapVO {

    /**
     * 库存
     */
    private Integer numberStock;

    /**
     * 活动类型
     * @see com.taobao.gpf.app.playabroad.constant.element.SpecialActivityTypeEnum
     */
    private Integer activityType;

    /**
     * 说明
     */
    @JsonProperty("ACTIVITY_CATEGORY_PATH_IDS")
    private String activityCategoryPathIds;
}
