package com.taobao.gpf.app.playabroad.component.reservedate;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.componet.standarddate.DateRangeValueDO;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

public class ReserveStartEndDateStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<DateRangeValueDO>> {

    @Override
    public void parseStore(BasicCompDO<DateRangeValueDO> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (storeDO.getBcStartTime() != null && storeDO.getBcEndTime() != null) {
            compDO.getValue().setStartDate(storeDO.getBcStartTime());
            compDO.getValue().setEntDate(storeDO.getBcEndTime());
        }
    }

    @Override
    public void transferStore(BasicCompDO<DateRangeValueDO> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (compDO.getValue() != null) {
            storeDO.setBcStartTime(compDO.getValue().getStartDate());
            storeDO.setBcEndTime(compDO.getValue().getEntDate());
        }
    }

    @Override
    public String getName() {
        return "reserveStartEndDateStoreStrategy";
    }

}
