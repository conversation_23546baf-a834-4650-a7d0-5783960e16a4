package com.taobao.gpf.app.playabroad.component.packageelements.convert.bo2vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.*;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo.*;
import com.taobao.gpf.app.playabroad.constant.element.CateringBusinessTypeEnum;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.constant.element.QuantitySpecificationEnum;
import com.taobao.gpf.domain.repository.TravelItemServiceRepo;
import com.taobao.gpf.domain.travelitem.element.ElementDataVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class PlayElementBO2VOConvertor {

    @Resource
    private PlayProductConvertorHelper playProductConvertorHelper;

    @Resource
    private TravelItemServiceRepo travelItemServiceRepo;

    public List<BasePlayElementGroupVO> convertElementBO2VO(List<BasePlayElementGroupBO> elementGroupBOList) {
        if (CollectionUtils.isEmpty(elementGroupBOList)) {
            return new ArrayList<>();
        }
        List<BasePlayElementGroupVO> elementGroupVOList = new ArrayList<>();
        for (BasePlayElementGroupBO basePlayElementGroupBO : elementGroupBOList) {

            ElementCategoryEnum elementCategoryEnum = ElementCategoryEnum.getByType(basePlayElementGroupBO.getElementCategory());
            if (Objects.isNull(elementCategoryEnum)) {
                continue;
            }
            BasePlayElementGroupVO basePlayElementGroupVO = new BasePlayElementGroupVO();
            switch (elementCategoryEnum) {
                case TICKET:
                    elementGroupVOList.add(this.dealWithTicketElementGroupVO((TicketElementGroupBO) basePlayElementGroupBO));
                    break;
                case HOTEL:
                    elementGroupVOList.add(this.dealWithHotelElementGroupVO((HotelElementGroupBO) basePlayElementGroupBO));
                    break;
                case TRAFFIC:
                    elementGroupVOList.add(this.dealWithTrafficElementGroupVO((TrafficElementGroupBO) basePlayElementGroupBO));
                    break;
                case WIFI:
                    elementGroupVOList.add(this.dealWithWifiElementGroupVO((WifiElementGroupBO) basePlayElementGroupBO));
                    break;
                case PHONECARD:
                    elementGroupVOList.add(this.dealWithPhoneCardGroupVO((PhoneCardGroupBO) basePlayElementGroupBO));
                    break;
                case CATERING:
                    elementGroupVOList.add(this.dealWithCateringElementGroupVO((CateringElementGroupBO) basePlayElementGroupBO));
                    break;
                case SPECIAL_ACTIVITY:
                    elementGroupVOList.add(this.dealWithSpecialActivityElementGroupVO((SpecialActivityElementGroupBO) basePlayElementGroupBO));
                    break;
                case SPA:
                    elementGroupVOList.add(this.dealWithSpaElementGroupVO((SpaElementGroupBO) basePlayElementGroupBO));
                    break;
                case TOUR:
                    elementGroupVOList.add(this.dealWithLocalTouristGuideElementGroupVO((LocalTouristGuideElementGroupBO) basePlayElementGroupBO));
                    break;
                case PHOTOGRAPHY:
                    elementGroupVOList.add(this.dealWithPhotographyElementGroupVO((PhotographyElementGroupBO) basePlayElementGroupBO));
                    break;
                case DIVING:
                    elementGroupVOList.add(this.dealWithDivingElementGroupVO((DivingElementGroupBO) basePlayElementGroupBO));
                    break;
                default:
                    break;
            }
        }


        return elementGroupVOList;
    }

    private TicketElementGroupVO dealWithTicketElementGroupVO(TicketElementGroupBO ticketElementGroupBO) {
        TicketElementGroupVO ticketElementGroupVO = playProductConvertorHelper.toTicketElementGroupVO(ticketElementGroupBO);
        ticketElementGroupVO.setElementCategory(ElementCategoryEnum.TICKET.getType());

        if (CollectionUtils.isNotEmpty(ticketElementGroupBO.getTicketElementList())) {
            TicketElementValueMapBO ticketElementValueMapBO = CollectionUtil.getFirst(ticketElementGroupBO.getTicketElementList()).getValueMap();
            if (Objects.nonNull(ticketElementValueMapBO)) {
                ticketElementGroupVO.setAdditionalRemarks(CollectionUtil.getFirst(ticketElementGroupBO.getTicketElementList()).getValueMap().getAdditionalRemarks());
            }
        }

        for (ElementVO<TicketElementValueMapVO> ticketElementValueMapVOElementVO : ticketElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(ticketElementValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            ticketElementValueMapVOElementVO.getValueMap().setScenicProductId(valueMapFeature.get("SCENIC_PRODUCT_ID"));
            ticketElementValueMapVOElementVO.getValueMap().setAreaName(valueMapFeature.get("AREA_NAME"));
            ticketElementValueMapVOElementVO.getValueMap().setAreaPv(valueMapFeature.get("AREA_PV"));
            ticketElementValueMapVOElementVO.getValueMap().setScenicId(valueMapFeature.get("SCENIC_ID"));
            ticketElementValueMapVOElementVO.getValueMap().setProductName(valueMapFeature.get("PRODUCT_NAME"));
            ticketElementValueMapVOElementVO.getValueMap().setEpisodeName(valueMapFeature.get("EPISODE_NAME"));
            ticketElementValueMapVOElementVO.getValueMap().setCity(valueMapFeature.get("CITY"));
            ticketElementValueMapVOElementVO.getValueMap().setEpisodePv(valueMapFeature.get("EPISODE_PV"));
            ticketElementValueMapVOElementVO.getValueMap().setTicketKindVid(valueMapFeature.get("TICKET_KIND_VID"));
            ticketElementValueMapVOElementVO.getValueMap().setDivisionId(valueMapFeature.get("DIVISION_ID"));
            ticketElementValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            ticketElementValueMapVOElementVO.getValueMap().setPoiId(valueMapFeature.get("POIID"));
            ticketElementValueMapVOElementVO.getValueMap().setElementType(valueMapFeature.get("ELEMENT_TYPE"));
            ticketElementValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
        }
        return ticketElementGroupVO;
    }

    private HotelElementGroupVO dealWithHotelElementGroupVO(HotelElementGroupBO hotelElementGroupBO) {
        HotelElementGroupVO hotelElementGroupVO = playProductConvertorHelper.toHotelElementGroupVO(hotelElementGroupBO);
        hotelElementGroupVO.setElementCategory(ElementCategoryEnum.HOTEL.getType());

        if (CollectionUtils.isNotEmpty(hotelElementGroupBO.getHotelElementList())) {
            HotelElementValueMapBO hotelElementValueMapBO = CollectionUtil.getFirst(hotelElementGroupBO.getHotelElementList()).getValueMap();
            if (Objects.nonNull(hotelElementValueMapBO)) {
                hotelElementGroupVO.setAdditionalRemarks(hotelElementValueMapBO.getAdditionalRemarks());
                hotelElementGroupVO.setEarliestCheckInTime(hotelElementValueMapBO.getEarliestCheckInTime());
                hotelElementGroupVO.setLatestCheckOutTime(hotelElementValueMapBO.getLatestCheckOutTime());
                hotelElementGroupVO.setAdditionalRule(hotelElementValueMapBO.getAdditionalRemarks());
            }
        }

        for (ElementVO<HotelElementValueMapVO> hotelElementValueMapVOElementVO : hotelElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(hotelElementValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            hotelElementValueMapVOElementVO.getValueMap().setCity(valueMapFeature.get("CITY"));
            hotelElementValueMapVOElementVO.getValueMap().setDivisionId(valueMapFeature.get("DIVISION_ID"));
            hotelElementValueMapVOElementVO.getValueMap().setShId(valueMapFeature.get("SHID"));
            hotelElementValueMapVOElementVO.getValueMap().setRoomTypeId(valueMapFeature.get("ROOM_TYPE_ID"));
            hotelElementValueMapVOElementVO.getValueMap().setPoiId(valueMapFeature.get("POIID"));
            hotelElementValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            hotelElementValueMapVOElementVO.getValueMap().setElementType(valueMapFeature.get("ELEMENT_TYPE"));
            hotelElementValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
        }
        return hotelElementGroupVO;
    }

    private TrafficElementGroupVO dealWithTrafficElementGroupVO(TrafficElementGroupBO trafficElementGroupBO) {
        TrafficElementGroupVO trafficElementGroupVO = playProductConvertorHelper.toTrafficElementGroupVO(trafficElementGroupBO);
        trafficElementGroupVO.setElementCategory(ElementCategoryEnum.TRAFFIC.getType());

        if (CollectionUtils.isNotEmpty(trafficElementGroupBO.getTrafficElementList())) {
            TrafficValueMapBO trafficValueMapBO = CollectionUtil.getFirst(trafficElementGroupBO.getTrafficElementList()).getValueMap();
            if (Objects.nonNull(trafficValueMapBO)) {
                trafficElementGroupVO.setAdditionalRemarks(trafficValueMapBO.getAdditionalRemarks());
            }
        }

        for (ElementVO<TrafficValueMapVO> trafficValueMapVOElementVO : trafficElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(trafficValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            trafficValueMapVOElementVO.getValueMap().setPoiId(valueMapFeature.get("POIID"));
            trafficValueMapVOElementVO.getValueMap().setElementType(valueMapFeature.get("ELEMENT_TYPE"));
            trafficValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
            trafficValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            trafficValueMapVOElementVO.getValueMap().setDesc(valueMapFeature.get("DESC"));
        }
        return trafficElementGroupVO;
    }

    private WifiElementGroupVO dealWithWifiElementGroupVO(WifiElementGroupBO wifiElementGroupBO) {
        WifiElementGroupVO wifiElementGroupVO = playProductConvertorHelper.toWifiElementGroupVO(wifiElementGroupBO);
        wifiElementGroupVO.setElementCategory(ElementCategoryEnum.WIFI.getType());

        if (CollectionUtils.isNotEmpty(wifiElementGroupBO.getWifiElementList())) {
            WifiValueMapBO wifiValueMapBO = CollectionUtil.getFirst(wifiElementGroupBO.getWifiElementList()).getValueMap();
            if (Objects.nonNull(wifiValueMapBO)) {
                wifiElementGroupVO.setAdditionalRemarks(wifiValueMapBO.getAdditionalRemarks());
            }
        }

        for (ElementVO<WifiValueMapVO> wifiValueMapVOElementVO : wifiElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(wifiValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            wifiValueMapVOElementVO.getValueMap().setPoiRelated(valueMapFeature.get("POI_RELATED"));
            wifiValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            wifiValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
            wifiValueMapVOElementVO.getValueMap().setDesc(valueMapFeature.get("DESC"));
        }
        return wifiElementGroupVO;
    }

    private PhoneCardGroupVO dealWithPhoneCardGroupVO(PhoneCardGroupBO phoneCardGroupBO) {
        PhoneCardGroupVO phoneCardGroupVO = playProductConvertorHelper.toPhoneCardElementGroupVO(phoneCardGroupBO);
        phoneCardGroupVO.setElementCategory(ElementCategoryEnum.PHONECARD.getType());

        if (CollectionUtils.isNotEmpty(phoneCardGroupBO.getPhoneCardElementList())) {
            PhoneCardValueMapBO phoneCardValueMapBO = CollectionUtil.getFirst(phoneCardGroupBO.getPhoneCardElementList()).getValueMap();
            if (Objects.nonNull(phoneCardValueMapBO)) {
                phoneCardGroupVO.setAdditionalRemarks(phoneCardValueMapBO.getAdditionalRemarks());
            }
        }

        for (ElementVO<PhoneCardValueMapVO> phoneCardValueMapVOElementVO : phoneCardGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(phoneCardValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            phoneCardValueMapVOElementVO.getValueMap().setSimNet(valueMapFeature.get("SIM_NET"));
            phoneCardValueMapVOElementVO.getValueMap().setSize(valueMapFeature.get("SIZE"));
            phoneCardValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            phoneCardValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
            phoneCardValueMapVOElementVO.getValueMap().setDesc(valueMapFeature.get("DESC"));
        }
        return phoneCardGroupVO;
    }

    private CateringElementGroupVO dealWithCateringElementGroupVO(CateringElementGroupBO cateringElementGroupBO) {
        CateringElementGroupVO cateringElementGroupVO = playProductConvertorHelper.toCateringElementGroupVO(cateringElementGroupBO);
        cateringElementGroupVO.setElementCategory(ElementCategoryEnum.CATERING.getType());

        if (CollectionUtils.isNotEmpty(cateringElementGroupBO.getCaterElementList())) {
            CateringValueMapBO cateringValueMapBO = CollectionUtil.getFirst(cateringElementGroupBO.getCaterElementList()).getValueMap();
            if (Objects.nonNull(cateringValueMapBO)) {
                cateringElementGroupVO.setAdditionalRemarks(cateringValueMapBO.getAdditionalRemarks());
                cateringElementGroupVO.setBusinessType(cateringValueMapBO.getBusinessType());
            }
        }

        if (BooleanUtil.isFalse(Objects.equals(cateringElementGroupVO.getBusinessType(), CateringBusinessTypeEnum.CATERING.getType()))) {
            PlayPoiVO playPoiVO = new PlayPoiVO();
            ElementBO<CateringValueMapBO> cateringValueMapBOElementBO = CollectionUtil.getFirst(cateringElementGroupBO.getCaterElementList());
            if (Objects.nonNull(cateringValueMapBOElementBO)) {
                playPoiVO.setPoiId(Objects.nonNull(cateringValueMapBOElementBO.getValueMap().getPoiId())
                        ? Long.valueOf(cateringValueMapBOElementBO.getValueMap().getPoiId()) : null);
                Map<String, Object> poiValue = new HashMap<>();
                poiValue.put("poiName", cateringValueMapBOElementBO.getValueMap().getPoiName());
                playPoiVO.setPoiValue(poiValue);
            }
            cateringElementGroupVO.setPlayPoiVO(playPoiVO);
        }

        for (ElementVO<CateringValueMapVO> cateringValueMapVOElementVO : cateringElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(cateringValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            cateringValueMapVOElementVO.getValueMap().setHtml4(valueMapFeature.get("html4"));
            cateringValueMapVOElementVO.getValueMap().setPoiId(valueMapFeature.get("POIID"));
            cateringValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            cateringValueMapVOElementVO.getValueMap().setElementType(valueMapFeature.get("ELEMENT_TYPE"));
            cateringValueMapVOElementVO.getValueMap().setPoiName(valueMapFeature.get("POI_NAME"));
            cateringValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
            cateringValueMapVOElementVO.getValueMap().setDesc(valueMapFeature.get("DESC"));
        }
        return cateringElementGroupVO;
    }

    private SpecialActivityElementGroupVO dealWithSpecialActivityElementGroupVO(SpecialActivityElementGroupBO specialActivityElementGroupBO) {
        SpecialActivityElementGroupVO specialActivityElementGroupVO = playProductConvertorHelper.toSpecialActivityElementGroupVO(specialActivityElementGroupBO);
        specialActivityElementGroupVO.setElementCategory(ElementCategoryEnum.SPECIAL_ACTIVITY.getType());

        if (CollectionUtils.isNotEmpty(specialActivityElementGroupBO.getSpecialActivityElementList())) {
            SpecialActivityValueMapBO specialActivityValueMapBO = CollectionUtil.getFirst(specialActivityElementGroupBO.getSpecialActivityElementList()).getValueMap();
            if (Objects.nonNull(specialActivityValueMapBO)) {
                specialActivityElementGroupVO.setAdditionalRemarks(specialActivityValueMapBO.getAdditionalRemarks());
            }
        }

        for (ElementVO<SpecialActivityValueMapVO> specialActivityValueMapVOElementVO : specialActivityElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(specialActivityValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            specialActivityValueMapVOElementVO.getValueMap().setPoiRelated(valueMapFeature.get("POI_RELATED"));
            specialActivityValueMapVOElementVO.getValueMap().setElementType(valueMapFeature.get("ELEMENT_TYPE"));
            specialActivityValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
            specialActivityValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            specialActivityValueMapVOElementVO.getValueMap().setDesc(valueMapFeature.get("DESC"));
            specialActivityValueMapVOElementVO.getValueMap().setActivityCategoryPathIds(valueMapFeature.get("ACTIVITY_CATEGORY_PATH_IDS"));
        }
        return specialActivityElementGroupVO;
    }

    private SpaElementGroupVO dealWithSpaElementGroupVO(SpaElementGroupBO spaElementGroupBO) {
        SpaElementGroupVO spaElementGroupVO = playProductConvertorHelper.toSpaElementGroupVO(spaElementGroupBO);
        spaElementGroupVO.setElementCategory(ElementCategoryEnum.SPA.getType());

        if (CollectionUtils.isNotEmpty(spaElementGroupBO.getSpaElementList())) {
            SpaValueMapBO spaValueMapBO = CollectionUtil.getFirst(spaElementGroupBO.getSpaElementList()).getValueMap();
            if (Objects.nonNull(spaValueMapBO)) {
                spaElementGroupVO.setAdditionalRemarks(spaValueMapBO.getAdditionalRemarks());
                spaElementGroupVO.setSpaUsageScope(spaValueMapBO.getSpaUsageScope());
            }
        }

        for (ElementVO<SpaValueMapVO> spaValueMapVOElementVO : spaElementGroupVO.getElementValueMapList()) {
            List<ElementDataVO> elementDataVOList = travelItemServiceRepo.searchByUniqKey(spaValueMapVOElementVO.getId());
            if (CollectionUtils.isEmpty(elementDataVOList)) {
                continue;
            }
            ElementDataVO elementDataVO = CollectionUtil.getFirst(elementDataVOList);
            Map<String, String> valueMapFeature = elementDataVO.getValueMap();
            spaValueMapVOElementVO.getValueMap().setCity(valueMapFeature.get("CITY"));
            spaValueMapVOElementVO.getValueMap().setPoiId(valueMapFeature.get("POIID"));
            spaValueMapVOElementVO.getValueMap().setName(valueMapFeature.get("NAME"));
            spaValueMapVOElementVO.getValueMap().setOuterId(valueMapFeature.get("OUTER_ID"));
            spaValueMapVOElementVO.getValueMap().setPoiRelated(valueMapFeature.get("POI_RELATED"));
            spaValueMapVOElementVO.getValueMap().setPoiName(valueMapFeature.get("POI_NAME"));
        }
        return spaElementGroupVO;
    }

    private LocalTouristGuideElementGroupVO dealWithLocalTouristGuideElementGroupVO(LocalTouristGuideElementGroupBO localTouristGuideElementGroupBO) {
        LocalTouristGuideElementGroupVO localTouristGuideElementGroupVO = playProductConvertorHelper.toTourElementGroupVO(localTouristGuideElementGroupBO);
        localTouristGuideElementGroupVO.setElementCategory(ElementCategoryEnum.TOUR.getType());

        if (CollectionUtils.isNotEmpty(localTouristGuideElementGroupBO.getLocalTouristGuideElementList())) {
            LocalTouristGuideValueMapBO localTouristGuideValueMapBO = CollectionUtil.getFirst(localTouristGuideElementGroupBO.getLocalTouristGuideElementList()).getValueMap();
            if (Objects.nonNull(localTouristGuideValueMapBO)) {
                localTouristGuideElementGroupVO.setLanguage(localTouristGuideValueMapBO.getLanguage());
                localTouristGuideElementGroupVO.setOuterId(localTouristGuideValueMapBO.getOuterId());
                localTouristGuideElementGroupVO.setAdditionalRemarks(localTouristGuideValueMapBO.getAdditionalRemarks());
                localTouristGuideElementGroupVO.setHasTicket(localTouristGuideValueMapBO.getHasTicket());
                localTouristGuideElementGroupVO.setBizType(localTouristGuideValueMapBO.getBizType());
                localTouristGuideElementGroupVO.setRelationLecturerId(localTouristGuideValueMapBO.getRelationLecturerId());
                localTouristGuideElementGroupVO.setLecturerNickName(localTouristGuideValueMapBO.getLecturerNickName());
                localTouristGuideElementGroupVO.setLecturerRealName(localTouristGuideValueMapBO.getLecturerRealName());
                if (Objects.nonNull(localTouristGuideValueMapBO.getType())) {
                    QuantitySpecificationEnum quantitySpecificationEnum = QuantitySpecificationEnum.getByType(Integer.valueOf(localTouristGuideValueMapBO.getType()));
                    if (Objects.nonNull(quantitySpecificationEnum)) {
                        localTouristGuideElementGroupVO.setType(quantitySpecificationEnum.getType());
                        localTouristGuideElementGroupVO.setNumberStock(localTouristGuideValueMapBO.getNumberStock());
                    }
                }
            }
        }
        return localTouristGuideElementGroupVO;
    }

    private PhotographyElementGroupVO dealWithPhotographyElementGroupVO(PhotographyElementGroupBO photographyElementGroupBO) {
        PhotographyElementGroupVO photographyElementGroupVO = playProductConvertorHelper.toPhotographyElementGroupVO(photographyElementGroupBO);
        photographyElementGroupVO.setElementCategory(ElementCategoryEnum.PHOTOGRAPHY.getType());

        if (CollectionUtils.isNotEmpty(photographyElementGroupBO.getPhotographyElementList())) {
            PhotographyValueMapBO photographyValueMapBO = CollectionUtil.getFirst(photographyElementGroupBO.getPhotographyElementList()).getValueMap();
            if (Objects.nonNull(photographyValueMapBO)) {
                photographyElementGroupVO.setAdditionalRemarks(photographyValueMapBO.getAdditionalRemarks());
                photographyElementGroupVO.setClothingNumber(photographyValueMapBO.getClothingNumber());
                photographyElementGroupVO.setOuterId(photographyValueMapBO.getOuterId());
                photographyElementGroupVO.setAddress(photographyValueMapBO.getAddress());
                photographyElementGroupVO.setDays(photographyValueMapBO.getDays());
                photographyElementGroupVO.setPhotographyStyle(photographyValueMapBO.getPhotographyStyle());
            }
        }

        return photographyElementGroupVO;
    }

    private DivingElementGroupVO dealWithDivingElementGroupVO(DivingElementGroupBO divingElementGroupBO) {
        DivingElementGroupVO divingElementGroupVO = new DivingElementGroupVO();
        DivingElementValueMapBO divingElementBO = CollectionUtil.getFirst(divingElementGroupBO.getDivingElementList());
        List<DivingElementVO> divingElementList = new ArrayList<>();
        DivingElementVO divingElementVO = new DivingElementVO();
        BeanUtils.copyProperties(divingElementBO, divingElementVO);
        BeanUtils.copyProperties(divingElementBO, divingElementGroupVO);
        BeanUtils.copyProperties(divingElementGroupBO, divingElementGroupVO);
        divingElementList.add(divingElementVO);
        divingElementGroupVO.setDivingElementList(divingElementList);
        divingElementGroupVO.setElementCategory(ElementCategoryEnum.DIVING.getType());

        if (Objects.nonNull(divingElementBO.getType())) {
            QuantitySpecificationEnum quantitySpecificationEnum = QuantitySpecificationEnum.getByType(Integer.valueOf(divingElementBO.getType()));
            if (Objects.nonNull(quantitySpecificationEnum)) {
                divingElementGroupVO.setType(quantitySpecificationEnum.getType());
            }
        } else {
            divingElementGroupVO.setNumberStock(null);
        }
        return divingElementGroupVO;
    }
}