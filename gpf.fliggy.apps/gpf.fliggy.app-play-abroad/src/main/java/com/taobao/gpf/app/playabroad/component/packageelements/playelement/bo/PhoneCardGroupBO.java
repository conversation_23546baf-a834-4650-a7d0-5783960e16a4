package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneCardGroupBO extends BasePlayElementGroupBO {

    /**
     * 电话卡元素列表
     */
    private List<ElementBO<PhoneCardValueMapBO>> phoneCardElementList;
}
