package com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo;

import com.taobao.gpf.app.playabroad.constant.element.SpaUsageScopeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpaValueMapBO extends BaseElementValueMapBO {

    /**
     * poi名称
     */
    private String poiName;

    /**
     * 城市
     */
    private String city;

    /**
     * spa店用途范围
     *
     * @see SpaUsageScopeEnum
     */
    private Integer spaUsageScope;
}
