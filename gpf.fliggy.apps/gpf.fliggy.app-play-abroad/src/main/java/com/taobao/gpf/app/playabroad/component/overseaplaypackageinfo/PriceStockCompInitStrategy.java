package com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.compPlugin.ICompInitStrategy;
import com.taobao.gpf.app.playabroad.model.PlaySkuPriceStockControlDO;

public class PriceStockCompInitStrategy implements ICompInitStrategy<BasicCompDO<String>> {

    @Override
    public String getName() {
        return "priceStockCompInitStrategy";
    }

    @Override
    public BasicCompDO<String> getInitCompDO() {
        BasicCompDO<String> compDO = new BasicCompDO<>();
        PlaySkuPriceStockControlDO priceStockControlDO = new PlaySkuPriceStockControlDO();
        compDO.setControlDO(priceStockControlDO);
        return compDO;
    }

    @Override
    public PlaySkuPriceStockControlDO getInitControlDO() {
       return new PlaySkuPriceStockControlDO();
    }

    

}
