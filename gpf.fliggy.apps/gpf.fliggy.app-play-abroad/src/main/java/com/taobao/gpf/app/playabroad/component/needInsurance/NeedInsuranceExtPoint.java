package com.taobao.gpf.app.playabroad.component.needInsurance;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.common.exception.ErrorCode;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoListControlDO;
import com.taobao.gpf.app.playabroad.component.packageelements.playelement.bo.BasePlayElementGroupBO;
import com.taobao.gpf.app.playabroad.constant.element.ElementCategoryEnum;
import com.taobao.gpf.app.playabroad.model.PlayThemeBO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.common.constants.NeedInsuranceConcreteEnum;
import com.taobao.gpf.domain.component.insurance.HighRiskItemInfoHelper;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodValueModel;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.constant.CategoryIdConstants;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.helper.PlatformInsuranceHelper;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.GraySwitchUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.util.*;

/**
* <AUTHOR>
* @Date  2025/5/26 17:12
**/
@Slf4j
public class NeedInsuranceExtPoint implements IInjectExtension {


    @Resource
    private TravelSellConfig travelSellConfig;

    @Resource
    private HighRiskItemInfoHelper highRiskItemInfoHelper;

    @Resource
    private PlatformInsuranceHelper platformInsuranceHelper;

    @Check(key = "abroadPlay-insurance-required-check")
    public CheckResult abroadPlayInsuranceCheck(CompExtParam param, BasicCompDO<String> compDO, @Category StdCategoryDO category) {

        CheckResult result = new CheckResult();
        // 获取类目的ID
        long catId = category.getCategoryId();
        Long sellerId = FliggyParamUtil.getUserDO(param).getUserId();
        //该校验点只检测境外玩乐套餐  *********
        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID) {
            return result;
        }

        AbstractCompDO abroadPlayComponent = param.getDependCompDO(CompConstants.OVERSEA_PLAY_PACKAGE_COMP_NAME);
        if (abroadPlayComponent == null || abroadPlayComponent.getControlDO() == null) {
            return result;
        }
        OverseaPlayPackageInfoListControlDO overseaPlayPackageInfoListControlDO = (OverseaPlayPackageInfoListControlDO) abroadPlayComponent.getControlDO();

        //切流逻辑，切流后采用新的高危玩法校验方式
        if (!GraySwitchUtils.isOnFor(FliggySwitchConfig.ABROAD_PLAY_PLATFORM_INSURANCE_GRAY, sellerId)) {
            for (OverseaPlayPackageInfoControlDO packageInfo : overseaPlayPackageInfoListControlDO.getPackageInfos()) {
                for (BasePlayElementGroupBO basePlayElementGroupBO : packageInfo.getPackageElementsControlDO().getElementInfoList()) {
                    if (Objects.equals(basePlayElementGroupBO.getElementCategory(), ElementCategoryEnum.DIVING.getType())) {
                        result.addErrorCode(FliggyErrorEnum.DIVE_MUST_HAVE_INSURANCE.getErrorCode());
                        return result;
                    }
                }
            }
        }else {
            boolean highRiskFlag = false;
            List<String> packageNameList = new ArrayList<>();

            // 检查高风险玩法ID
            for (OverseaPlayPackageInfoControlDO packageInfo : overseaPlayPackageInfoListControlDO.getPackageInfos()) {
                packageNameList.add(packageInfo.getPackageName());
                PlayThemeBO playThemeBO = packageInfo.getPlayThemeBO();

                // 使用Stream API检查是否包含高风险ID
                highRiskFlag = playThemeBO.getPlayThemeValue().stream()
                        .map(PlayMethodValueModel::getKey)
                        .filter(NumberUtils::isDigits)
                        .map(Long::valueOf)
                        .anyMatch(FliggySwitchConfig.HIGH_RISK_PLAY_ID_LIST::contains);

                if (highRiskFlag) {
                    break;
                }
            }

            // 如果没有高风险玩法ID，再检查高风险关键词
            if (!highRiskFlag) {
                Set<String> highRiskWordSet = highRiskItemInfoHelper.getHighRiskWordSet(param);

                // 使用双重流检查包名是否包含高风险关键词
                highRiskFlag = packageNameList.stream()
                        .anyMatch(packageName ->
                                highRiskWordSet.stream()
                                        .anyMatch(packageName::contains)
                        );
            }


            String needInsurance = compDO.getValue();
            if (highRiskFlag) {
                if (!Objects.equals(needInsurance, "3")) {
                    result.addErrorCode(FliggyErrorEnum.HIGH_RISK_INSURANCE_REQUIRE_ERROR.getErrorCode());
                    return result;
                }else {
                    AbstractCompDO platFormInsuranceProductComp = param.getDependCompDO(CompConstants.PLATFORM_INSURANCE_PRODUCT_INFO);
                    if (Objects.isNull(platFormInsuranceProductComp) || Objects.isNull(platFormInsuranceProductComp.getValue())) {
                        result.addErrorCode(FliggyErrorEnum.HIGH_RISK_INSURANCE_REQUIRE_ERROR.getErrorCode());
                    }else{
                        String[] platFormInsuranceProductInfoStrList = platFormInsuranceProductComp.getValue().toString().split("-");
                        if (platFormInsuranceProductInfoStrList.length < 4) {
                            result.addErrorCode(FliggyErrorEnum.HIGH_RISK_INSURANCE_REQUIRE_ERROR.getErrorCode());
                        } else {
                            Long premiumId = Long.valueOf(platFormInsuranceProductInfoStrList[2]);
                            Map<Long, String> highRiskPlatformInsuranceIdNameMap = platformInsuranceHelper.getHighRiskPlatformInsuranceIdNameMap(param.getReq().getUserId(), catId, true);
                            if (MapUtils.isNotEmpty(highRiskPlatformInsuranceIdNameMap)) {
                                if (!highRiskPlatformInsuranceIdNameMap.containsKey(premiumId)) {
                                    Map.Entry<Long, String> firstEntry = highRiskPlatformInsuranceIdNameMap.entrySet().iterator().next();
                                    result.addErrorCode(new ErrorCode(FliggyErrorEnum.HIGH_RISK_INSURANCE_DETAIL_REQUIRE_ERROR.name(), FliggyErrorEnum.HIGH_RISK_INSURANCE_DETAIL_REQUIRE_ERROR.getMessage() + firstEntry.getValue()));
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    @Check(key = "abroadPlay-platformItemInsuranceInfo-check")
    public CheckResult abroadPlayPlatformItemInsuranceInfoCheck(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO category) {

        CheckResult result = new CheckResult();
        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID) {
            return result;
        }

        BasicCompDO<String> sellTypeComp = param.getDependCompDO(CompConstants.PLATFORM_ITEM_INSURANCE_INFO);
        if (sellTypeComp == null || sellTypeComp.getValue() == null || !sellTypeComp.getValue().equals(NeedInsuranceConcreteEnum.SYSTEM_INSURANCE.getValue())) {
            return result;
        }

        if (compDO == null || compDO.getValue() == null) {
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("platformItemInsuranceInfo", "商业保险信息"));
            return result;
        }
        return result;
    }

    @Check(key = "abroadPlay-platformItemInsuranceCompanyInfo-check")
    public CheckResult abroadPlayPlatformItemInsuranceCompanyInfoCheck(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO category) {

        CheckResult result = new CheckResult();
        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID) {
            return result;
        }

        BasicCompDO<String> sellTypeComp = param.getDependCompDO(CompConstants.PLATFORM_ITEM_INSURANCE_INFO);
        if (sellTypeComp == null || sellTypeComp.getValue() == null || !sellTypeComp.getValue().equals(NeedInsuranceConcreteEnum.SYSTEM_INSURANCE.getValue())) {
            return result;
        }

        if (compDO == null || compDO.getValue() == null) {
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("platformItemInsuranceCompanyInfo", "请选择保险公司"));
            return result;
        }
        return result;
    }

    //校验选择保险方案 platformInsuranceProductInfo
    @Check(key = "abroadPlay-platformInsuranceProductInfo-check")
    public CheckResult abroadPlayPlatformInsuranceProductInfoCheck(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO category) {
        CheckResult result = new CheckResult();
        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID) {
            return result;
        }
        BasicCompDO<String> sellTypeComp = param.getDependCompDO(CompConstants.PLATFORM_ITEM_INSURANCE_INFO);
        if (sellTypeComp == null || sellTypeComp.getValue() == null || !sellTypeComp.getValue().equals(NeedInsuranceConcreteEnum.SYSTEM_INSURANCE.getValue())) {
            return result;
        }
        if (compDO == null || compDO.getValue() == null) {
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("platformInsuranceProductInfo", "请选择保险方案"));
            return result;
        }
        return result;
    }


    //请查看并勾选协议 platformInsuranceConfirm
    @Check(key = "abroadPlay-platformInsuranceConfirm-check")
    public CheckResult abroadPlayPlatformInsuranceConfirmCheck(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO category) {
        CheckResult result = new CheckResult();
        if (category.getCategoryId() != CategoryIdConstants.PLAY_ABROAD_CATEGORY_ID) {
            return result;
        }
        BasicCompDO<String> sellTypeComp = param.getDependCompDO(CompConstants.PLATFORM_ITEM_INSURANCE_INFO);
        if (sellTypeComp == null || sellTypeComp.getValue() == null || !sellTypeComp.getValue().equals(NeedInsuranceConcreteEnum.SYSTEM_INSURANCE.getValue())) {
            return result;
        }
        if (compDO == null || compDO.getValue() == null) {
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("platformInsuranceConfirm", "请查看并勾选协议"));
            return result;
        }
        return result;
    }

}
