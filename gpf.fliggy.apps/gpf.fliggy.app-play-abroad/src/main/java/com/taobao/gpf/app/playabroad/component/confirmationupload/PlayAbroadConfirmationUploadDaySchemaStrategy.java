package com.taobao.gpf.app.playabroad.component.confirmationupload;

import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.compdo.TabbedCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.schemastrategy.FliggyTabbedNavComplexFieldSchemaStratgey;
import com.taobao.top.schema.field.ComplexField;
import com.taobao.top.schema.field.Field;
import com.taobao.top.schema.field.InputField;
import com.taobao.top.schema.rule.Rule;
import com.taobao.top.schema.rule.TipRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 境外玩乐业务身份下确认书上传规则组件的schema策略类
 */
@Slf4j
public class PlayAbroadConfirmationUploadDaySchemaStrategy extends FliggyTabbedNavComplexFieldSchemaStratgey {

    @Override
    public String getName() {
        return "playAbroadConfirmationUploadDaySchemaStrategy";
    }

    @Override
    protected void customRenderField(ComplexField field, TabbedCompDO<List<TextValueModel>> compDO, 
                                   CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        // 首先调用父类方法完成基本设置
        super.customRenderField(field, compDO, param, compConfig, context);
        
        // 添加自定义的业务处理逻辑
        List<Rule> rules = new ArrayList<>();
        rules.add(new TipRule("设置确认书上传规则, 不上传、出行日前5天或自定义天数"));
        field.addRules(rules);
    }

    @Override
    protected void setCompValue(TabbedCompDO<List<TextValueModel>> compDO, ComplexField field,
                                CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        // 首先从field中获取所选择的规则和自定义天数
        String selectedRule = null;
        Integer customDayValue = null;
        
        if (field.getComplexValue() != null) {
            // 获取选择的规则
            Field selectField = field.getComplexValue().getValueField("confirmationUploadDaySelect");
            if (selectField != null && selectField instanceof InputField && ((InputField)selectField).getValue() != null) {
                selectedRule = ((InputField)selectField).getValue().getValue();
            }
            
            // 获取自定义的天数
            Field customDayField = field.getComplexValue().getValueField("customDay");
            if (customDayField != null && customDayField instanceof InputField && ((InputField)customDayField).getValue() != null) {
                String customDayStr = ((InputField)customDayField).getValue().getValue();
                if (StringUtils.isNumeric(customDayStr)) {
                    customDayValue = Integer.parseInt(customDayStr);
                }
            }
        }
        
        // 直接调用父类方法处理更多细节，避免处理navCompDO值类型问题
        super.setCompValue(compDO, field, param, compConfig, context);
        
        // 如果选择的是"自定义天数"，还需要设置customDay子组件的值
        if (StringUtils.isNotEmpty(selectedRule) && "customUpload".equals(selectedRule) && customDayValue != null) {
            for (AbstractCompDO childCompDO : compDO.getChildren()) {
                if (!(childCompDO instanceof ParentCompDO)) {
                    continue;
                }
                
                ParentCompDO parentCompDO = (ParentCompDO)childCompDO;
                if (!"customUpload".equals(parentCompDO.getCompName()) || CollectionUtils.isEmpty(parentCompDO.getChildren())) {
                    continue;
                }
                
                BasicCompDO customDayCompDO = (BasicCompDO)parentCompDO.getChildren().get(0);
                customDayCompDO.setValue(String.valueOf(customDayValue));
                break;
            }
        }
    }
} 