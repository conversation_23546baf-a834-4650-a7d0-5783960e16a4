package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CateringElementGroupVO extends BasePlayElementGroupVO {

    /**
     * 业务类型
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.CateringTypeEnum
     */
    private Integer businessType;

    /**
     * poi信息
     */
    private PlayPoiVO playPoiVO;

    /**
     * 餐饮元素列表
     */
    private List<ElementVO<CateringValueMapVO>> elementValueMapList;


    /**
     * 数量
     */
    private Integer numberStock;
}
