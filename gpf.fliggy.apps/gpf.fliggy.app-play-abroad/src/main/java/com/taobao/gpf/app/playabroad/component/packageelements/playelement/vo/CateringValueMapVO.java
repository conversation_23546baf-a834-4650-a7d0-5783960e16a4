package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CateringValueMapVO extends BaseElementValueMapVO {

    /**
     * 餐饮poi名称
     */
    @JsonProperty("POI_NAME")
    private String poiName;

    /**
     * html4
     */
    private String html4;

    /**
     * 餐饮库存
     */
    private Integer numberStock;
}
