package com.taobao.gpf.app.playabroad.component.packageelements.convert.ovp2combo;

import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fliggy.vic.common.shared.Specification;
import com.fliggy.vic.common.shared.play.PlayTheme;
import com.fliggy.vic.common.shared.play.PlayThemeAttributeValue;
import com.fliggy.vpp.client.dto.response.line.element.PackageElement;
import com.fliggy.vpp.client.dto.response.line.poi.PoiDTO;
import com.taobao.gpf.app.playabroad.component.overseaplaypackageinfo.model.OverseaPlayPackageInfoControlDO;
import com.taobao.gpf.app.playabroad.constant.element.PlayThemeEnum;
import com.taobao.tradespi.utils.CollectionUtils;
import com.taobao.travel.client.domain.dataobject.structured.Element;
import com.taobao.travel.client.domain.dataobject.structured.ElementFeature;
import com.taobao.travel.client.domain.dataobject.structured.Product;
import com.taobao.travel.client.domain.dataobject.structured.ProductFeature;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class AbstractElementConverter implements IElementConverter {

    //元素详细说明
    protected final static String KEY_DETAIL_DESC = "DETAIL_DESC";
    /**
     * 元素使用说明，套餐维度
     */
    protected final static String INSTRUCTIONS = "INSTRUCTIONS";

    @Override
    public Product fromOvpElement(PackageElement pElement, Combo combo, OverseaPlayPackageInfoControlDO playPackageInfoControlDO) {
        Product product = new Product();
        Element element = new Element();
        // 主元素
        product.setMain(getElementCategory().equals(playPackageInfoControlDO.getMainElementType()));
        product.setElement(element);
        product.setProductFeatures(new ArrayList<>());
        List<ElementFeature> elementFeatures = new ArrayList<>();
        element.setElementFeatures(elementFeatures);
        // 元素库Id
        if (Objects.nonNull(pElement.getOuterCode())) {
            element.setId(Long.valueOf(pElement.getOuterCode()));
        }
        // 元素类型
        element.setType(getTravelItemElementCategory());
        // 数量
        buildProductNum(product, pElement);
        // product features
        buildProductFeatures(product, pElement);
        // 产品维度使用说明
        buildElementInstruction(element, pElement);
        return product;
    }

    protected void buildProductNum(Product product, PackageElement pElement) {
        // 元素个数兜底设置为1份
        product.setNum(Optional.ofNullable(pElement.getSpecification())
                .map(Specification::getQuantity)
                .orElse(1));
        product.getProductFeatures().add(new ProductFeature("num", String.valueOf(product.getNum())));
    }

    protected void buildProductFeatures(Product product, PackageElement packageElement) {
        if (Objects.isNull(packageElement.getAdditionalRemarks())) {
            return;
        }
        product.getProductFeatures().add(new ProductFeature(KEY_DETAIL_DESC, packageElement.getAdditionalRemarks()));
    }

    /**
     * 获取travelItem元素类型
     */
    protected abstract Integer getTravelItemElementCategory();

    protected List<ElementFeature> fromPoiDTO(PoiDTO poiDTO) {
        List<ElementFeature> elementFeatures = new ArrayList<>();
        if (Objects.nonNull(poiDTO)) {
            elementFeatures.add(new ElementFeature("POI_NAME", poiDTO.getPoiName()));
            elementFeatures.add(new ElementFeature("POIID", poiDTO.getPoiId()));
            elementFeatures.add(new ElementFeature("POI_RELATED", "否"));
        } else {
            elementFeatures.add(new ElementFeature("POI_RELATED", "是"));
        }
        return elementFeatures;
    }

    protected void setCustomProductIdAndElementId(Product product) {
        Long productId = (long)getTravelItemElementCategory();
        product.setId(productId);
        product.getElement().setId(productId);
    }

    protected void buildElementInstruction(Element element, PackageElement packageElement) {
        // 说明, 使用产品维度的
        Optional.ofNullable(packageElement.getAdditionalRemarks())
                .ifPresent(desc -> element.getElementFeatures().add(new ElementFeature("INSTRUCTIONS", desc)));
    }

    public Boolean needBuildProductFeaturesFromPlayThemes(PackageElement pElement) {
        return CollectionUtils.isNotEmpty(pElement.getPlayThemes());
    }

    public void buildProductFeaturesFromPlayThemes(List<PlayTheme> playThemes, List<ProductFeature> productFeatures, PlayThemeEnum playThemeEnum) {
        if (Objects.isNull(playThemes.get(0)) || CollectionUtils.isEmpty(playThemes.get(0).getPlayThemeAttributes())) {
            return;
        }
        playThemes.get(0).getPlayThemeAttributes().stream().
                filter(playThemeAttribute -> Objects.equals(playThemeAttribute.getAttributeId(), playThemeEnum.getAttributeId())).
                findFirst().ifPresent(playThemeAttribute -> {
                    List<PlayThemeAttributeValue> values = playThemeAttribute.getAttributeValues();
                    if (CollectionUtils.isEmpty(values) || Objects.isNull(values.get(0))) {
                        return;
                    }
                    playThemeEnum.getFeatureKeys().forEach(
                            featureKey -> productFeatures.add(new ProductFeature(featureKey, values.get(0).getValue()))
                    );
                });
    }

    public void buildElementFeaturesFromPlayThemes(List<PlayTheme> playThemes, List<ElementFeature> elementFeatures, PlayThemeEnum playThemeEnum) {
        if (Objects.isNull(playThemes.get(0)) || CollectionUtils.isEmpty(playThemes.get(0).getPlayThemeAttributes())) {
            return;
        }
        playThemes.get(0).getPlayThemeAttributes().stream().
                filter(playThemeAttribute -> Objects.equals(playThemeAttribute.getAttributeId(), playThemeEnum.getAttributeId())).
                findFirst().ifPresent(playThemeAttribute -> {
                    List<PlayThemeAttributeValue> values = playThemeAttribute.getAttributeValues();
                    if (CollectionUtils.isEmpty(values) || Objects.isNull(values.get(0))) {
                        return;
                    }
                    if (values.size() == 1) {
                        playThemeEnum.getFeatureKeys().forEach(
                                featureKey -> elementFeatures.add(new ElementFeature(featureKey, values.get(0).getValue()))
                        );
                    } else {
                        playThemeEnum.getFeatureKeys().forEach(
                                featureKey -> elementFeatures.add(new ElementFeature(featureKey,
                                        values.stream().map(PlayThemeAttributeValue::getValue).filter(Objects::nonNull).collect(Collectors.joining(","))))
                        );
                    }
                });
    }
}
