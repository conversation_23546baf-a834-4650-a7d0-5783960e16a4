package com.taobao.gpf.app.playabroad.component.packageelements.playelement.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PhoneCardValueMapVO extends BaseElementValueMapVO {

    /**
     * 网络类型
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.CellularNetworkTypeEnum
     */
    @JsonProperty("SIM_NET")
    private String simNet;

    /**
     * 卡类型
     *
     * @see com.taobao.gpf.app.playabroad.constant.element.PhoneCardTypeEnum
     */
    @JsonProperty("SIZE")
    private String size;

}
