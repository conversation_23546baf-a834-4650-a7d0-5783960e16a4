# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a Maven-based Java project for the GPF-Fliggy system, which is a商品发布框架 (product publishing framework) used by Fliggy (Alibaba's travel platform). The system uses a component-based architecture where each business feature is encapsulated as an independent component object.

## Project Structure

The project follows a Maven multi-module structure:

```
gpf-fliggy
├── decompile (反编译项目中引入的sdk源码的目录)
├── gpf.fliggy.web (Web 层)
├── gpf.fliggy.service (服务层)
├── gpf.fliggy.biz (业务逻辑层)
├── gpf.fliggy.domain (领域层) - 核心组件定义
├── gpf.fliggy.common (公共层)
├── gpf.fliggy.client (客户端接口)
├── gpf.fliggy.aspect (切面层)
├── gpf.fliggy.sdk (SDK 层)
├── gpf.fliggy.lite (轻量级功能)
└── gpf.fliggy.apps (业务应用层)
    ├── gpf.fliggy.app-line: 线路业务，类目: 境内跟团游(50258004) 境内自由行(50272002)
    ├── gpf.fliggy.app-line-abroad: 线路业务，类目: 出境跟团游(50258005) 出境自由行(50278002)
    ├── gpf.fliggy.app-daytour: 线路业务，类目: 境外一日游/多日游(50276003)
    ├── gpf.fliggy.app-line-customized: 定制游业务，类目: 出境线路定制(125408001) 境内线路定制(125210016)
    ├── gpf.fliggy.app-ticket: 门票业务，类目: 国内票务(124866006) 国外票务(50462016)
    ├── gpf.fliggy.app-visa: 签证业务: 类目: 签证直营(124770001,126516017) 入台证(125534004,125522007)  
    ├── gpf.fliggy.app-cruise: 邮轮业务: 类目: 境内邮轮(124720017) 出境邮轮(124488003)
    ├── gpf.fliggy.app-play: 玩乐业务: 类目: 境内酒景套餐(201301317) 境内玩乐套餐(125094026) 露营(201887407)
    ├── gpf.fliggy.app-play-abroad: 境外玩乐业务: 类目: 境外玩乐套餐(125310001)
    ├── gpf.fliggy.app-chartercar: 包车(127220006)
    ├── gpf.fliggy.app-dive: 潜水(50025886)
    ├── gpf.fliggy.app-phonecard: 电话卡&wifi业务 类目: 旅行设备/相机/翻译机租赁(127420008) 境外随身WIFI租赁(124864009) 境外电话卡/手机卡(125352005)
    ├── gpf.fliggy.app-recharge: 充值业务 类目: 境外流量包/充值(125456001) 境外电话卡/wifi流量充值(125696019)
    ├── gpf.fliggy.app-traffic-abroad: 境外交通业务 类目:  境外火车票(50026091)
    ├── gpf.fliggy.app-general: 通用业务 类目:  旅拍(50025880) 境内玩乐单项(201173810)
    └── ... 其他非度假业务
```

## Core Architecture Patterns

### Component Architecture
- Each business feature is encapsulated as an independent component object (CompDO)
- Behavior is defined through JSON configuration files rather than hard-coding
- Components support lifecycle management (init, render, submit, validate)
- Parent-child component relationships enable complex business logic modularization

### Four-Layer Data Model
```
JSON/Schema Layer (Frontend interaction)
       ↕ jsonStrategy
CompDO Layer (Component object layer)
       ↕ storeStrategy
StoreDO Layer (Storage object layer)
       ↕ storeType
Travelitem Layer (Product model layer)
```

### Strategy Pattern Usage
- **initStrategy**: Component initialization logic
- **jsonStrategy**: JSON data conversion (ICompParseJsonStrategy)
- **storeStrategy**: Storage data conversion (IStoreStrategy)
- **schemaStrategy**: Schema chain conversion (ICompParseSchemaStrategy)

## Configuration Management

### Core Configuration Files (in each app module's src/main/resources/)
1. **compconfig files**: Component template configurations
2. **compext files**: Component extension point configurations
3. **publishconfig files**: Publishing process configurations
4. **compadapter files**: Component adapter configurations (includes schemaStrategy definitions)

### Domain-level Base Configurations (in gpf.fliggy.domain/src/main/resources/)
- identity-all-config.json: Business identity definitions
- fliggy-comptemplate-config.json: Base component templates
- fliggy-compexttemplate-config.json: Base component extension points
- fliggy-publishunique-config.json: Base publishing processes
- fliggy-adaptertemplate-config.json: Base component adapters

## Common Development Tasks

### Schema Chain Implementation Process
1. Identify business identity and component list
2. Analyze component configurations and existing schemaStrategies
3. Create component analysis files (requirements)
4. Create implementation files (technical design)
5. Implement schemaStrategy classes
6. Configure schemaStrategy in compadapter files
7. Test and validate implementation

### Component Development Path
1. Define component configuration (compconfig)
2. Implement strategy interfaces (initStrategy, jsonStrategy, storeStrategy)
3. Configure extension points (compext)
4. Implement Schema strategy (schemaStrategy)
5. Configure adapter (compadapter)

## Key SDK Classes and Packages

### Component Related
- `com.alibaba.gpf.sdk.compdo.AbstractCompDO`: Base component class
- `com.alibaba.gpf.base.top.domain.extpoint.compparser.CommonFieldSchemaStrategy`: Base schema strategy
- `com.alibaba.gpf.sdk.config.AdapterCompConfig`: Component adapter configuration

### Schema Related
- `com.taobao.top.schema.field.*`: Field types (InputField, SingleCheckField, etc.)
- `com.taobao.top.schema.value.*`: Value types
- `com.taobao.top.schema.option.Option`: Option definitions

### Utilities
- `com.taobao.gpf.domain.utils.schema.SchemaFieldCreateUtil`: Schema field creation utilities

## Decoding and Reference Materials

The `decompile/` directory contains decompiled SDK source code which should be referenced when:
1. Looking for SDK class paths and method signatures
2. Understanding framework behavior
3. Implementing strategies that extend SDK classes

Always check the `decompile/` directory first when importing SDK classes to ensure correct package paths.

## Python Helper Scripts

Two Python scripts are available for component analysis:
1. `./get_component_short_config.py`: Get component configurations
2. `./query_schema_strategy.py`: Query component schema strategies

Use `python3 script_name.py` to see usage instructions.