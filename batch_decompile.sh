#!/bin/bash

# CFR 反编译工具批量反编译脚本
# 用法: ./batch_decompile.sh <jar文件路径> [CFR工具路径]
# 示例: ./batch_decompile.sh /path/to/your.jar
#       ./batch_decompile.sh /path/to/your.jar /path/to/cfr.jar

set -e  # 遇到错误立即退出

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "CFR 反编译工具批量反编译脚本"
    echo ""
    echo "用法:"
    echo "  $0 <jar文件路径> [CFR工具路径]"
    echo ""
    echo "参数:"
    echo "  jar文件路径    要反编译的jar文件的完整路径"
    echo "  CFR工具路径    CFR反编译工具jar文件路径（可选，默认会尝试下载）"
    echo ""
    echo "示例:"
    echo "  $0 /path/to/your-1.0.0.jar"
    echo "  $0 /path/to/your-1.0.0.jar /path/to/cfr-0.152.jar"
    echo ""
    echo "输出目录结构:"
    echo "  decompile/jar包名称(带版本号)/反编译之后的类全路径/"
    echo "  示例: decompile/example-1.0.0/com/example/MyClass.java"
}

# 检查参数
if [ $# -lt 1 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

JAR_PATH="$1"
CFR_JAR_PATH="$2"

# 检查jar文件是否存在
if [ ! -f "$JAR_PATH" ]; then
    log_error "jar文件不存在: $JAR_PATH"
    exit 1
fi

# 获取jar包的基础文件名（包含版本号）
JAR_BASENAME=$(basename "$JAR_PATH" .jar)
log_info "准备反编译jar包: $JAR_BASENAME"

# 设置输出目录 - 按照用户要求的规则: decompile/jar包名(带版本号)/反编译之后的类全路径
OUTPUT_DIR="decompile/$JAR_BASENAME"
log_info "输出目录规则: decompile/jar包名(带版本号)/反编译之后的类全路径"
log_info "实际输出目录: $OUTPUT_DIR"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# CFR工具处理
CFR_DEFAULT_PATH="./cfr-0.152.jar"

if [ -z "$CFR_JAR_PATH" ]; then
    if [ ! -f "$CFR_DEFAULT_PATH" ]; then
        log_info "CFR工具不存在，正在下载..."
        if command -v curl >/dev/null 2>&1; then
            curl -L -o "$CFR_DEFAULT_PATH" "https://github.com/leibnitz27/cfr/releases/download/0.152/cfr-0.152.jar"
        elif command -v wget >/dev/null 2>&1; then
            wget -O "$CFR_DEFAULT_PATH" "https://github.com/leibnitz27/cfr/releases/download/0.152/cfr-0.152.jar"
        else
            log_error "未找到curl或wget工具，无法下载CFR。请手动下载CFR工具并指定路径。"
            log_error "下载地址: https://github.com/leibnitz27/cfr/releases/download/0.152/cfr-0.152.jar"
            exit 1
        fi
        
        if [ $? -eq 0 ] && [ -f "$CFR_DEFAULT_PATH" ]; then
            log_success "CFR工具下载完成"
        else
            log_error "CFR工具下载失败"
            exit 1
        fi
    fi
    CFR_JAR_PATH="$CFR_DEFAULT_PATH"
else
    if [ ! -f "$CFR_JAR_PATH" ]; then
        log_error "指定的CFR工具文件不存在: $CFR_JAR_PATH"
        exit 1
    fi
fi

log_info "使用CFR工具: $CFR_JAR_PATH"

# 检查Java环境
if ! command -v java >/dev/null 2>&1; then
    log_error "未找到Java环境，请确保Java已安装并在PATH中"
    exit 1
fi

# 创建临时目录用于解压jar包
TEMP_DIR=$(mktemp -d)
log_info "使用临时目录: $TEMP_DIR"

# 清理函数
cleanup() {
    log_info "清理临时目录..."
    rm -rf "$TEMP_DIR"
}

# 设置清理陷阱
trap cleanup EXIT

# 解压jar包到临时目录
log_info "解压jar包..."
cd "$TEMP_DIR"
if ! jar xf "$JAR_PATH" 2>/dev/null; then
    # 如果jar命令不可用，尝试使用unzip
    if command -v unzip >/dev/null 2>&1; then
        unzip -q "$JAR_PATH"
    else
        log_error "无法解压jar包，请确保jar或unzip命令可用"
        exit 1
    fi
fi

# 返回原目录
cd - >/dev/null

# 统计.class文件数量
CLASS_COUNT=$(find "$TEMP_DIR" -name "*.class" | wc -l | tr -d ' ')
log_info "发现 $CLASS_COUNT 个class文件"

if [ "$CLASS_COUNT" -eq 0 ]; then
    log_warn "jar包中未找到class文件"
    exit 0
fi

# 反编译所有class文件
log_info "开始反编译..."
log_info "CFR工具会自动按照包结构创建目录，确保类按全路径存放"

# 使用CFR反编译整个jar包
log_info "使用CFR反编译jar包..."
if java -jar "$CFR_JAR_PATH" "$JAR_PATH" --outputdir "$OUTPUT_DIR" --caseinsensitivefs true --silent true 2>/dev/null; then
    log_success "CFR反编译完成"
else
    log_error "CFR反编译失败，尝试逐个反编译class文件..."
    
    # 逐个处理class文件
    PROCESSED=0
    FAILED=0
    
    find "$TEMP_DIR" -name "*.class" | while read class_file; do
        # 获取相对路径
        rel_path=${class_file#$TEMP_DIR/}
        
        # 创建对应的输出目录（保持类的全路径结构）
        target_dir="$OUTPUT_DIR/$(dirname "$rel_path")"
        mkdir -p "$target_dir"
        
        # 使用CFR反编译单个文件
        java_file="${target_dir}/$(basename "${rel_path%.class}").java"
        
        if java -jar "$CFR_JAR_PATH" "$class_file" --outputdir "$target_dir" --silent true 2>/dev/null; then
            PROCESSED=$((PROCESSED + 1))
        else
            FAILED=$((FAILED + 1))
            log_warn "反编译失败: $rel_path"
        fi
        
        # 每100个文件显示一次进度
        if [ $((PROCESSED % 100)) -eq 0 ]; then
            log_info "已处理: $PROCESSED 个文件"
        fi
    done
    
    log_info "反编译完成，成功: $PROCESSED 个文件，失败: $FAILED 个文件"
fi

# 检查输出结果
JAVA_COUNT=$(find "$OUTPUT_DIR" -name "*.java" | wc -l | tr -d ' ')
log_success "反编译完成！"
log_success "生成了 $JAVA_COUNT 个Java源文件"
log_success "输出目录: $OUTPUT_DIR"

# 显示目录结构示例（展示类的全路径结构）
log_info "目录结构示例（显示类的全路径）:"
find "$OUTPUT_DIR" -name "*.java" | head -5 | while read file; do
    echo "  $file"
done

if [ "$JAVA_COUNT" -gt 5 ]; then
    echo "  ..."
fi

# 显示输出目录规则说明
log_info "输出目录结构符合规则: decompile/jar包名(带版本号)/反编译之后的类全路径"
log_info "反编译任务完成！"