#!/usr/bin/env node

// CFR反编译工具的命令行包装器
import { decompile } from '@run-slicer/cfr';
import * as fs from 'fs/promises';
import * as path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import * as os from 'os';

const execPromise = promisify(exec);

async function decompileJar(jarFilePath, outputDir, className = null) {
  console.log(`开始反编译: ${jarFilePath}`);
  
  // 创建临时目录
  const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'cfr-'));
  
  try {
    // 解压JAR包
    await execPromise(`cd "${tempDir}" && jar xf "${jarFilePath}"`);
    
    // 获取所有class文件
    const { stdout } = await execPromise(`jar tf "${jarFilePath}" | grep ".class$"`);
    const classFiles = stdout.trim().split('\n').filter(f => f.trim());
    
    console.log(`发现 ${classFiles.length} 个class文件`);
    
    // 创建输出目录
    await fs.mkdir(outputDir, { recursive: true });
    
    let processed = 0;
    
    for (const classFile of classFiles) {
      if (className && !classFile.includes(className.replace(/\./g, '/'))) {
        continue;
      }
      
      try {
        const classPath = path.join(tempDir, classFile);
        const classData = await fs.readFile(classPath);
        const internalName = classFile.replace('.class', '');
        
        const decompiled = await decompile(internalName, {
          source: async (name) => {
            if (name === internalName) {
              return classData;
            }
            // 尝试读取其他依赖类
            const depPath = path.join(tempDir, name + '.class');
            try {
              return await fs.readFile(depPath);
            } catch {
              return name.startsWith('java/lang/') ? Buffer.from([]) : null;
            }
          },
          options: {
            hidelangimports: 'true',
            showversion: 'false',
          },
        });
        
        // 写入反编译结果
        const outputFile = path.join(outputDir, internalName + '.java');
        await fs.mkdir(path.dirname(outputFile), { recursive: true });
        await fs.writeFile(outputFile, decompiled);
        
        processed++;
        if (processed % 50 === 0) {
          console.log(`已处理: ${processed} 个文件`);
        }
      } catch (error) {
        console.warn(`跳过文件 ${classFile}: ${error.message}`);
      }
    }
    
    console.log(`反编译完成！处理了 ${processed} 个文件，输出目录: ${outputDir}`);
    
  } finally {
    // 清理临时目录
    await fs.rm(tempDir, { recursive: true, force: true });
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
if (args.length < 2) {
  console.log('用法: node cfr_wrapper.js <jar文件路径> <输出目录> [类名过滤]');
  console.log('示例: node cfr_wrapper.js app.jar ./decompiled');
  console.log('示例: node cfr_wrapper.js app.jar ./decompiled com.example.MyClass');
  process.exit(1);
}

const [jarPath, outputDir, className] = args;

decompileJar(jarPath, outputDir, className)
  .catch(error => {
    console.error('反编译失败:', error.message);
    process.exit(1);
  });