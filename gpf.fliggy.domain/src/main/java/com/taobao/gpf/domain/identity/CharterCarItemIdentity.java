package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.exception.GpfException;
import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.alibaba.gpf.shared.util.BeanUtil;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggyGraySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;
import com.taobao.gpf.domain.utils.GraySwitchUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by maoxinming on 2019/5/10.
 */
@Slf4j
public class CharterCarItemIdentity extends BaseAppBizIdentity {

    @Override
    public boolean isFitByCustom(IdentityParam identityParam) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)identityParam.getWro()).getCategory() ;
        if(null == stdCategoryDO){
            LogUtil.sysErrorLog("CharterCarItemIdentity.isFitByCustom","stdCategoryDO is null");
            return false;
        }
        TravelSellConfig travelSellConfig = BeanUtil.getBean("travelSellConfig",TravelSellConfig.class);
        if(null == travelSellConfig){
            LogUtil.sysErrorLog("TripCarItemIdentity.isFitByCustom","get bean(travelSellConfig) is null");
            throw new GpfException(FliggyErrorEnum.SYS_ERROR.getErrorCode());
        }
        long sellerId = identityParam.getUserId();
        boolean onFor = GraySwitchUtils.isOnFor(FliggyGraySwitchConfig.charterItemDetailGray, sellerId);
        if (onFor) {
            return travelSellConfig.isCharterCarCat(stdCategoryDO.getCategoryId());
        }
        return false;
    }
}
