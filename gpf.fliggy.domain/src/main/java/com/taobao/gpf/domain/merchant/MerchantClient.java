package com.taobao.gpf.domain.merchant;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.mcms.api.model.Objects;

import com.fliggy.fmp.mms.client.common.BaseResult;
import com.fliggy.fmp.mms.client.mc.contract.api.MerContUserClientService;
import com.fliggy.fmp.mms.client.mc.contract.model.MerBizAgtDTO;
import com.fliggy.fmp.mms.client.mc.contract.model.MerBizAgtQueryReq;
import com.fliggy.fmp.mms.client.mc.merchant.api.BizIdentityMcClientService;
import com.fliggy.fmp.mms.client.mc.merchant.model.BizIdentityInfoDTO;
import com.fliggy.fmp.mms.client.mc.merchant.model.QueryBizIdentityInfoReq;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MerchantClient {

    @Autowired
    private MerContUserClientService merContUserClientService;

    @Autowired
    private BizIdentityMcClientService bizIdentityMcClientService;

    /**
     * 通过sellerId查询供应商ID
     */
    @AteyeInvoker(description = "通过sellerId查询供应商ID", paraDesc = "sellerId")
    public List<Long> getSupplierIdBySellerId(Long sellerId) {
        QueryBizIdentityInfoReq infoReq = new QueryBizIdentityInfoReq();
        infoReq.setBizIdentityCode(sellerId);
        infoReq.setBizSceneCode(4L);
        BaseResult<List<BizIdentityInfoDTO>> baseResult = bizIdentityMcClientService.queryAssociatedBizIdentityInfo(
            infoReq);
        if (baseResult.isSuccess() && baseResult.getModule() != null) {
            return baseResult.getModule().stream().map(BizIdentityInfoDTO::getLoginAccountId).map(Long::valueOf)
                .collect(Collectors.toList());
        } else {
            return null;
        }
    }

    /**
     * 通过供应商ID查询签署的合同信息
     */
    @AteyeInvoker(description = "通过供应商ID查询签署的合同信息", paraDesc = "bizIdentityCode&bizAgtId")
    public List<MerBizAgtDTO> getContractInfoBySupplierId(Long bizIdentityCode, Long bizAgtId) {
        MerBizAgtQueryReq merBizAgtQueryReq = new MerBizAgtQueryReq();
        merBizAgtQueryReq.setBizIdentityCode(bizIdentityCode);
        merBizAgtQueryReq.setBizAgtId(bizAgtId);
        merBizAgtQueryReq.setRequestDate(new Date());
        BaseResult<List<MerBizAgtDTO>> listBaseResult = merContUserClientService.queryMerchantBizAgreements(
            merBizAgtQueryReq);
        if (listBaseResult.isSuccess() && listBaseResult.getModule() != null) {
            return listBaseResult.getModule();
        } else {
            return null;
        }
    }

    /**
     * seller 是否签署了货仓相关协议
     * BIZ_VALID(3, "已签署,已生效"),
     */
    public Boolean checkSellerSignWarehouseAgreement(Long sellerId) {
        // 代扣协议平台商家签署。直接用sellerid 查询
        if (Objects.nonNull(FliggySwitchConfig.bizAgreement4DeductAgmt)) {
            List<MerBizAgtDTO> contractInfoBySupplierId = getContractInfoBySupplierId(sellerId,
                FliggySwitchConfig.bizAgreement4DeductAgmt);
            if (CollectionUtils.isEmpty(contractInfoBySupplierId)) {
                return false;
            }
            if (!contractInfoBySupplierId.get(0).getStatus().equals(3)) {
                return false;
            }
        }

        // 是否签署了货仓协议
        List<Long> bizAccountIds = getSupplierIdBySellerId(sellerId);
        if (CollectionUtils.isEmpty(bizAccountIds)) {
            return false;
        }

        for (Long bizAccountId : bizAccountIds) {
            List<MerBizAgtDTO> contractInfoBySupplierId = getContractInfoBySupplierId(bizAccountId,
                FliggySwitchConfig.bizAgreement4Warehouse);
            if (CollectionUtils.isEmpty(contractInfoBySupplierId)) {
                continue;
            }
            if (contractInfoBySupplierId.get(0).getStatus().equals(3)) {
                return true;
            }
        }
        return false;
    }

}
