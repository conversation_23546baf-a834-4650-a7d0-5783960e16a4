package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;
import com.taobao.gpf.domain.utils.FliggyParamUtil;

import javax.annotation.Resource;

/**
 * 境外玩乐业务身份验证
 */
public class PlayAbroadItemIdentity extends BaseAppBizIdentity {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject) param.getWro()).getCategory();
        // 境外玩乐回迁gpf，当前只支持api，后续需要支持web请将isApiDirect条件去掉
        return travelSellConfig.getJingwaiWanletaocanCatId() == stdCategoryDO.getCategoryId()
                && FliggyParamUtil.isApiDirect(((FliggyRequiredObject) param.getWro()).getRequest());
    }
}