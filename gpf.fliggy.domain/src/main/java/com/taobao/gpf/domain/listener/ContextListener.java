package com.taobao.gpf.domain.listener;

import com.taobao.eagleeye.EagleEye;
import com.taobao.gpf.domain.repository.FliggyCategoryCacheRepo;
import com.taobao.travel.client.common.log.Log;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * <AUTHOR>
 */
@Component
public class ContextListener implements ApplicationListener<ContextRefreshedEvent> {

	private static final Log log = Log.getLog(ContextListener.class);

	/**
	 * 每天晚上定时获取类目缓存和重建Lucene索引cron使用的线程池
	 */
	public static final ScheduledExecutorService scheduled1 = new ScheduledThreadPoolExecutor(1,new BasicThreadFactory.Builder().namingPattern("gpf-fliggy-schedule-pool-%d").daemon(true).build());

	private volatile int yestodayTaskCount = 0;

	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		log.warn("onApplicationEvent");
		ApplicationContext springContext = event.getApplicationContext();
		final FliggyCategoryCacheRepo fliggyCategoryCacheRepo = (FliggyCategoryCacheRepo)springContext.getBean("fliggyCategoryCacheRepo");
		// 通过HSF接口从traveltic系统获取类目等缓存数据
		if(event.getApplicationContext().getParent() == null){
			EagleEye.startTrace(null, "gpf-reload-cache");
			log.warn(EagleEye.getTraceId() + " 通过HSF接口开始从traveltic获取类目缓存信息开始！");
			boolean result = fliggyCategoryCacheRepo.reloadCache();
			if(!result){
				log.error(EagleEye.getTraceId() + " 通过HSF接口从traveltic系统获取类目等缓存数据失败！");
				System.exit(-1);
				return;
			}
			log.warn(EagleEye.getTraceId() + "通过HSF接口从traveltic系统获取类目等缓存数据成功！");
			EagleEye.endTrace();
		}
		// 获取类目缓存和重建Lucene索引 every day 1 times(5:00-6:00之间)
		scheduled1.scheduleWithFixedDelay(new Runnable() {
			@Override
			public void run() {
				try {
					Calendar now = Calendar.getInstance();
					int nowHour = now.get(Calendar.HOUR_OF_DAY);
					if (nowHour == 4) {
						yestodayTaskCount = 0;
					}
					if (yestodayTaskCount == 0 && nowHour == 6) {
						boolean result = fliggyCategoryCacheRepo.reloadCache();
						yestodayTaskCount += 1;
					}
				} catch (Throwable e) {
					log.error("获取类目缓存和重建Lucene索引时出现异常", e);
				}
			}
		}, 60 * 60, 10 * 60, TimeUnit.SECONDS);  // 延迟1小时后执行，每10分钟执行一次
	}

}
