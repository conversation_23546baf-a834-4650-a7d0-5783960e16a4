package com.taobao.gpf.domain.component.itemtag;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.travelitems.constants.SellType;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import org.apache.commons.lang3.StringUtils;

public class ItemTagContentStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<String>> {

    @Override
    public String getName() {
        return "itemTagContentStoreStrategy";
    }

    @Override
    public void transferStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (StringUtils.isNotEmpty(compDO.getValue())) {
            storeDO.getAlitripFeatures().put("itemTagContent", compDO.getValue());
        }
    }

    @Override
    public void parseStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        String content = storeDO.getAlitripFeatures().get("itemTagContent");
        if(StringUtils.isNotBlank(content)){
            compDO.setValue(content);
        }
    }
}
