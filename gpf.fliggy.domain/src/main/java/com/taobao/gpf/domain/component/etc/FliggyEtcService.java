package com.taobao.gpf.domain.component.etc;

import com.alibaba.common.lang.StringUtil;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.utils.CatFeaturesUtils;
import com.taobao.item.util.CollectionUtils;
import com.taobao.traveltic.bo.user.UserManager;
import com.taobao.uic.common.domain.BaseUserDO;
import com.taobao.uic.common.domain.ResultDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FliggyEtcService {

    private static final String SP = ";";
    private static final String SP_KV = ":";
    private static final String R_SP = "#3A";
    private static final String R_SP_KV = "#3B";

    @Resource
    private UserManager userManager;

    /**
     *判断类目是否在电子凭证类目黑名单里
     */
    public boolean isInCategoryBlacklist(@Nonnull StdCategoryDO category, @Nonnull BaseUserDO user) {
        if(null == category || null == user ) {
            return false;
        }
        ResultDO<String> rs = userManager.getUicDataServiceClient().getData(user.getUserId(), CatFeaturesUtils.ETICKET_CATEGORY_BLACKLIST);
        if(!rs.isSuccess()){
            return false;
        }
        String result = rs.getModule();
        if (StringUtil.isNotEmpty(result)) {
            String[] catIds = result.split(";");
            List<String> idList = Arrays.asList(catIds);
            if (idList.size() == 1 && idList.contains("ALL")) {
                return true;
            }
            if (idList.contains(String.valueOf(category.getCategoryId()))) {
                return true;
            }
        }
        return false;
    }

    /**
     *获取类目的电子凭证支持状态信息
     *
     */
    public int getEtcCategorySupportStatus(@Nonnull StdCategoryDO category, @Nonnull BaseUserDO user) {
        if(null == category || null == user) {
            return 0;
        }
        ResultDO<String> rs = userManager.getUicDataServiceClient().getData(user.getUserId(), CatFeaturesUtils.TMALL_ECERTIFICATE_USERDATA);
        if(!rs.isSuccess()){
            return 0;
        }
        String result = rs.getModule();

        String catId = String.valueOf(category.getCategoryId());
        if (StringUtil.isNotEmpty(result)) {
            Map<String, String> userDataMaps = str2map(result);
            if (CollectionUtils.isNotEmpty(userDataMaps)) {
                if (userDataMaps.containsKey(catId)) {
                    String value = userDataMaps.get(catId);
                    if (StringUtil.isNumeric(value)) {
                        int intValue = Integer.parseInt(value);
                        return intValue;
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 将string格式的userdata转为map格式
     * @param userdata
     * @return
     */
    private static Map<String, String> str2map(String userdata) {
        Map<String, String> result = new HashMap<String, String>(20);
        if (StringUtils.isBlank(userdata)) {
            return result;
        }
        String[] kvs = userdata.split(SP);
        for (String string : kvs) {
            String[] kv = string.split(SP_KV);
            if (2 == kv.length) {
                result.put(decode(kv[0]), decode(kv[1]));
            }
        }
        return result;
    }

    /**
     * 将字符串中的 #3A转化为:,#3B转化为;
     * @param val
     * @return
     */
    private static String decode(String val) {
        return val.replace(R_SP, SP).replace(R_SP_KV, SP_KV);
    }
}
