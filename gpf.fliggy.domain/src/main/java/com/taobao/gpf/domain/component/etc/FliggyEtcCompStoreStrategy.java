package com.taobao.gpf.domain.component.etc;

import javax.annotation.Resource;

import com.alibaba.gpf.business.domain.component.etc.EtcControlDO;
import com.alibaba.gpf.business.domain.repository.NewEtcManagerRepo;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.CompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;

import com.google.common.base.Objects;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.macenter.domain.EtcAuctionExtendsDO;
import com.taobao.macenter.domain.dto.EtcFormDTO;
import com.taobao.macenter.domain.dto.EtcPublishDTO;
import com.taobao.macenter.eticket.domain.result.ResultDO;
import com.taobao.macenter.eticket.service.EtcSendOperateService;
import com.taobao.travel.client.domain.dataobject.TravelItemLocalityLifeDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/04/29/下午2:20
 */
public class FliggyEtcCompStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<String>>{

    private static final Logger LOGGER = LoggerFactory.getLogger(FliggyEtcCompStoreStrategy.class);

    @Resource
    private NewEtcManagerRepo newEtcManagerRepo;

    @Resource
    private EtcSendOperateService etcSendOperateService;

    @Resource
    TravelSellConfig travelSellConfig ;

    @Override
    public String getName() {
        return "fliggyEtcCompStoreStrategy";
    }

    @Override
    public void parseStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        super.parseStore(compDO, storeDO, param);
        if (storeDO.getTravelItemExtendInfo() != null) {
            TravelItemLocalityLifeDO eCertDO = storeDO.getTravelItemExtendInfo().getEletronicVoucher();
            EtcControlDO controlDO = compDO.getControlDO();
            if (eCertDO != null && Objects.equal(eCertDO.getExtractWay(), 2)) {
                controlDO.setSelected(true);
            } else if (storeDO.getETicketInfo() != null &&
                (Integer.valueOf(2).equals(storeDO.getETicketInfo().getExtractWay())
                    || Integer.valueOf(3).equals(storeDO.getETicketInfo().getExtractWay()))) {
                controlDO.setSelected(true);
            }
        }

    }

    @Override
    public void transferStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        super.transferStore(compDO, storeDO, param);
        //EtcAuctionExtendsDO etcAuctionExtendsDO = newEtcManagerRepo.generateAuctionExtend(buildEtcPublishDTO(compDO.getControlDO(),param));
        //storeDO.setEtcAuctionExtendsDO(etcAuctionExtendsDO);

        EtcAuctionExtendsDO etcAuctionExtendsDO = fliggyBuildEtcPublishDTO(compDO.getControlDO(), param);
        if (etcAuctionExtendsDO == null) {
            return;
        }
        storeDO.setEtcAuctionExtendsDO(etcAuctionExtendsDO);
    }

    public EtcAuctionExtendsDO fliggyBuildEtcPublishDTO(EtcControlDO controlDO, CompExtParam param) {
        EtcAuctionExtendsDO etcAuctionExtendsDO = null;
        try {
            ResultDO<EtcAuctionExtendsDO> resultDO = etcSendOperateService.generateAuctionExtendInfo(buildEtcPublishDTO(controlDO, param));
            if (resultDO.isSuccess()) {
                etcAuctionExtendsDO = resultDO.getModule();
            }
        } catch (Exception e) {
            LOGGER.error("fliggyBuildEtcPublishDTO error,msg:"+e.getMessage(), e);
        }
        return etcAuctionExtendsDO;
    }

    private EtcPublishDTO buildEtcPublishDTO(EtcControlDO controlDO,CompExtParam param) {
        if(null == controlDO || null == controlDO.getEtcDTO()){
            return new EtcPublishDTO();
        }

        BasicCompDO<Long> primaryCompDo = (BasicCompDO)param.getDependCompDO("id");
        Long primaryId = null;
        if  (primaryCompDo != null) {
             primaryId = primaryCompDo.getValue();
        }

        EtcFormDTO etcFormDTO = controlDO.getEtcDTO();
        EtcPublishDTO etcPublishDTO = new EtcPublishDTO();
        etcPublishDTO.setItemId(etcFormDTO.getItemId() == null ? primaryId : etcFormDTO.getItemId());
        etcPublishDTO.setCategoryId((long)FliggyParamUtil.getCategoryDO(param).getCategoryId());
        etcPublishDTO.setSellerId(etcFormDTO.getSellerId() == null ? param.getReq().getUserId() : etcFormDTO.getSellerId());
        etcPublishDTO.setFromApp("dujiaSell");
        etcPublishDTO.setEtcFormDTO(etcFormDTO);
        etcFormDTO.setSellerId(etcPublishDTO.getSellerId());
        etcFormDTO.setItemId(etcPublishDTO.getItemId());
        return etcPublishDTO;
    }


    @Override
    public void cleanStore(CompConfig compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        super.cleanStore(compDO, storeDO, param);
    }


}
