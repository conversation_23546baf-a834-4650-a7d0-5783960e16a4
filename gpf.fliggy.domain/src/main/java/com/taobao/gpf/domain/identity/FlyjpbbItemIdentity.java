package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;


/**
 * <AUTHOR>
 * 机票宝贝业务身份识别
 */
public class FlyjpbbItemIdentity extends BaseAppBizIdentity {

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)param.getWro()).getCategory();
        return FliggySwitchConfig.flyjpbbCatIds.contains(Long.parseLong(String.valueOf(stdCategoryDO.getCategoryId())));
    }
}
