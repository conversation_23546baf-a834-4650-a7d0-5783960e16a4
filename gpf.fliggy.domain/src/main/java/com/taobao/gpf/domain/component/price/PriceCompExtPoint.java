package com.taobao.gpf.domain.component.price;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import com.alibaba.gpf.common.exception.ErrorCode;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.ProcessForSubmit;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.TableColumnCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.dataobject.MoneyValueDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;

import com.taobao.gpf.domain.component.calendarprice.CalendarPriceDO;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.top.tbml.css.value.basic.Int;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @description:普通库存的价格组件的扩展点
 * <AUTHOR>
 */
@Slf4j
public class PriceCompExtPoint implements IInjectExtension {

    private static MoneyValueDO ZERO_VALUE = new MoneyValueDO(0);

    @Check(key = "price-fliggy-submit-datacheck")
    public CheckResult check(CompExtParam param,TableColumnCompDO<MoneyValueDO> compDO) {
        CheckResult checkResult = new CheckResult();
        BasicCompDO<Integer> saleWayCompDo = (BasicCompDO)param.getDependCompDO("saleWay");
        //如果不是普通库存
        if(null == saleWayCompDo || null == saleWayCompDo.getValue() || 1 != saleWayCompDo.getValue()){
            return checkResult;
        }
        final List<MoneyValueDO> value = compDO.getValue();
        if(CollectionUtils.isEmpty(value)){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_SKU_LACK_PRICE.getErrorCode());
            return checkResult;
        }
        if(value.contains(null)){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_SKU_LACK_PRICE.getErrorCode());
            return checkResult;
        }
        Optional<MoneyValueDO> errorValue = value.stream().filter(x -> (null==x || x.compareTo(ZERO_VALUE)<0)).findFirst();
        if(errorValue.isPresent()){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_SKU_LACK_PRICE.getErrorCode());
            return checkResult;
        }
        return checkResult;
    }




}
