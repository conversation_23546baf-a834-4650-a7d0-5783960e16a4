package com.taobao.gpf.domain.component.etc;

import com.alibaba.gpf.business.domain.component.etc.EtcControlDO;
import com.alibaba.gpf.business.domain.component.extractWay.DeliverWayEnum;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.constant.delivery.DeliveryWayConstants;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.macenter.domain.dto.EtcFormDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;

@Slf4j
public class FliggyEtcCheckRule implements IInjectExtension {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Check(key = "fliggyEtcCheckRule")
    public CheckResult check(BasicCompDO<String> compDO, CompExtParam param) {
        CheckResult result = new CheckResult();
        try {
            if (compDO.getControlDO() == null || ((EtcControlDO)compDO.getControlDO()).getWay() != DeliverWayEnum.ETC) {
                return result;
            }

            if (compDO.getControlDO() != null && ((EtcControlDO)compDO.getControlDO()).getEtcDTO() != null) {
                EtcFormDTO etcFormDTO = ((EtcControlDO)compDO.getControlDO()).getEtcDTO();

                if (travelSellConfig.isScenicTicketCatId(FliggyParamUtil.getCategoryId(param))){
                    //门票不校验电子凭证有效期
                    //do nothing
                }else if ((etcFormDTO.getExpiryDateType() != 1 && etcFormDTO.getExpiryDateType() != 2 && etcFormDTO.getExpiryDateType() != 3) || (StringUtils.isEmpty(etcFormDTO.getExpiryTime()) || ",".equals(etcFormDTO.getExpiryTime()))) {
                    result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode(DeliveryWayConstants.DELIVERY_WAY,
                            "请选择【电子凭证】有效期类型，例如有效期区间段或者购买成功后有效日期！"));
                    return result;
                }

                // 旅行购抵用券校验
                FliggyRequiredObject wro =(FliggyRequiredObject) param.getWro();
                if (********* == wro.getCategory().getCategoryId() || ********* == wro.getCategory().getCategoryId()) {
                    if (etcFormDTO.getExpiryDateType() != 3 || NumberUtils.toInt(etcFormDTO.getExpiryTime()) > 25) {
                    result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode(DeliveryWayConstants.DELIVERY_WAY,
                            "抵用券有效期模式仅支持第三种“购买成功后N天内有效”，N≤25。"));
                    return result;
                    }
                }

                if (etcFormDTO.getSelectPackageId() == null) {
                    result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode(DeliveryWayConstants.DELIVERY_WAY,
                            "请选择核销门店库！"));
                    return result;
                }
            } else {
                result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode(DeliveryWayConstants.DELIVERY_WAY,
                        "请填写电子交易凭证相关信息！"));
                return result;
            }
        } catch (Exception e) {
            log.error("FliggyEtcCheckRule check error,msg:"+e.getMessage(), e);
        }
        return result;
    }


}