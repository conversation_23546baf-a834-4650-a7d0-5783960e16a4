package com.taobao.gpf.domain.component.images;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.constant.image.ImageNoteConstant;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: shushen
 * @date: 2020/6/5
 * @time: 下午7:49
 * @version:1.0
 */
public class ImageNoteSettingStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<String>> {


    @Override
    public String getName() {
        return "imageNoteSettingStoreStrategy";
    }

    @Override
    public void parseStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if(null == storeDO.getImageNoteStoreDO()){
            return;
        }
        if(storeDO.getImageNoteStoreDO().isSettingImageNote()) {
            compDO.setValue(ImageNoteConstant.IS_SETTING_IMAGE_NOTE);
        }
    }

}