package com.taobao.gpf.domain.component.prebook;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.constant.DatePickerTypeEnum;
import com.alibaba.gpf.sdk.model.pagemodel.DatePickerPageModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.shared.componet.standarddate.StandardDateCompParseJsonStrategy;
import com.alitrip.travel.common.util.QuaDateUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.utils.common.GpfDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: chetao
 * @date: 2020/6/19
 * @time: 9:05 PM
 * @version:1.0
 */
@Slf4j
public class BookableStartTimeJsonStrategy extends StandardDateCompParseJsonStrategy {

    @Override
    public String getName() {
        return "bookableStartTimeJsonStrategy";
    }

    @Override
    public JsonNode renderJson(BasicCompDO<Date> compDO, CompExtParam param) {
        JsonNode jsonNode =  super.renderJson(compDO, param);
        if(jsonNode == null){
            jsonNode = JacksonUtil.createObjectNode();
        }
        if(!(compDO.getPagemodel() instanceof DatePickerPageModel)){
            return jsonNode;
        }
        DatePickerPageModel pageModel = (DatePickerPageModel)compDO.getPagemodel();
        if(StringUtils.isNotBlank(pageModel.getPickerType())) {
            DatePickerTypeEnum datePickerTypeEnum = DatePickerTypeEnum.of(pageModel.getPickerType());
            if(null!=datePickerTypeEnum) {
                if (compDO.getValue() != null) {
                    ((ObjectNode)jsonNode).put("value", GpfDateUtils.toFormatString(compDO.getValue(), datePickerTypeEnum.getFormat()));
                } else {
                    Date preBookStartDate = getPreBookStartDate();
                    if (preBookStartDate != null) {
                        ((ObjectNode)jsonNode).put("value", GpfDateUtils.toFormatString(preBookStartDate, datePickerTypeEnum.getFormat()));
                    }
                }
            }
        }
        return jsonNode;
    }

    /**
     * 获取配置化预约开始时间
     * @return
     */
    public Date getPreBookStartDate() {
        List<String> dateStr = FliggySwitchConfig.prebookStartDate;
        Date startDate = null;
        for(String startStr : dateStr){
            try {
                startDate = QuaDateUtils.stringToDate(startStr, DatePickerTypeEnum.MINUTE.getFormat());
            } catch (Exception e){
                log.error("method:getPreBookStartDate,e.getMessage():"+e.getMessage(),e);
                continue;
            }
        }
        return startDate;
    }
}
