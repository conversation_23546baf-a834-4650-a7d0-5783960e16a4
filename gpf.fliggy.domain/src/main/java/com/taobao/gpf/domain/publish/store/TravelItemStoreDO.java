package com.taobao.gpf.domain.publish.store;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.gpf.business.domain.store.storedo.ItemWholeStoreDO;

import com.alitrip.travel.common.enums.CommonStatus;
import com.alitrip.travel.common.store.StorageType;
import com.alitrip.travel.common.store.StoreContent;
import com.alitrip.travel.common.store.StoreKey;
import com.alitrip.travel.travelitems.constants.AssociateItemType;
import com.alitrip.travel.travelitems.constants.ItemStatus;
import com.alitrip.travel.travelitems.constants.PromotedStatus;
import com.alitrip.travel.travelitems.constants.RefundRuleType;
import com.alitrip.travel.travelitems.constants.SecondKillType;
import com.alitrip.travel.travelitems.constants.SellType;
import com.alitrip.travel.travelitems.constants.TransportType;
import com.alitrip.travel.travelitems.model.*;
import com.alitrip.travel.travelitems.model.base.ContactsInfo;
import com.alitrip.travel.travelitems.model.base.ImageTextSellPoint;
import com.alitrip.travel.travelitems.model.base.Location;
import com.alitrip.travel.travelitems.model.detail.FliggySpecialDetail;
import com.alitrip.travel.travelitems.model.glocalprice.PriceGlobalItemContent;
import com.alitrip.travel.travelitems.model.grade.TravelItemGrade;
import com.alitrip.travel.travelitems.model.hela.BookingNotes;
import com.alitrip.travel.travelitems.model.hela.FeeExclude;
import com.alitrip.travel.travelitems.model.hela.FeeInclude;
import com.alitrip.travel.travelitems.model.hela.LinePlay;
import com.alitrip.travel.travelitems.model.hela.SMProductHighlights;
import com.alitrip.travel.travelitems.model.hotel.HotelInfo;
import com.alitrip.travel.travelitems.model.scenery.SceneryTicket;
import com.alitrip.travel.travelitems.model.service.ServiceItem;
import com.alitrip.travel.travelitems.model.service.ServiceProcess;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.alitrip.travel.travelitems.model.supply.SupplierInfo;
import com.alitrip.travel.travelitems.model.systemvendordirect.SystemVendorDirectConfiguration;
import com.alitrip.travel.travelitems.model.tripstruct.BaseSelfFeeInclude;
import com.alitrip.travel.travelitems.model.tripstruct.OneTrip;
import com.alitrip.travel.travelitems.model.tripticket.AirticketInfo;
import com.alitrip.travel.travelitems.model.vedio.VideoStruct;
import com.alitrip.travel.travelitems.supply.constants.ItemSupplyTypeConstants;
import com.alitrip.travel.tripcenter.scenic.model.ScenicProductDO;
import com.fliggy.fic.dto.eticket.ETicketInfo;
import com.fliggy.travel.product.client.dto.ProductDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.gpf.domain.component.playmethod.model.PlayMethodStoreModel;
import com.taobao.gpf.domain.component.refundrule.SelfHolidayRefundValueModel;
import com.taobao.gpf.domain.model.collage.CollageComboSupportStoreDO;
import com.taobao.gpf.domain.model.collage.CollageItemSupportStoreDO;
import com.taobao.gpf.domain.model.coupon.CouponComboSupportStoreDo;
import com.taobao.gpf.domain.model.cruise.CruiseLinerScheduleStoreDO;
import com.taobao.gpf.domain.model.hotel.HotelComboSupportStoreDO;
import com.taobao.gpf.domain.model.hotel.HotelItemSupportStoreDO;
import com.taobao.gpf.domain.model.phonecard.storedo.AcquiredBySelfFeeDescStoreDO;
import com.taobao.gpf.domain.model.phonecard.storedo.AcquiredBySelfRefundRuleStoreDO;
import com.taobao.gpf.domain.model.phonecard.storedo.BookingDetailDescStoreDO;
import com.taobao.gpf.domain.model.phonecard.storedo.PostFeeDescStoreDO;
import com.taobao.gpf.domain.model.phonecard.storedo.PostRefundRuleStoreDO;
import com.taobao.gpf.domain.model.phonecard.storedo.ProductDetailDescStoreDO;
import com.taobao.gpf.domain.model.storedo.TravelItemComboStoreDO;
import com.taobao.gpf.domain.publish.store.convertor.ext.Store2ItemForComboListConvertor;
import com.taobao.gpf.domain.publish.store.packageinfo.ItemPackagePublishDO;
import com.taobao.gpf.domain.publish.store.rule.MultiDimensionRefundRuleEnum;
import com.taobao.item.domain.ItemDO;
import com.taobao.macenter.domain.EtcAuctionExtendsDO;
import com.taobao.travel.client.domain.dataobject.TravelItemComboDO;
import com.taobao.travel.client.domain.dataobject.TravelItemPropValueDO;
import com.taobao.travel.tripcenter.basic.standard.dimension.domain.StandardDimension;
import com.taobao.travel.tripcenter.domain.ScenicDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ecs.wml.I;

/**
 * <AUTHOR>
 * @date 2019/03/18
 * 类简介: 飞猪生态商品的存储模型,具体字段是参考TravelItem的内容。放弃使用WholeStoreDO中的Map分组形式。
 */
@Data
public class TravelItemStoreDO extends ItemWholeStoreDO {
    /**
     * 日历库存值
     */
    public static Integer CALENDAR_INVENTORY_TYPE_VALUE = 5;

    /**
     * 普通库存值
     */
    public static Integer COMMON_INVENTORY_TYPE_VALUE = 1;

    public TravelItemStoreDO() {

    }


    /**
     * 度假专用features
     */
    private Map<String, String> alitripFeatures = Maps.newHashMap();

    /**
     * 延后销售天数（单位：天），发布或修改商品时设置推迟多久才开始销售 一口价商品默认为7；拍卖商品调用方传入7或者14
     */
    private Integer duration;


    /**
     * 预订须知，存放在TFS/OSS中
     */
    private String bookTips;


    /**
     * 预定须知类型，null或者1表示普通纯文本，2表示旅行机票(暂时存储在alifeature中)
     */
    private Integer bookTipsType;


    /**
     * 商品子标题
     */
    private List<String> subTitles = new ArrayList<String>();



    /**
     * 推荐状态(0普通,1橱窗推荐[卖家操作],2推荐[小二操作])
     */
    private PromotedStatus promotedStatus;



    /**
     * 是否自定义退改规则：0-标准，1-自定义退改规则
     */
    private RefundRuleType refundRuleType;


    /**
     * 退改规则，文本，DB存放json字符串，在商品详情for detail查询取出的时候转换成具体的文案
     */
    private String refundTerms;

    /**
     * 自定义节假日退改规则，文本，DB存放json字符串，在商品详情for detail查询取出的时候转换成具体的文案
     */
    private String selfDefineHolidayRefundTerms;

    /**
     * 自定义节假日定义
     */
    private List<SelfHolidayRefundValueModel> selfHolidayRefundValueModels;

    /**
     * 多维度退款政策，境内跟团游稀缺性发布形式下 非旺季时段不启用稀缺性场景使用
     */
    private Map<MultiDimensionRefundRuleEnum, String> multiDimensionRefundRuleMap = Maps.newHashMap();

    /**
     * 秒杀商品类型
     */
    private SecondKillType secondKillType;

    /**
     * 库存方式，
     *
     * 1、旧版日历库存；2、日历库存非共享库存；3 区间库存  4 区间票   5 规则价格
     */
    private Integer inventoryType;

    /**
     * 无线端商品详情，查询接口请使用mobileDescrPath从tfs取
     */
    private String mobileDescr;

    /**
     * 飞猪特殊描述详情
     * key是行业标识  例如酒店请使用hotel  线路使用line
     * value是List<FliggySpecialDetail>
     */
    private Map<String,List<FliggySpecialDetail>> specialDetailInfo;

    /**
     * 主图视频扩展结构
     */
    private VideoStruct videoStruct;

    /**
     * 是否参与会员打折
     */
    private Boolean hasDiscount;

    /**
     * 商品对应时区，例如:America/Los_Angeles 这种格式
     */
    private String timeZoneId;

    private TravelItemExt travelItemExtendInfo = new TravelItemExt();

    /**
     * 自取模板描述
     */
    private String pickUpMyselfDesc;

    /**
     * IC商品状态(0，1正常;-1:用户删除;-2:用户下架;-3 小二下架;-4 小二删除;-5 从未上架;-9 CC)
     */
    private ItemStatus itemStatus;

    /**
     * 本地库商品状态(1正常;-1:冻结)
     */
    private CommonStatus status;

    /**
     * 商品上架时间
     */
    private Date startTime;

    /**
     * 销售类型-预售/普通
     */
    private SellType sellType;


    /**
     * 邮轮售卖方式: 单船票/船票套餐
     */
    private Integer sellStyle;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 预约截止时间
     */
    private Date bcEndTime;

    /**
     * 预约开始时间
     */
    private Date bcStartTime;

    /**
     * 新版二次预约可约次数
     */
    private Integer bookableTimes;

    /**
     * 商品标签
     */
    private String itemContent;

    /**
     * 费用不含
     */
    private String feeNotInclude;

    /** 最晚收客时间 :小时 */
    private Integer orderDeadlineHours;

    /** 最晚收客时间 :分钟 */
    private Integer orderDeadlineMinutes;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 预约套餐信息
     */
    private List<TravelItemComboDO> comboList;
    /**
     * 商品发布套餐模型
     */
    private ItemPackagePublishDO itemPackagePublishDO;
    /**
     * gpf-fliggy系统下的套餐模型
     */
    private List<TravelItemComboStoreDO> travelItemComboStoreDOList;

    /**
     * 去程交通类型
     */
    private String goTrafficType;

    /**
     * 回程交通类型
     */
    private String backTrafficType;

    private int ConfirmationUploadType;

    private int ConfirmationUploadDay;

    /**
     * 1:有,0:无
     */
    Integer hasInvoice = 0;


    /**
     * 是否即时确认
     */
    Boolean immediateConfirm;

    /**
     * 确认类型 - 1:即时确认/ 2：二次确认
     */
    private int confirmType;
    /**
     * 确认时间 - 2：6：24
     */
    private int confirmTime;

    //旺铺编辑字段
    private String customizationData;
    private String moduleList;
    private String nativeDetailJson;
    private ExtraUpdateInfo extraUpdateInfo;

    /**
     * 激活方式
     */
    private String activateWay;

    /**
     * 多地可用
     */
    private String multipleSitesAvailable;


    private String visaMarkRangeDesc;

    /**
     * 一口价
     */
    private Long price;

    /**
     * 商品库存：目前在库存中心维护，IC会在itemcenter-client merge这份数据；
     */
    Integer quantity;


    private ContactsInfo contactsInfo;

    /**
     * 邮轮房型
     */
    private String housingName;

    /**
     * 邮轮容纳人数
     */
    private String capacity;

    /**
     * 邮轮下单是否需要匹配人数
     */
    private boolean limitPeopleNum;

    /**
     * 行程预览
     */
    private List<TravelPreviewStoreDO> travelPreviewStoreDOList = Lists.newArrayList();

    /**
     * 邮轮航线
     */
    private List<CruiseLinerScheduleStoreDO> cruiseLinerScheduleStoreDOList;

    private Integer mainElementType;

    /* 超市产品亮点 */
    private List<SMProductHighlights> smProductHighlights;

    /* 超市线路玩法 */
    private LinePlay smLinePlay;
    /* 超市费用包含 */
    private FeeInclude smFeeInclude;
    /* 超市费用不含 */
    private FeeExclude smFeeExclude;
    /* 超市预订须知 */
    private BookingNotes smBookingNotes;

    /** 超市线路玩法  */
    private int smLinePlayId;
    /**
     * 玩法id
     */
    private List<Long> playIdList = new ArrayList<>();


    //**************以下字段为暂时不用字段**************

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品在IC中的ID
     */
    private Long itemId;

    /**
     * 本地生成的商品id
     */
    private Long localItemId;


    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品所属类目
     */
    private Long categoryId;

    /**
     * 卖家Id
     */
    private Long sellerId;

    /**
     * 商家昵称
     */
    private String sellerNick ;

//    /**
//     * 宝贝开始销售的时间，2021.09.24汐云注释，请使用startTime
//     */
//    private Date beginTime;

    /**
     * 宝贝下架时间
     * 结束日期：只对拍卖商品有效，一口价已经不使用；目前商品没有所谓的到期自动下架的概念，只有三种方式商品会下架：一是库存为0，二是卖家主动下架
     * ，三是后台系统下架（业务系统或是处罚系统）
     */
    private Date endTime;

    /**
     * 线路类型0-长线游,1-周边游
     */
    private Byte routeType;

    /**
     * 旅游方式
     */
    private String tripWay;

    /**
     * 出发地，可以多个，形式：locationId:locationName;locationId:locationName，已废弃
     */
    @Deprecated
    private Map<Long, String> fromLocations = new HashMap<Long, String>();

    /**
     * 旅游目的地，可以多个，形式：locationId:locationName;locationId:locationName，已废弃
     */
    @Deprecated
    private Map<Long, String> toLocations = new HashMap<Long, String>();

    /** 出发地 */
    private List<Location> from = Lists.newArrayList();

    /** 目的地 */
    private List<Location> to = Lists.newArrayList();

    /**
     * 行程天数
     */
    private Integer tripDays;

    /**
     * 住宿晚数
     */
    private Integer accomNights;

    /**
     * 出发日期时间段上限
     */
    private Date departStartDate;

    /**
     * 出发日期时间段下限
     */
    private Date departEndDate;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * 来源
     */
    private String source ;

    /**
     * 宝贝描述存储方式：1-TFS，2-OSS
     */
    private StorageType storageType;

    /**
     * 费用不含，文本，内容保存在TFS或者OSS中
     */
    @StoreContent(keyField = "feeExcludePath")
    private String feeExclude;

    /**
     * 存放在TFS/OSS的路径
     */
    @StoreKey(contentField = "feeExclude")
    private String feeExcludePath;

    /**
     * 费用包含，文本，存放在TFS/OSS
     */
    @StoreContent(keyField = "feeIncludePath")
    private String feeInclude;

    /**
     * 费用包含，存放TFS或者OSS上的路径
     */
    @StoreKey(contentField = "feeInclude")
    private String feeIncludePath;

    /**
     * 预订须知，存放在TFS/OSS中的路径
     */
    @StoreKey(contentField = "bookTips")
    private String bookTipsPath;

    /**
     * 商品详情描述的TFS路径
     */
    private String descrPath;

    /**
     * 无线宝贝描述在TFS或OSS上的存储路径
     */
    private String mobileDescrPath;

    /**
     * 商品详情，查询接口默认不返回，请使用descrPath从tfs取
     */
    private String descr;


    /**
     * 退改规则的细节
     */
    private List<TravelItemRefundRule> refundRuleList = new ArrayList<TravelItemRefundRule>();

    /**
     * 商品图片，第一张是主图，请严格按照顺序来设置图片
     */
    private List<TravelImage> images = new ArrayList<TravelImage>();

    /**
     * 商家类型：true表示航旅卖家；false表示非航旅卖家。B/C商家类型通过flag标来区分
     */
    private Boolean tripSeller = true;

    /**
     * 需提前预约的天数
     */
    private Integer bookDaysAhead;


    /**
     * 商品特征
     */
    private Map<String, String> features = new HashMap<String, String>();

    /**
     * 商品扩展属性
     */
    private Map<String, String> extendProperties = new HashMap<String, String>();

    /**
     * 度假扩展标记位（按位取与），见文档：http://docs.alibaba-inc.com:8090/pages/viewpage.action?
     * pageId= *********
     */
    private Long flag;

    /**
     * 标记位（按位取与），对应于Ic的Option
     */
    private Long itemOption;

    /**
     * 标签：多个中间用,逗号分隔
     */
    private List<Long> tags = new ArrayList<Long>();


    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 商家自定义标签Id，多个中间用逗号分隔
     */
    private Map<String, String> sellerTags = new HashMap<String, String>();

    /**
     * 属性别名，例如：套餐别名
     */
    private Map<Long, String> propertiesAlias = new HashMap<Long, String>();

    /**
     * 商品关联元素，费用包含中的大交通、酒店、景点等
     */
    Map<AssociateItemType, List<TravelAssociateItem>> associateItems = new HashMap<>();

    /**
     * storeDo类型的关联元素
     */
    Map<AssociateItemType,List<TravelAssociateItemStoreDO>> associateItemStoreDOs = Maps.newHashMap();


    /**
     * 行程信息
     */
    List<TravelItinerary> itineraries = new ArrayList<TravelItinerary>();


    /**
     * 商品属性,属性存储结构问题，有很严重的数据丢失问题，以后不再使用;请使用propertyList
     */
    @Deprecated
    Map<String, String> properties = new HashMap<String, String>();

    /**
     * 商品属性
     */
    List<ItemPVPair> propertyList = new ArrayList<ItemPVPair>();

    /**
     * 商品所在地 - 省份
     */
    String province;

    /**
     * 商品所在地 - 城市
     */
    String city;



    /**
     * 预扣库存：拍下减商品为防止超卖使用该字段作为预扣库存字段；虚拟库存、只有打了防超卖标记的商品才有意义、默认是0
     */
    Integer withHoldingQuantity;

    /**
     * 返点比率
     */
    Integer itemPoint;


    /**
     * 去程交通类型，主要是跟团游使用，已废弃， @see onwardTrafficType
     */
    @Deprecated
    TransportType onwardTransportType;

    /**
     * 回程交通类型，主要是跟团游使用，已废弃，@see returnTrafficType
     */
    @Deprecated
    TransportType returnTransportType;

    /**
     * 去程交通，字段值转换为pvid后持久化
     */
    private String onwardTrafficType;

    /**
     * 回程交通，字段值转换为pvid后持久化
     */
    private String returnTrafficType;


    /**
     * 度假专用tags
     */
    private List<Long> alitripTags = new ArrayList<Long>();

    /**
     * 判断本商品是否是新版的商品，true为新版，false为旧版 当所有类目都切换到新的版本，这个标志会deprecated掉
     */
    private boolean newItem = true;

    /**
     * 商品详情在TFS/OSS上的路径，该详情包括费用不含、费用包含、预定须知、退改规则、日程和用户自定义详情等
     */
    @StoreKey(contentField = "detail")
    private String detailPath;

    /**
     * 所有商品详情，该详情包括费用不含、费用包含、预定须知、退改规则、日程和用户自定义详情等
     */
    @StoreContent(keyField = "detailPath")
    private String detail;

    /**
     * 预约开始时间，预约商品专用
     */
    private Date bookStartTime;

    /*** 拍卖字段 */

    /**
     * 起拍价：只适用于拍卖商品
     */
    private Long minimumBid = 0L;

    /**
     * 价格增幅：只对拍卖商品有效，增价幅度如果为0表示系统代理加幅
     */
    private Long incrementNum = 0L;

    /** 拍卖字段end */

    /**
     * 邮轮对象
     */
    private TravelCruises travelCruises;

    /**
     * 因maybach强依赖，仅供traveldetailskip使用。 其他应用禁止使用
     */
    private ItemDO itemDO;

    // 1:国内门票套餐,2:境外门票套餐
    private String ticketAreaType;

    /**
     * 门票产品审核状态
     */
    private Integer ticketAuditPeriod;

    /* 签证结构化fields begin */
    private List<TravelGeneralSku> travelGeneralSkus = new ArrayList<TravelGeneralSku>();

    /* 签证服务对象 */
    private TravelVisa travelVisa;

    /**
     * 商品资源列表
     */
    private List<TravelResource> travelResources = new ArrayList<TravelResource>();

    /**
     * 服务商品
     */
    private List<ServiceItem> serviceItems = new ArrayList<ServiceItem>();

    /**
     * 服务履约
     */
    private List<ServiceProcess> serviceProcess = new ArrayList<ServiceProcess>();

    /** 最少购买数，默认0，如果是0 表示没有限制 */
    private Integer minLimit = 0;

    /** 最大购买数，默认0，如果是0 表示没有限制 */
    private Integer maxLimit = 0;

    /** 游玩最长时间，默认是0，表示不是一个范围。例如7--15天自由行,tripDays = 7,tripMaxDays=15 */
    private Integer tripMaxDays = 0;

    /* 商品的sku和基本信息是否分开发布,true:是 */
    private boolean onlyPublishItemBase=false;

    /**
     * 主题标签
     */
    private TravelTheme travelTheme;



    /**
     * 购买时间限制
     */
    private Date buyTimeLimit;

    /**
     * 购买最大天数  -1不限制
     */
    private Integer buyMaxDay;

    /**
     * 购买后多少天有效
     */
    private Integer effDay;

    /**
     * 如果是门票类型，该字段不能为空。主要一些门票的特有的相关信息
     */
    @Deprecated
    private SceneryTicket sceneryTicket;

    /**
     * 门票的特有的相关信息.
     * 替代sceneryTicket,当sceneryTicket有对象的时候,sceneryTickets里面为单个对象.
     */
    private List<SceneryTicket> sceneryTickets;

    /**
     * 费用包含、预定须知、退改签、改期描述的路径
     */
    private String allSkuDetail;

    /**
     * 获取所有的产品信息
     */
    private String allProductDetail;

    /**
     * 购买限制时间类型:1:无限制,2:有限制
     */
    private Integer buyTimeType;

    /**
     * 购买时间限制,拍下多少小时可以入园
     */
    private Integer buyTimeHour;
    /**
     * 购买时间限制,拍下后多少时间可以入园
     */
    private Date buyEnterTime;

    /**
     * 购买至某日期有效,和eff_day二者选一,格式"yyyy-MM-dd"
     */
    private Date effDate;

    /**
     * 票的有效期类型,1:至某天有效,2:至某个日期有效,3:套餐指定日期开放预约，4:套餐自购买日起可以预约
     */
    private Integer effType;

    /**
     * true:需要邮箱,false:不需要邮箱
     */
    private boolean hasEmail;

    /**
     * wifi for api
     */
    private Wifi wifi;

    /**
     * 电话卡模型
     */
    private PhoneCard phoneCard;

    /**
     * 线路品质分级
     */
    private TravelItemGrade travelItemGrade;

    /**
     * 组织者名称
     */
    private String organization;

    /**
     * 组织者介绍
     */
    private String orgIntroduce;

    /**
     * 组织者电话
     */
    private String orgTel;

    /**
     * 组织者旺旺
     */
    private String orgWangwang;

    /**
     * 活动地点
     */
    private String activityPlace;

    /**
     * 活动时间
     */
    private String activityTime;

    /**
     * true:新版电子凭证,false:旧版
     */
    private Boolean newEtc;

    /**
     * 租车特殊服务
     */
    private Set<String> carSpecialServiceSet;

    /**
     * 租车车辆大类名称
     */
    private String carCheliangBigCategoryName;

    /**
     * 用车类型
     */
    private String useCarTypeName;

    /**
     * 报价申明,to get name that use Enum Class BaochePriceInclude
     */
    private String baochePriceInclude;

    /**
     * 报价申明选项,to get name that use Enum Class BaochePriceIncludeOptions
     */
    private List<String> baochePriceIncludeOptions;

    /**
     * 运费模板信息
     */
    private PostageTemplate postageTemplate;

    /**
     * 新版行程结构化模型
     */
    private OneTrip oneTrip;

    /**
     * 新版行程结构化模型-多行程
     */
    private List<OneTrip> multipleTrip;

    /**
     * true:使用新版行程结构
     */
    private boolean useNewTripStruct;

    /**
     * 出行人模板id
     */
    private Long travelPersonTempletaId;
    /**
     * 出行人填写方式，1:下单过程中填写 2:付款后补填
     */
    private Integer travelPersonFlowType;

    /**
     * 旅行机票航班信息
     */
    private AirticketInfo airticketInfo;
    /**
     * 退改规则结构化原始json,仅供sell使用,内容存储在TFS中
     */
    private String bookTipsJson;


    @StoreContent(keyField = "oneTripJsonTfsPath")
    private String oneTripJson;

    @StoreKey(contentField = "oneTripJson")
    private String oneTripJsonTfsPath;



    /**
     * 联系人
     */
    private ContactsInfo contacts;

    /**
     * 酒店商品信息
     */
    private HotelInfo hotelInfo;
    /**
     * 有价券1.0时期保存的优惠券信息，当时认为一个商品对应一个优惠券信息
     * 2.0之后为了一对多需求追加couponInfos字段，该字段为兼容老接口没有删除
     */
    private CouponInfo couponInfo = new CouponInfo() ;

    /**
     * 有价券2.0需求，表示一个商品对应一个券包
     */
    private CouponPackage couponPackage;
    /**
     * 权益卡信息
     */
    private CouponCard couponCard;
    private Integer originHotelBenefitCardStatus;

    /**
     * 盲盒信息
     */
    private BlindBoxInfo blindBoxInfo;

    /**
     * 商品类型，0表示有价券，1表示平台盲盒，2表示抽奖
     */
    private Integer couponItemType;

    // 供应商属性
    private SupplierInfo supplierInfo;
    // true:供应商商品
    private boolean isSuppierItem;

    //-----------境外旅行超市-赫拉:境外当地玩乐类目商品模型start---------------//
    /* true:同意商品加入旅行商品 */
    private boolean agreeJoinTripSuperMarket;

    /* 统一在tfs存储费用包含、不含、预订须知、产品亮点内容 */
    @StoreContent(keyField = "helaJsonPath")
    private String helaJson;
    @StoreKey(contentField = "helaJson")
    private String helaJsonPath;
    //-----------境外旅行超市-赫拉:境外当地玩乐类目商品模型end---------------//
    /* true:v3组件化商品 */
    private boolean v3Item;

    /** 商品快照描述(仅用于发布端值传递) */
    private String snapshotDesc;

    /**
     * 不可预约说明
     */
    private String noOrderDesc;

    /**
     * 处理时长说明
     */
    private String endorseTimeDesc;

    /**
     * 是否指定入园时间 1需要指定入园时间 2无需指定入园时间
     */
    private Integer specifyTime ;

    /**
     * 是否日历库存 1日历库存 2非日历库存
     */
    private Integer calendarStock;

    /**
     * 电子凭证对象
     */
    private EtcAuctionExtendsDO etcAuctionExtendsDO;

    /**
     * 电子凭证查询对象，发布编辑时不感知，只用于查询
     */
    private ETicketInfo eTicketInfo;

    /**
     * 套餐对象
     */
    private List<Combo> combos;

    private Long spuId;

    /**
     * 供销商品类型（1、供销产品 ；2、代销商品； 3、经销商品）
     * @see ItemSupplyTypeConstants
     */
    private Integer supplyType;


    /**
     * 供销产品信息
     */
    private SupplyProductInfoStoreDO supplyProductInfoStoreDO;


    /**
     * 门店信息
     */
    private List<HotelStoreDO> hotelStoreDOList;

    /**
     * 新版的门店信息
     * 后续List<HotelStoreDO> 考虑是否也迁移到这里，需要酒店支持下
     */
    private StoreDO storeDO = new StoreDO();


    private List<ScenicDO> scenicList ;

    private ScenicProductDO scenicProductDO ;

    private StandardDimension standardDimension;

    private List<ProductDTO> erpProducts;

    private Integer codeMode ;

    private boolean isEdit ;

    private String currency ;

    /*
     * 客服电话
     */
	/*@Param(required = true, message = "com.alitrip.travel.travelsell.parameter.notnull.customTel")*/
    private String customTel ;

    /**
     * 核销服务商
     */
	/*@Param(required = true, message = "com.alitrip.travel.travelsell.parameter.notnull.vertifyMerchant")*/
    private String vertifyMerchant  ;

    private GlobalTourData globalTourData = new GlobalTourData();

    private BizItemExtensionDO bizItemExtensionDO;

    /**
     * 费用补充说明
     */
    private String feeExtraDesc;

    /**
     * 自费项目
     */
    private List<BaseSelfFeeInclude> selfFeeIncludes;

    /**
     * 机票宝贝业务 Flyjpbb 专属
     */
    private FlyjpbbPackage flyjpbbPackage;

    /**
     * 玩法属性信息
     */
    private List<PlayMethodStoreModel> playProp;

    /**
     * 飞猪行业自定义扩展
     *
     * @see com.fliggy.fic.dto.FItemDTO#
     */
    private Map<String, String> fliggyBizFeature = new HashMap<>();

    /**
     * 新的多亮点组件
     */
    private List<ImageTextSellPoint> imageTextSellPoints;

    /**
     * 系统商直连配置
     */
    private SystemVendorDirectConfiguration systemVendorDirectConfiguration = new SystemVendorDirectConfiguration();

    /**
     * 使用指南
     */
    private UsingTip usingTip;

    public FlyjpbbPackage getFlyjpbbPackage() {
        if(null == flyjpbbPackage){
            flyjpbbPackage = new FlyjpbbPackage();
        }
        return flyjpbbPackage;
    }

    /**
     * 增加kv对到度假专用features
     * @param key
     * @param value
     */
    public void addAlitripFeature(String key,String value){
        if(StringUtils.isBlank(key)){
            return;
        }
        if(null == getAlitripFeatures()){
            alitripFeatures = new HashMap<>();
        }
        getAlitripFeatures().put(key,value);
    }


    /**
     * 追加多个kv对到度假专用features
     * @param appendFeatures
     */
    public void addAlitripFeatures(Map<String, String> appendFeatures) {
        if(MapUtils.isEmpty(appendFeatures)){
            return;
        }
        if(null == getAlitripFeatures()){
            setAlitripFeatures(new HashMap<>());
        }
        getAlitripFeatures().putAll(appendFeatures);
    }

    /**
     * 删除kv对到度假专用features
     * @param key
     */
    public void delAlitripFeature(String key){
        if(StringUtils.isBlank(key)){
            return;
        }
        if(null == getAlitripFeatures()){
            alitripFeatures = new HashMap<>();
        }
        getAlitripFeatures().remove(key);
    }

    /**
     * 获取度假专用features中指定key的值
     * @param key
     */
    public String getAlitripFeature(String key){
        if(StringUtils.isBlank(key)){
            return null;
        }
        if(null == getAlitripFeatures()){
            return null;
        }
        return getAlitripFeatures().get(key);
    }

    /**
     * 添加子标题
     * @param newSubTitle
     */
    public void addSubTitle(String newSubTitle){
        if(StringUtils.isBlank(newSubTitle)){
            return;
        }
        if(null == this.getSubTitles()){
            this.subTitles = Lists.newArrayList();
        }
        CollectionUtils.addIgnoreNull(this.getSubTitles(), newSubTitle);
    }

    /**
     * 获取子标题
     * @param index
     * @return
     */
    public String getSubTitle(Integer index) {
        if(null == index){
            return null;
        }
        if(index+1<=(null==this.getSubTitles()?0:this.getSubTitles().size())){
            return this.getSubTitles().get(index);
        }
        return null;
    }


    public boolean isCommonInventoryType(){
        return null!=inventoryType && inventoryType.equals(TravelItemStoreDO.COMMON_INVENTORY_TYPE_VALUE);
    }

    public boolean isCalendarInventoryType(){
        return null!=inventoryType && inventoryType.equals(TravelItemStoreDO.CALENDAR_INVENTORY_TYPE_VALUE);
    }

    /**
     * 判断是否是立即确认
     * @param confirmType
     * @return
     */
    public static Boolean isImmediatelyConfirm(int confirmType){
        if(1 == confirmType){
            return true;
        }else if(2 == confirmType){
            return false;
        }else{
            return null;
        }
    }

    /**
     * 添加storeDo形式的元素
     */
    public void appendTravelAssociateItemStoreDO(AssociateItemType associateItemType,TravelAssociateItemStoreDO associateItemStoreDO){
        if(null == associateItemType || null == associateItemStoreDO){
            return;
        }
        if(!this.getAssociateItemStoreDOs().containsKey(associateItemType)){
            this.getAssociateItemStoreDOs().put(associateItemType,Lists.newArrayList());
        }
        this.getAssociateItemStoreDOs().get(associateItemType).add(associateItemStoreDO);
    }

    /**
     * 添加storeDo形式的元素列表
     */
    public void appendTravelAssociateItemStoreDOs(AssociateItemType associateItemType,List<TravelAssociateItemStoreDO> associateItemStoreDOS){
        if(null == associateItemType || CollectionUtils.isEmpty(associateItemStoreDOS)){
            return ;
        }
        if(!this.getAssociateItemStoreDOs().containsKey(associateItemType)){
            this.getAssociateItemStoreDOs().put(associateItemType,Lists.newArrayList());
        }
        this.getAssociateItemStoreDOs().get(associateItemType).addAll(associateItemStoreDOS);
    }


    public List<TravelAssociateItemStoreDO> getTravelAssociateItemStoreDOs(AssociateItemType associateItemType){
        if(null == associateItemType){
            return null;
        }
        return this.getAssociateItemStoreDOs().get(associateItemType);
    }

    /**
     * 判断是否是立即确认
     * @return
     */
    public boolean isImmediatelyConfirm(){
        return 1 == confirmType;
    }

    public ExtraUpdateInfo getExtraUpdateInfo(){
        if(null == extraUpdateInfo){
            extraUpdateInfo = new ExtraUpdateInfo();
        }
        return extraUpdateInfo;
    }

    /**
     * 是否包含指定tag
     * @param matchTag
     * @return
     */
    public boolean containTag(Long matchTag){
        if(null == matchTag){
            return false;
        }
        return CollectionUtils.isNotEmpty(this.getTags()) && this.getTags().contains(matchTag);
    }

    public void addTag(Long addTag){
        if(null == addTag){
            return;
        }
        if(null == getExtraUpdateInfo().getAddTags()){
            Set<Long> addTags = Sets.newHashSet();
            getExtraUpdateInfo().setAddTags(addTags);
        }
        getExtraUpdateInfo().getAddTags().add(addTag);
    }

    public void removeTag(Long removeTag){
        if(null == removeTag){
            return;
        }
        if(null == getExtraUpdateInfo().getRemoveTags()){
            Set<Long> removeTags = Sets.newHashSet();
            getExtraUpdateInfo().setRemoveTags(removeTags);
        }
        getExtraUpdateInfo().getRemoveTags().add(removeTag);
    }

    public void appendComboList(List<TravelItemComboDO> appendComboList){
        if(CollectionUtils.isEmpty(appendComboList)){
            return;
        }
        if(null == comboList){
            comboList = Lists.newArrayList();
        }
        CollectionUtils.addAll(comboList,appendComboList);
    }

    public void appendCombos(List<Combo> appendComboList){
        if(CollectionUtils.isEmpty(appendComboList)){
            return;
        }
        if(null == combos){
            combos = Lists.newArrayList();
        }
        CollectionUtils.addAll(combos,appendComboList);
    }

    /**
     * 追加属性值
     * @param appendPropValueDO
     */
    public void appendExtInfoPropertyValue(TravelItemPropValueDO appendPropValueDO){
        if(null == appendPropValueDO){
            return;
        }
        this.getTravelItemExtendInfo().getPropertyValues().add(appendPropValueDO);
    }

    public boolean isSeasonTicket(){
        if(null == scenicProductDO){
            return false ;
        }
        return StringUtils.isNotEmpty(scenicProductDO.getEpisodes()) || StringUtils.isNotEmpty(scenicProductDO.getAreas());
    }


    /**
     * 追加属性值列表
     * @param appendPropValueDOS
     */
    public void appendExtInfoPropertyValues(List<TravelItemPropValueDO> appendPropValueDOS){
        if(CollectionUtils.isEmpty(appendPropValueDOS)){
            return;
        }
        this.getTravelItemExtendInfo().getPropertyValues().addAll(appendPropValueDOS);
    }

    public TravelItemPropValueDO findExtInfoPropValueDO(long findPid) {
        if(CollectionUtils.isEmpty(this.getTravelItemExtendInfo().getPropertyValues())){
            return null;
        }
        for(final TravelItemPropValueDO propValueDO : this.getTravelItemExtendInfo().getPropertyValues()){
            if(propValueDO.getPid() == findPid){
                return propValueDO;
            }
        }
        return null;
    }

    public Map<AssociateItemType,List<TravelAssociateItem>> convert2AssociateItems() {
        if(MapUtils.isEmpty(this.getAssociateItemStoreDOs())){
            return null;
        }
        Map<AssociateItemType,List<TravelAssociateItem>> travelAssociateItemMap = Maps.newHashMap();
        for(Map.Entry<AssociateItemType,List<TravelAssociateItemStoreDO>> entry : this.getAssociateItemStoreDOs().entrySet()){
            travelAssociateItemMap.put(entry.getKey(),buildTravelAssociateItems(entry.getValue()));
        }
        return travelAssociateItemMap;
    }

    private List<TravelAssociateItem> buildTravelAssociateItems(List<TravelAssociateItemStoreDO> source) {
        if(CollectionUtils.isEmpty(source)){
            return Lists.newArrayList();
        }
        return source.stream().filter(Objects::nonNull).map(TravelAssociateItemStoreDO::convert).collect(Collectors.toList());
    }

    public Map<AssociateItemType, List<TravelAssociateItemStoreDO>> convert2AssociateItemStoreDOs(Map<AssociateItemType, List<TravelAssociateItem>> associateItems) {
        if(MapUtils.isEmpty(associateItems)){
            return null;
        }
        Map<AssociateItemType, List<TravelAssociateItemStoreDO>> associateItemStoreDOMap = Maps.newHashMap();
        for(Map.Entry<AssociateItemType, List<TravelAssociateItem>> entry : associateItems.entrySet()){
            if(!associateItemStoreDOMap.containsKey(entry.getKey())){
                associateItemStoreDOMap.put(entry.getKey(),Lists.newArrayList());
            }
            associateItemStoreDOMap.get(entry.getKey()).addAll(buildTravelAssociateItemStoreDOs(entry.getValue()));
        }
        return associateItemStoreDOMap;
    }

    private Collection<? extends TravelAssociateItemStoreDO> buildTravelAssociateItemStoreDOs(List<TravelAssociateItem> source) {
        if(CollectionUtils.isEmpty(source)){
            return null;
        }
        List<TravelAssociateItemStoreDO> target = Lists.newArrayList();
        for(TravelAssociateItem sourceData : source){
            CollectionUtils.addIgnoreNull(target,buildTravelAssociateItemStoreDO(sourceData));
        }
        return target;
    }

    private TravelAssociateItemStoreDO buildTravelAssociateItemStoreDO(TravelAssociateItem sourceData) {
        if(null == sourceData){
            return null;
        }
        TravelAssociateItemStoreDO target = TravelAssociateItemStoreDO.generateFrom(sourceData);
        return target;

    }

    /**
     * 获取travelItemComboStoreDOList
     * @return
     */
    public List<TravelItemComboStoreDO> getTravelItemComboStoreDOList() {
        if(null == travelItemComboStoreDOList){
            travelItemComboStoreDOList = Lists.newArrayList();
        }
        return travelItemComboStoreDOList;
    }

    public TravelItemComboStoreDO obtainFirstTravelItemComboStoreDO(){
        if(CollectionUtils.isEmpty(travelItemComboStoreDOList)){
            return null;
        }
        Optional<TravelItemComboStoreDO> match = travelItemComboStoreDOList.stream()
            .filter(x -> (null!=x && null!=x.getVisaPackageStoreDO() && x.getVisaPackageStoreDO().getPackageType() == 1 && x.getVisaPackageStoreDO().getBasePackageSort() ==1))
            .findFirst();
        return match.orElse(null);
    }

    /**
     * 追加TravelItemComboStoreDO
     * @param appendComboStoreDO
     */
    public void appendComboStoreDO(TravelItemComboStoreDO appendComboStoreDO) {
        CollectionUtils.addIgnoreNull(getTravelItemComboStoreDOList(), appendComboStoreDO);
    }

    public void putTravelAssociateItems(AssociateItemType type,List<TravelAssociateItem> appendItems) {
        if(null == type || CollectionUtils.isEmpty(appendItems)){
            return;
        }
        if(null == this.getAssociateItems()){
            this.setAssociateItems(Maps.newHashMap());
        }
        if(!this.getAssociateItems().containsKey(type)){
            this.getAssociateItems().put(type,Lists.newArrayList());
        }
        this.getAssociateItems().get(type).addAll(appendItems);
    }

    public void putTravelAssociateItemStoreDOs(AssociateItemType type,List<TravelAssociateItemStoreDO> appendItemStoreDOs) {
        if(null == type || CollectionUtils.isEmpty(appendItemStoreDOs)){
            return;
        }
        if(null == this.getAssociateItemStoreDOs()){
            this.setAssociateItemStoreDOs(Maps.newHashMap());
        }
        if(!this.getAssociateItemStoreDOs().containsKey(type)){
            this.getAssociateItemStoreDOs().put(type,Lists.newArrayList());
        }
        this.getAssociateItemStoreDOs().get(type).addAll(appendItemStoreDOs);
    }



    /**
     * 追加TravelItemComboStoreDO列表
     * @param appendComboStoreDOs
     */
    public void appendComboStoreDOs(List<TravelItemComboStoreDO> appendComboStoreDOs){
        if(CollectionUtils.isEmpty(appendComboStoreDOs)){
            return;
        }
        CollectionUtils.addAll(getTravelItemComboStoreDOList(), appendComboStoreDOs);
    }

    /**
     * 将TravelItemComboStoreDOs 转化y为
     * @return
     */
    public List<TravelItemComboDO> parserTravelItemComboStoreDO() {
        List<TravelItemComboStoreDO> travelItemComboStoreDOList = this.getTravelItemComboStoreDOList();
        if(CollectionUtils.isEmpty(travelItemComboStoreDOList)){
            return null;
        }
        List<TravelItemComboDO> appendTravelItemComboDOs = Store2ItemForComboListConvertor.transferComboStoreDO2ComboDO(travelItemComboStoreDOList);
        return appendTravelItemComboDOs;
    }

    /**
     * 获取所有的类目属性PV对
     * @return
     */
    public Map<String,String> obtainTravelItemExtPropValues(){
        if(null == this.getTravelItemExtendInfo() || CollectionUtils.isEmpty(this.getTravelItemExtendInfo().getPropertyValues())){
            return Maps.newHashMap();
        }
        Map<String,String> propValues = Maps.newHashMap();
        for(final TravelItemPropValueDO propValueDO : this.getTravelItemExtendInfo().getPropertyValues()){
            propValues.put(propValueDO.getPid()+"",propValueDO.getVid()+"");
        }
        return propValues;
    }

    /**
     * 动态打包模板id
     */
    private Long dynamicPackageTemplateId;

    /**
     * 外币信息(商品级别)
     */
    private PriceGlobalItemContent priceGlobalItemContent;

    /**
     * 头图笔记信息
     */
    private ImageNoteStoreDO imageNoteStoreDO;

    /**
     * 拼团业务SKU supportDO
     */
    private CollageComboSupportStoreDO collageComboSupportStoreDO;

    /**
     * 酒店套餐行业SKU supportDO
     */
    private HotelComboSupportStoreDO hotelComboSupportStoreDO;

    /**
     * 拼团业务item  supportDO
     */
    private CollageItemSupportStoreDO collageItemSupportStoreDO;

    /**
     * 酒店套餐行业item  supportDO
     */
    private HotelItemSupportStoreDO hotelItemSupportStoreDO;
    /**
     * 有价券相关套餐
     */
    private CouponComboSupportStoreDo couponComboSupportStoreDo;

    public CollageItemSupportStoreDO getCollageItemSupportStoreDO() {
        if(null == collageItemSupportStoreDO){
            collageItemSupportStoreDO = new CollageItemSupportStoreDO();
        }
        return collageItemSupportStoreDO;
    }

    public HotelItemSupportStoreDO getHotelItemSupportStoreDO() {
        if(null == hotelItemSupportStoreDO){
            hotelItemSupportStoreDO = new HotelItemSupportStoreDO();
        }
        return hotelItemSupportStoreDO;
    }
    /**
     * 202007 无线电话卡自取费用详情
     */
    private AcquiredBySelfFeeDescStoreDO acquiredBySelfFeeDescStoreDO;

    public AcquiredBySelfFeeDescStoreDO getAcquiredBySelfFeeDescStoreDO() {
        if (acquiredBySelfFeeDescStoreDO == null){
            acquiredBySelfFeeDescStoreDO = new AcquiredBySelfFeeDescStoreDO();
        }
        return acquiredBySelfFeeDescStoreDO;
    }
    /**
     * 202007 无线电话卡邮寄费用描述
     */
    private PostFeeDescStoreDO postFeeDescStoreDO;

    public PostFeeDescStoreDO getPostFeeDescStoreDO() {

        if (postFeeDescStoreDO == null) {
            postFeeDescStoreDO = new PostFeeDescStoreDO();
        }
        return postFeeDescStoreDO;
    }

    /**
     * 202007 无线电话卡预订详情描述
     */
    private BookingDetailDescStoreDO bookingDetailDescStoreDO;


    public BookingDetailDescStoreDO getBookingDetailDescStoreDO(){
        if (bookingDetailDescStoreDO == null) {
            bookingDetailDescStoreDO = new BookingDetailDescStoreDO();
        }
        return bookingDetailDescStoreDO;
    }

    /**
     * 202007 无线电话卡产品详情
     */
    private ProductDetailDescStoreDO productDetailDescStoreDO;

    public ProductDetailDescStoreDO getProductDetailDescStoreDO() {
        if (productDetailDescStoreDO == null) {
            productDetailDescStoreDO = new ProductDetailDescStoreDO();
        }
        return productDetailDescStoreDO;
    }

    /**
     * 202007 无线电话卡自取退改规则详情
     */
    private AcquiredBySelfRefundRuleStoreDO acquiredBySelfRefundRuleStoreDO;

    public AcquiredBySelfRefundRuleStoreDO getAcquiredBySelfRefundRuleStoreDO() {

        if (acquiredBySelfRefundRuleStoreDO == null) {
            acquiredBySelfRefundRuleStoreDO = new AcquiredBySelfRefundRuleStoreDO();
        }
        return acquiredBySelfRefundRuleStoreDO;
    }
    /**
     * 202007 无线电话卡邮寄退改规则详情
     */
    private PostRefundRuleStoreDO postRefundRuleStoreDO;

    public PostRefundRuleStoreDO getPostRefundRuleStoreDO() {
        if (postRefundRuleStoreDO == null) {
            postRefundRuleStoreDO = new PostRefundRuleStoreDO();
        }
        return postRefundRuleStoreDO;
    }

    public List<ComboStoreDO> getComboStoreDO(){
        List<ComboStoreDO> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(combos)){
            return list;
        }
        for (Combo combo : combos){
            list.add(new ComboStoreDO(combo));
        }
        return list;
    }

    @AllArgsConstructor
    @Data
    public static class ComboStoreDO implements com.alibaba.gpf.sdk.storedo.StoreDO {
        private Combo combo;
    }
}
