package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.exception.GpfException;
import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.alibaba.gpf.shared.util.BeanUtil;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/06/06/上午11:58
 */
public abstract class AbstractAppBizIdentity extends BaseAppBizIdentity {

    @Override
    public boolean isFitByCustom(IdentityParam identityParam) {
        FliggyRequiredObject fliggyRequiredObject = ((FliggyRequiredObject)identityParam.getWro());
        if(null == fliggyRequiredObject){
            LogUtil.sysErrorLog("AppBizIdentity.isFitByCustom","fliggyRequiredObject is null");
            return false;
        }
        StdCategoryDO stdCategoryDO = fliggyRequiredObject.getCategory() ;
        if(null == stdCategoryDO){
            LogUtil.sysErrorLog("AppBizIdentity.isFitByCustom","stdCategoryDO is null");
            return false;
        }
        TravelSellConfig travelSellConfig = BeanUtil.getBean("travelSellConfig",TravelSellConfig.class);
        if(null == travelSellConfig){
            LogUtil.sysErrorLog("AppBizIdentity.isFitByCustom","get bean(travelSellConfig) is null");
            throw new GpfException(FliggyErrorEnum.SYS_ERROR.getErrorCode());
        }
        return doJudge(travelSellConfig,stdCategoryDO.getCategoryId());
    }

    protected abstract boolean doJudge(TravelSellConfig travelSellConfig, int categoryId);

}
