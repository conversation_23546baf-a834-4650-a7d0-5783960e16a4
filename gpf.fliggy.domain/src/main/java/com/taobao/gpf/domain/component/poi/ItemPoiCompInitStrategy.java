package com.taobao.gpf.domain.component.poi;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.compPlugin.ICompInitStrategy;

/**
 * Created by maoxinming on 2019/4/19.
 */
public class ItemPoiCompInitStrategy implements ICompInitStrategy<BasicCompDO<String>> {
    @Override
    public BasicCompDO<String> getInitCompDO() {
        BasicCompDO<String> compDO = new BasicCompDO<>();
        compDO.setControlDO(new ItemPoiControlDO());
        return compDO;
    }

    @Override
    public ItemPoiControlDO getInitControlDO() {
        return new ItemPoiControlDO();
    }

    @Override
    public String getName() {
        return "itemPoiCompInitStrategy";
    }
}
