package com.taobao.gpf.domain.component.title;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.component.shelfTime.ShelfTimeControlDO;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2025-03-07-15:28
 */
@Slf4j
public class TitleCompExtPoint implements IInjectExtension {

    @Check(key = "title-fliggy-dataCheck")
    public CheckResult check(CompExtParam param, BasicCompDO<String> compDO, @Category StdCategoryDO stdCategory) {
        CheckResult result = new CheckResult();
        try {
            if(null == compDO.getControlDO()){
                return result;
            }

            // 旅行定制 - 标题校验
            customtripCheck(result, compDO, stdCategory);
            return result;
        } catch (Exception e) {
            log.error("ShelfTimeCompExtPoint addRules error,msg:"+e.getMessage(), e);
        }
        return result;
    }

    private void customtripCheck(CheckResult result, BasicCompDO<String> compDO, StdCategoryDO stdCategory) {
        String title = compDO.getValue();
        // 类目校验
        int categoryId = stdCategory.getCategoryId();

        if (categoryId != 127450001) {
            return;
        }

        // 标题校验
        if (FliggySwitchConfig.customtripBcTitleCheck.stream().anyMatch(title::contains)) {
            return;
        }

        result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("title",
                "补差商品标题必须包含“补差”文案，请更改商品标题后再发布。定制补差类目仅用于商品补差价使用，如果用于其他场景，会进行违规处罚。"));
    }


}
