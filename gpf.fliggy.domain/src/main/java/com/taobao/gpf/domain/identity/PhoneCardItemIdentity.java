package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * Created by maoxinming on 2019/6/12.
 */
public class PhoneCardItemIdentity extends BaseAppBizIdentity {

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)param.getWro()).getCategory() ;
        return FliggySwitchConfig.phoneCardCatIds.contains(stdCategoryDO.getCategoryId());
    }
}
