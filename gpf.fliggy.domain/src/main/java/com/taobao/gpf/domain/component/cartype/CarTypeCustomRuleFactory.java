package com.taobao.gpf.domain.component.cartype;

import com.alibaba.gpf.sdk.model.pagemodel.RuleModel;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/14 00:00
 */
public class CarTypeCustomRuleFactory {

    /**
     * https://yuque.alibaba-inc.com/trip_plat/gc9db4/ns16xa
     * 规则设置：
     *             当carType用车类型为无行程用车（9），组件隐藏
     *             当carType用车类型不为无行程用车（9），组件展示，并且为必填项
     *
     * @return
     */
    public static List<RuleModel> getCarTypeRules() {
        List<RuleModel> customRules = Lists.newArrayList();
        //空座率显隐
        customRules.add(vacancyRateRuleModel("${carType}&&${carType}.length && ${carType}.some(val => val.value !== 9)",true,true));
        customRules.add(vacancyRateRuleModel("${carType} == null || ${carType} ==' '|| !${carType}.length || (${carType}&&${carType}.length && ${carType}.some(val => val.value == 9))", false, false));
        //车型图片显隐
        customRules.add(carTypePictureVisibleRuleMode( "${carType}&&${carType}.length",true));
        customRules.add(carTypePictureVisibleRuleMode( "${carType} == null || ${carType} ==' '|| !${carType}.length",false));
        //车型与图片联动
        customRules.add(carTypeEffectPicture("${carType}&&${carType}.length", "${carType}", true));
        customRules.add(carTypeEffectPicture("${carType} == null || ${carType} ==' '|| !${carType}.length", "[]", false));

        /*
        规则设置：
            当carType用车类型为无行程用车（9），组件隐藏
            当carType用车类型不为无行程用车（9），组件展示，并且为必填项
         */
//        customRules.add(vacancyRateRuleModel("${carType.value} != 9", true, true));
//        customRules.add(vacancyRateRuleModel("${carType} == null || ${carType} ==' '||${carType.value} == 9", false, false));
        return customRules;
    }

    public static RuleModel carTypeEffectPicture(String condition,String carType,Boolean visible){
        ObjectNode targetProp = JacksonUtil.createObjectNode();
        ObjectNode carTypePicture = JacksonUtil.createObjectNode();
        carTypePicture.put("visible", visible);
        carTypePicture.put("carType", carType);
        targetProp.putPOJO("carTypePicture", carTypePicture);


        ObjectNode target = JacksonUtil.createObjectNode();
        target.put("condition", condition);
        target.putPOJO("targetProp", targetProp);
        target.put("targetPropType", "expression");
        target.put("version", "v2");

        RuleModel ruleModel = new RuleModel();
        ruleModel.setTarget(target);
        return ruleModel;
    }

    private static RuleModel carTypePictureVisibleRuleMode(String condition, boolean visible) {
        ObjectNode targetProp = JacksonUtil.createObjectNode();
        ObjectNode carTypePicture = JacksonUtil.createObjectNode();
        carTypePicture.put("visible", visible);
        targetProp.putPOJO("carTypePicture", carTypePicture);


        ObjectNode target = JacksonUtil.createObjectNode();
        target.put("condition", condition);
        target.putPOJO("targetProp", targetProp);
        target.put("version", "v2");

        RuleModel ruleModel = new RuleModel();
        ruleModel.setTarget(target);
        return ruleModel;
    }

    /**
     * {
     * "target": {
     * "condition": "${carType} *= 9",
     * "targetProp": {
     * "vacancyRate": {
     * "visible": false
     * }
     * },
     * "version": "v2"
     * }
     * }
     */
    private static RuleModel vacancyRateRuleModel(String condition, boolean visible, boolean required) {
        ObjectNode targetProp = JacksonUtil.createObjectNode();
        ObjectNode vacancyRateGroup = JacksonUtil.createObjectNode();
        vacancyRateGroup.put("visible", visible);
        vacancyRateGroup.put("serverRequired", required);
        targetProp.putPOJO("vacancyRate", vacancyRateGroup);


        ObjectNode target = JacksonUtil.createObjectNode();
        target.put("condition", condition);
        target.putPOJO("targetProp", targetProp);
        target.put("version", "v2");

        RuleModel ruleModel = new RuleModel();
        ruleModel.setTarget(target);
        return ruleModel;
    }
}
