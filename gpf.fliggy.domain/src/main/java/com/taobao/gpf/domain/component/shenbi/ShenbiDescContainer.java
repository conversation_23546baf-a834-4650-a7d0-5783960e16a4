package com.taobao.gpf.domain.component.shenbi;

import lombok.Data;

import java.util.List;

/**
 * @description: 旺铺装修包含数据
 * @author: luoli
 * @create: 2019-04-16
 */
@Data
public class ShenbiDescContainer {
    /**
     * 千人千面人群数据（wireless_module_desc.customization_data）
     */
    private String customizationData;

    /**
     * 无线端xml详情（wldesc.desc_path）
     */
    private String detail;

    /**
     * // 需要push到IC的标
     */
    private ShenbiFeatures features;

    /**
     * 模块列表（wireless_module_desc.wireless_module_list）
     */
    private String moduleList;

    /**
     * 无线端native详情（wireless_module_desc.module_template_path）
     */
    private String nativeDetail;

    /**
     * 需要从IC上面remove掉的tags
     */
    private List<Long> removeTags;

    /**
     * 需要push到IC的tag
     * 25282：//是否有h5图文详情
     * 233282：//是否有直播模块
     * 233346：//是否有群聊模块
     */
    private List<Long> tags;
}
