package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * 线路定制游业务身份
 */
public class LineCustomizedIdentity extends BaseAppBizIdentity {
    @Override
    public boolean isFitByCustom(IdentityParam identityParam) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject) identityParam.getWro()).getCategory();
        Long categoryId = (long) stdCategoryDO.getCategoryId();
        return FliggySwitchConfig.customTripCats.contains(categoryId);
    }
}
