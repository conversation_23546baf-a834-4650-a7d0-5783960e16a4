package com.taobao.gpf.domain.component.deposit.depositselect;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.shared.extpoint.build.BaseBuildModelCompStrategy;

import com.taobao.gpf.domain.component.common.ext.CommonExtPoint;
import com.taobao.gpf.domain.utils.FliggyParamUtil;

/**
 * @description:
 * <AUTHOR>
 */

public class FliggyDepositSelectBuildModelCompStrategy<V> extends BaseBuildModelCompStrategy {

    @Override
    public String getName() {
        return "fliggyDepositSelectBuildModelCompStrategy";
    }

	@Override
	protected void dynamicBuildCompDO(AbstractCompDO compdo, Param param) {
		boolean internationalUserEnable = CommonExtPoint.internationalUserEnableCheck(FliggyParamUtil.getUserDO(param),FliggyParamUtil.getCategoryId(param));
		if(internationalUserEnable){
			compdo.setValue("conbinePay");
		}else{
			compdo.setValue("noDeposit");
		}
	}
}
