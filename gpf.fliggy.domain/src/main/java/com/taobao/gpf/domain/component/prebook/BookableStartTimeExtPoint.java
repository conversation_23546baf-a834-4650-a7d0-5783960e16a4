package com.taobao.gpf.domain.component.prebook;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.EnumTextPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.common.util.QuaDateUtils;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.helper.PreReservationTimeConfig;
import com.taobao.gpf.domain.utils.FicItemUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
public class BookableStartTimeExtPoint implements IInjectExtension {

    @Resource
    private PreReservationTimeConfig preReservationTimeConfig;

    @PrepareForRender(key = "bookableStartTime-fliggy-addDataSource")
    public void  addDataSource(CompExtParam param, BasicCompDO<Date> compDO) {
        if(null == compDO.getPagemodel() || !(compDO.getPagemodel() instanceof EnumTextPageModel)){
            return;
        }

        EnumTextPageModel enumTextPageModel = (EnumTextPageModel)compDO.getPagemodel();
        List<TextValueModel> dataSource = Lists.newArrayList();

        List<String> dateStr = FliggySwitchConfig.prebookStartDate;
        Date startDate = null;
        String startDateStr = null;
        String startDateLabel = null;
        for(String startStr : dateStr){
            try {
                startDateStr = startStr;
                startDate = QuaDateUtils.stringToDate(startStr, "yyyy-MM-dd HH:mm");
                startDateLabel = QuaDateUtils.dateToString(startDate, "yyyy年MM月dd日 HH:mm");
            } catch (Exception e){
                log.warn("method:addDataSource,e.getMessage():"+e.getMessage(),e);
                continue;
            }
        }

        dataSource.add(new TextValueModel(startDateStr, startDateLabel));
        enumTextPageModel.setDataSource(dataSource);
    }

    @PrepareForRender(key = "bookableStartTime-fliggy-addCompBottomInfo")
    public void buildCompBottomInfo(CompExtParam param, BasicCompDO<Date> compDO) {
        InfoModel infoModel = compDO.getPagemodel().getInfo();
        List<String> dateStrList = FliggySwitchConfig.prebookStartDate;
        if (CollectionUtils.isNotEmpty(dateStrList)) {
            String dateStr = dateStrList.get(0);
            infoModel.setBottom(Lists.newArrayList("系统设置的大促活动时间：" + dateStr + "。如果要报名大促活动，请将预约开始时间设置为系统设置的大促活动时间。成功报名大促后，系统将锁定预约开始时间直至大促结束。"));
            compDO.getPagemodel().setInfo(infoModel);
        }
    }

    @PrepareForRender(key = "reservation-checkBookableStartTime")
    public void checkBookableTimeProcessRender(AbstractCompDO compDO, @Category StdCategoryDO category, CompExtParam param) {
        //如果是新增，则不校验
        if(!param.getReq().isEdit()){
            return;
        }
        Long itemId = FicItemUtils.getItemIdFromDB(param);
        if(itemId == null){
            return;
        }
        if(FliggySwitchConfig.lockReservationBookingStartTime){
            ((BasicPageModel) compDO.getPagemodel()).setReadonly(true);
            return;
        }
        boolean isLocked = preReservationTimeConfig.isLockTime(itemId);
        if(isLocked) {
            ((BasicPageModel) compDO.getPagemodel()).setReadonly(true);
        }
    }

}
