package com.taobao.gpf.domain.component.catprop.match;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.adapt.ICompParseJsonStrategy;
import com.alibaba.gpf.sdk.param.CompExtParam;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * @description:
 * <AUTHOR>
 */

public class FliggyCatItemPropBlankCompPaseJsonStrategy implements ICompParseJsonStrategy<AbstractCompDO> {

    @Override
    public void parseJson(AbstractCompDO compDO, JsonNode node, CompExtParam param) {

    }

    @Override
    public JsonNode renderJson(AbstractCompDO compDO, CompExtParam param) {
        return null;
    }

    @Override
    public String getName() {
        return "fliggyCatItemPropBlankCompPaseJsonStrategy";
    }
}