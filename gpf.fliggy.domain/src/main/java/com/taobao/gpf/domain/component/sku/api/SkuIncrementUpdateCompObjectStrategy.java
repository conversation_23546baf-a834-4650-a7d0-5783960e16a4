package com.taobao.gpf.domain.component.sku.api;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.gpf.base.publish.domain.component.sku.BasicSkuTableCompDO;
import com.alibaba.gpf.base.publish.domain.component.sku.BasicSkuTableRowDO;
import com.alibaba.gpf.sdk.compdo.TableCellCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.constant.AdapterTypeEnum;
import com.alibaba.gpf.sdk.extpoint.adapt.ICompParseObjectStrategy;
import com.alibaba.gpf.sdk.model.parammodel.ParamModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.boot.AdapterConfigBoot;
import com.alibaba.gpf.shared.boot.ExtensionPluginBoot;
import com.alibaba.gpf.shared.extpoint.adapt.BaseCompParseObjectStrategy;

import com.google.common.collect.Lists;
import com.taobao.gpf.domain.model.api.base.BaseProductDTO;
import com.taobao.gpf.domain.model.api.base.BaseSkusDTO.BaseSkuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @description:
 * <AUTHOR>
 */
@Slf4j
public class SkuIncrementUpdateCompObjectStrategy extends BaseCompParseObjectStrategy<BasicSkuTableCompDO, BaseProductDTO> {


    @Override
    public String getName() {
        return "skuIncrementUpdateCompObjectStrategy";
    }

    @Override
    public void parseObject(BasicSkuTableCompDO skuTableCompDO, BaseProductDTO productDTO, CompExtParam extParam, ParamModel paramModel) {
        //入参pojo里没有
        if (null == productDTO.getSkus() || productDTO.getSkus().isEmpty()) {
            return;
        }
        //商品本身没有sku值
        if(CollectionUtils.isEmpty(skuTableCompDO.getValue())){
            return;
        }

        //Map<String,BaseSkuDTO> newOuterIdAndSkuMap = productDTO.getSkus().getBaseSkuDTOList().stream()
        //    .filter(skuDTO -> StringUtils.isNotBlank(skuDTO.getOuterSkuId()))
        //    .collect(Collectors.toMap(BaseSkuDTO::getOuterSkuId, skuDTO -> skuDTO));

        Map<String, AdapterCompConfig> adapterMap = AdapterConfigBoot.getInstance().getCompAdapters(extParam.getBizIdentity().getIdentity(), AdapterTypeEnum.OBJECT.name().toLowerCase());
        Map<String, ICompParseObjectStrategy> strategyMap = ExtensionPluginBoot.getInstance().getNameExtensionMap(ICompParseObjectStrategy.class);

        for(final BaseSkuDTO newSkuDTO : productDTO.getSkus().getBaseSkuDTOList()){
            if(StringUtils.isBlank(newSkuDTO.getOuterSkuId()) && CollectionUtils.isEmpty(newSkuDTO.getSalePropertyPairs())){
                continue;
            }
            BasicSkuTableRowDO matchSkuTableRowDO = findOriginalSkuRowDO(skuTableCompDO,newSkuDTO);
            skuTableCompDO.getColumns().forEach(column -> {
                if (column.getPagemodel() == null) {
                    return;
                }
                TableCellCompDO cellCompDO = skuTableCompDO.getCell(matchSkuTableRowDO, column);

                AdapterCompConfig compConfig = adapterMap.get(cellCompDO.getCompName());
                if(null == compConfig){
                    return;
                }
                String strategyName = compConfig.getStrategy();
                ICompParseObjectStrategy strategy = strategyMap.get(strategyName);

                if (strategy != null) {
                    try {
                        strategy.parseObject(cellCompDO, newSkuDTO, extParam, compConfig.getParam());
                    }
                    catch (Exception exp) {
                        log.error(String.format("[SkuCompObjectStrategy][parseObject][ChildProp], Child Component[%s].", cellCompDO.getCompName()), exp);
                    }
                }
            });
        }

    }

    private BasicSkuTableRowDO findOriginalSkuRowDO(BasicSkuTableCompDO skuTableCompDO,BaseSkuDTO matchSkuDTO) {
        BasicSkuTableRowDO originalBasicSkuTableRowDO = doFindOriginalSkuRowDO(skuTableCompDO.getValue(),matchSkuDTO);
        //匹配到则更新
        if(null != originalBasicSkuTableRowDO){
            return originalBasicSkuTableRowDO;
        }else{
            //没匹配到新增
            BasicSkuTableRowDO newSkuTableRowDO =  skuTableCompDO.createRow();
            skuTableCompDO.getValue().add(newSkuTableRowDO);
            return newSkuTableRowDO;
        }
    }

    private BasicSkuTableRowDO doFindOriginalSkuRowDO(List<BasicSkuTableRowDO> originalSkuRowDOs,BaseSkuDTO matchSkuDTO) {
        if(CollectionUtils.isEmpty(originalSkuRowDOs)){
            return null;
        }
        String matchOuterSkuId = matchSkuDTO.getOuterSkuId();
        List<String> matchPVs = CollectionUtils.isNotEmpty(matchSkuDTO.getSalePropertyPairs())
            ?matchSkuDTO.getSalePropertyPairs().stream().filter(pvPairDTO -> 0!=pvPairDTO.getPropertyId() && 0!=pvPairDTO.getValueId()).map(pvPairDTO -> pvPairDTO.getPropertyId()+":"+pvPairDTO.getValueId()).collect(Collectors.toList())
            : Lists.newArrayList();
        for(final BasicSkuTableRowDO originRowDO : originalSkuRowDOs){
            //outId匹配上了
            if(StringUtils.equals(originRowDO.getSkuOuterId(),matchOuterSkuId)){
                return originRowDO;
            }
            if(CollectionUtils.isEmpty(originRowDO.getSaleProps())){
                continue;
            }
            List<String> originalPVs = originRowDO.getSaleProps().stream()
                .filter(pvPairDO -> 0!=pvPairDO.getValueId() && 0!=pvPairDO.getPropertyId())
                .map(pvPairDO -> pvPairDO.getPropertyId()+":"+pvPairDO.getValueId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(originalPVs)){
                continue;
            }
            //pv对匹配上了
            if(originalPVs.containsAll(matchPVs)){
                return originRowDO;
            }
        }
        return null;
    }

}
