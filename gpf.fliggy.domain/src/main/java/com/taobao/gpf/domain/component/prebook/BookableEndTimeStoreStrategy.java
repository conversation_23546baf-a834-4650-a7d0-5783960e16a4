package com.taobao.gpf.domain.component.prebook;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;

import java.util.Date;

public class BookableEndTimeStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<Date>> {


    @Override
    public String getName() {
        return "bookableEndTimeStoreStrategy";
    }

    @Override
    public void parseStore(BasicCompDO<Date> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (storeDO.getBcEndTime() != null) {
            compDO.setValue(storeDO.getBcEndTime());
        }
    }

    @Override
    public void transferStore(BasicCompDO<Date> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (compDO.getValue() != null) {
            storeDO.setBcEndTime(compDO.getValue());
        }
    }
}
