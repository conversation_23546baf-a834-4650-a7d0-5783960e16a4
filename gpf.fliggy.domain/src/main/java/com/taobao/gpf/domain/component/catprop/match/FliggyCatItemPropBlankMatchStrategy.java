package com.taobao.gpf.domain.component.catprop.match;

import javax.annotation.Resource;

import com.alibaba.gpf.base.publish.sdk.extpoint.ICompTemplateMatchStrategy;
import com.alibaba.gpf.base.publish.sdk.param.MatchParam;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyDO;
import com.taobao.gpf.domain.component.catprop.NormalCatPropFilter;
import com.taobao.gpf.domain.config.TravelSellConfig;


/**
 * @description:
 * <AUTHOR>
 */

public class FliggyCatItemPropBlankMatchStrategy implements ICompTemplateMatchStrategy<StdCategoryPropertyDO> {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public boolean match(MatchParam param, StdCategoryPropertyDO stdCategoryPropertyDO) {
        //TODO
        return NormalCatPropFilter.match(stdCategoryPropertyDO);
    }

    @Override
    public String getName() {
        return "fliggyCatItemPropBlankMatchStrategy";
    }
}