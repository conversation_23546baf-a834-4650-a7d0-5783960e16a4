package com.taobao.gpf.domain.component.poi;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.adapt.ICompParseJsonStrategy;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.usa.util.StringUtils;


/**
 * Created by maoxinming on 2019/4/19.
 */
public class ItemPoiCompJsonStrategy  implements ICompParseJsonStrategy<BasicCompDO<String>> {
    @Override
    public void parseJson(BasicCompDO<String> compDO, JsonNode node, CompExtParam param) {
       compDO.setValue(node.toString());
    }

    @Override
    public JsonNode renderJson(BasicCompDO<String> compDO, CompExtParam param) {
        ObjectNode node = compDO.getPagemodel().toJsonNode() ;
        if(null != compDO.getControlDO()){
            //填充node数据
            compDO.getControlDO().toJsonNode(node);
        }
        if(StringUtils.isNotEmpty(compDO.getValue())){
            node.put("value",compDO.getValue());
        }
        return node ;
    }

    @Override
    public String getName() {
        return "itemPoiCompJsonStrategy";
    }
}
