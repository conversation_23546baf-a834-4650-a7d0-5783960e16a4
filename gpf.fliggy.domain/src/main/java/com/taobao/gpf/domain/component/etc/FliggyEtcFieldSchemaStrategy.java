package com.taobao.gpf.domain.component.etc;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.complexfield.ComplexFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.business.domain.component.etc.EtcControlDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.taobao.gpf.domain.constant.schema.SchemaFieldIdEnum;
import com.taobao.gpf.domain.utils.schema.vistor.Field2JsonVistor;
import com.taobao.gpf.domain.utils.schema.vistor.Json2FieldVistor;
import com.taobao.macenter.domain.dto.EtcFormDTO;
import com.taobao.macenter.util.ItemEtcConvertUtil;
import com.taobao.top.schema.depend.DependExpress;
import com.taobao.top.schema.enums.FieldTypeEnum;
import com.taobao.top.schema.enums.RuleTypeEnum;
import com.taobao.top.schema.enums.ValueTypeEnum;
import com.taobao.top.schema.factory.SchemaFactory;
import com.taobao.top.schema.field.ComplexField;
import com.taobao.top.schema.field.Field;
import com.taobao.top.schema.field.InputField;
import com.taobao.top.schema.field.SingleCheckField;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 小名
 * @date: 2020-03-03
 * @time: 17:49
 * @version:1.0
 */
@Deprecated
public class FliggyEtcFieldSchemaStrategy extends ComplexFieldSchemaStrategy<BasicCompDO<String>> {


    @Override
    public String getName() {
        return "fliggyEtcFieldSchemaStrategy";
    }

    @Override
    protected void setCompValue(BasicCompDO<String> compDO, ComplexField field, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        if(null == field.getComplexValue()){
            return;
        }
        JsonNode valueJson = Field2JsonVistor.parse(field,field, param);
        if(null == valueJson){
            return;
        }
        compDO.setValue(valueJson.toString());
        // 在这里构建可以解除SKU和电子凭证的执行顺序问题
        EtcFormDTO etcFormDTO = ItemEtcConvertUtil.converToEtcFormDO(compDO.getValue());
        if (etcFormDTO == null) {
            return;
        }
        ((EtcControlDO)compDO.getControlDO()).setEtcDTO(etcFormDTO);
    }

    @Override
    protected void renderSchemaFieldValue(ComplexField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext context) {
        if(StringUtils.isBlank(compDO.getValue()) || !((EtcControlDO)compDO.getControlDO()).isSelected()){
            return;
        }
        JsonNode valueJson = JacksonUtil.readTree(compDO.getValue());
        Field valueField = Json2FieldVistor.parseJson(field,valueJson);
        if(valueField instanceof ComplexField){
            field.setComplexValue(((ComplexField) valueField).getComplexValue());
        }
    }

    protected void customRenderField(ComplexField field, BasicCompDO<String> compDO, CompExtParam param, AdapterCompConfig compConfig, SchemaParseContext config) {
        super.customRenderField(field, compDO, param, compConfig, config);
        field.addRule(RuleTypeEnum.DISABLE_RULE).addDependGroup(SchemaFieldIdEnum.EXTRACTWAYSELECT.getId(),compDO.getCompName(), DependExpress.SYMBOL_CONTAINS);
        //设置有效期信息
        fillValidityField(field);
        //属性设置
        fillPropertyField(field);
        //核销门店库
        fillStoreField(field,compDO);
        //码池
        fillPoolField(field);
    }

    /**
     * 码池
     * @param field
     */
    private void fillPoolField(ComplexField field) {
        SingleCheckField supportCodePoolField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        supportCodePoolField.setId(SchemaFieldIdEnum.SUPPORTCODEPOOL.getId());
        supportCodePoolField.setName(SchemaFieldIdEnum.SUPPORTCODEPOOL.getName());
        supportCodePoolField.addOption("勾选","true");
        supportCodePoolField.addOption("不勾选","false");
        supportCodePoolField.setValue("false");
        field.add(supportCodePoolField);


        SingleCheckField codePoolTypeField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        codePoolTypeField.setId(SchemaFieldIdEnum.CODEPOOLTYPE.getId());
        codePoolTypeField.setName(SchemaFieldIdEnum.CODEPOOLTYPE.getName());
        codePoolTypeField.addOption("针对宝贝","1");
        codePoolTypeField.addOption("针对sku","2");
        field.add(codePoolTypeField);

        SingleCheckField poolIdField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        poolIdField.setId(SchemaFieldIdEnum.POOLID.getId());
        poolIdField.setName(SchemaFieldIdEnum.POOLID.getName());
        poolIdField.addOption("针对宝贝","1");
        poolIdField.addOption("针对sku","2");
        field.add(poolIdField);

    }

    /**
     * //核销门店库
     * @param field
     */
    private void fillStoreField(ComplexField field,BasicCompDO<String> compDO) {
        SingleCheckField packageValueField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        packageValueField.setId(SchemaFieldIdEnum.PACKAGEVALUE.getId());
        packageValueField.setName(SchemaFieldIdEnum.PACKAGEVALUE.getName());
        packageValueField.addRule(RuleTypeEnum.TIP_RULE,"核销门店库,切换核销库后，如宝贝发布成功会替换原关联库信息，请谨慎操作");
        //TODO OPTIONs
        field.add(packageValueField);

        SingleCheckField storeBookingField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        storeBookingField.setId(SchemaFieldIdEnum.STOREBOOKING.getId());
        storeBookingField.setName(SchemaFieldIdEnum.STOREBOOKING.getName());
        storeBookingField.addOption("勾选","true");
        storeBookingField.addOption("不勾选","false");
        storeBookingField.setValue("false");
        field.add(storeBookingField);
    }

    /**
     * //属性设置
     * @param field
     */
    private void fillPropertyField(ComplexField field) {
        SingleCheckField writeoffField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        writeoffField.setId(SchemaFieldIdEnum.WRITEOFF.getId());
        writeoffField.setName(SchemaFieldIdEnum.WRITEOFF.getName());
        writeoffField.addOption("核销打款","true");
        writeoffField.addOption("T+N打款","false");
        writeoffField.setValue("true");
        field.add(writeoffField);


        SingleCheckField refundField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        refundField.setId(SchemaFieldIdEnum.REFUND.getId());
        refundField.setName(SchemaFieldIdEnum.REFUND.getName());
        refundField.addOption("勾选","true");
        refundField.addOption("不勾选","false");
        refundField.setValue("false");
        field.add(refundField);

        InputField refundValueField = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        refundValueField.setId(SchemaFieldIdEnum.REFUNDVALUE.getId());
        refundValueField.setName(SchemaFieldIdEnum.REFUNDVALUE.getName());
        refundValueField.addValueTypeRule(ValueTypeEnum.INTEGER);
        refundValueField.addRule(RuleTypeEnum.MIN_INPUT_NUM_RULE,"1");
        refundValueField.addRule(RuleTypeEnum.MAX_INPUT_NUM_RULE,"100");
        refundValueField.addRule(RuleTypeEnum.REQUIRED_RULE,"false").addDependGroup(SchemaFieldIdEnum.REFUNDVALUE.getId(),"true");
        field.add(refundValueField);


        SingleCheckField fullRefundField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        fullRefundField.setId(SchemaFieldIdEnum.FULLREFUND.getId());
        fullRefundField.setName(SchemaFieldIdEnum.FULLREFUND.getName());
        fullRefundField.addOption("勾选","true");
        fullRefundField.addOption("不勾选","false");
        fullRefundField.setValue("false");
        field.add(fullRefundField);

        InputField fullRefundValueField = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        fullRefundValueField.setId(SchemaFieldIdEnum.FULLREFUNDVALUE.getId());
        fullRefundValueField.setName(SchemaFieldIdEnum.FULLREFUNDVALUE.getName());
        fullRefundValueField.addValueTypeRule(ValueTypeEnum.INTEGER);
        fullRefundValueField.addRule(RuleTypeEnum.MIN_INPUT_NUM_RULE,"100");
        fullRefundValueField.addRule(RuleTypeEnum.MAX_INPUT_NUM_RULE,"100");
        fullRefundValueField.addRule(RuleTypeEnum.REQUIRED_RULE,"false").addDependGroup(SchemaFieldIdEnum.FULLREFUND.getId(),"true");
        field.add(fullRefundValueField);
    }

    /**
     * //设置有效期信息
     * @param field
     */
    private void fillValidityField(ComplexField field) {
        SingleCheckField validityField = (SingleCheckField)SchemaFactory.createField(FieldTypeEnum.SINGLECHECK);
        validityField.setId(SchemaFieldIdEnum.VALIDITY.getId());
        validityField.setName(SchemaFieldIdEnum.VALIDITY.getName());
        validityField.addOption("xxxx-xx-xx 到 xxxx-xx-xx","1");
        validityField.addOption("购买成功日 至 xxxx-xx-xx","2");
        validityField.addOption("购买成功 xx 天内有效","3");
        validityField.setValue("1");
        field.add(validityField);

        InputField validityValueField = (InputField)SchemaFactory.createField(FieldTypeEnum.INPUT);
        validityValueField.setId(SchemaFieldIdEnum.VALIDITYVALUE.getId());
        validityValueField.setName(SchemaFieldIdEnum.VALIDITYVALUE.getName());
        validityValueField.addRule(RuleTypeEnum.DEV_TIP_RULE,"如果是validity=1，则validityValue=\"xxxx-xx-xx,xxxx-xx-xx\"，如果validity=2，则validityValue=\"xxxx-xx-xx\"，如果validity=3，则validityValue=\"xxx\" 最大3位数");
        field.add(validityValueField);
    }
}