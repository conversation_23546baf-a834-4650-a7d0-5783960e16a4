package com.taobao.gpf.domain.component.play;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.multicheckfield.MultiCheckFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.google.common.collect.Lists;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.model.play.PlayConfigModel;
import com.taobao.gpf.domain.model.play.PlayView;
import com.taobao.gpf.domain.repository.PlayServiceRepo;
import com.taobao.gpf.domain.repository.PlayTypeSwitchServiceRepo;
import com.taobao.gpf.domain.shared.diamond.PlayConfigDiamondProcessor;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.top.schema.field.MultiCheckField;
import com.taobao.top.schema.rule.MaxInputNumRule;
import com.taobao.top.schema.value.Value;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @desc: 玩法组件schema转换策略
 * @author: luoli
 * @date: 2020/3/9
 * @time: 10:22 AM
 * @version:1.0
 */
public class PlayFieldSchemaStrategy extends MultiCheckFieldSchemaStrategy<AbstractCompDO<List<TextValueModel>>> {

    @Resource
    PlayConfigDiamondProcessor playConfigDiamondProcessor;

    @Resource
    private PlayServiceRepo playServiceRepo;


    @Resource
    private PlayTypeSwitchServiceRepo playTypeSwitchServiceRepo;

    @Override
    public String getName() {
        return "playFieldSchemaStrategy";
    }

    @Override
    protected void setCompValue(AbstractCompDO<List<TextValueModel>> compDO,
                                MultiCheckField field, CompExtParam compExtParam,
                                AdapterCompConfig adapterCompConfig, SchemaParseContext schemaParseContext) {
        if(CollectionUtils.isEmpty(field.getValues())){
            return;
        }
        List<TextValueModel> values = Lists.newArrayList();
        for(Value value : field.getValues()){
            //传入值非数字，报错
            if (!NumberUtils.isDigits(value.getValue())){
                compExtParam.getRes().addErrorCode(FliggyErrorEnum.CHK_TOP_PLAY_PROP_VALUE_ERROR.getErrorCode(compDO.getCompName()).putErrorParam("PLAYID",value.getValue()));
            }
            String playName = "";
            int catId = FliggyParamUtil.getCategoryId(compExtParam);
            if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(String.valueOf(catId)))) {
                PlayType playType = playTypeSwitchServiceRepo.getPlayType(Long.valueOf(value.getValue()),
                    FliggyParamUtil.getUserId(compExtParam));
                if(playType != null){
                    playName = playType.getName();
                }
            }else {
                playName = playServiceRepo.getPlayName(Long.valueOf(value.getValue()));
            }
            //玩法名为空(传入的玩法值不正确），则报错
            if (StringUtils.isBlank(playName)){
                compExtParam.getRes().addErrorCode(FliggyErrorEnum.CHK_TOP_PLAY_PROP_VALUE_ERROR.getErrorCode(compDO.getCompName()).putErrorParam("PLAYID",value.getValue()));
            }
            TextValueModel valueModel = new TextValueModel();
            valueModel.setValue(value.getValue());
            valueModel.setText(playName);
            values.add(valueModel);
        }
        compDO.setValue(values);
    }

    @Override
    protected void renderSchemaFieldValue(MultiCheckField field,
                                          AbstractCompDO<List<TextValueModel>> compDO,
                                          CompExtParam compExtParam, AdapterCompConfig adapterCompConfig,
                                          SchemaParseContext schemaParseContext) {
        if (CollectionUtils.isEmpty(compDO.getValue())){
            return;
        }
        List<Value> valueList = new ArrayList<>();
        for (TextValueModel valueModel : compDO.getValue()){
            Value value = new Value(valueModel.getValue());
            valueList.add(value);
        }
        field.setValues(valueList);
    }

    @Override
    protected void customRenderField(MultiCheckField field,
                                          AbstractCompDO<List<TextValueModel>> compDO,
                                          CompExtParam compExtParam, AdapterCompConfig adapterCompConfig,
                                          SchemaParseContext schemaParseContext) {
        int catId = FliggyParamUtil.getCategoryId(compExtParam);

        //设置最大可选个数
        if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(String.valueOf(catId)))) {
            field.add(new MaxInputNumRule(FliggySwitchConfig.useNewPlayCategoryMap.get(Long.valueOf(String.valueOf(catId))) + ""));
        }else {
            PlayConfigModel playConfigModel = playConfigDiamondProcessor.getPlayConfigModel(catId);
            field.add(new MaxInputNumRule(playConfigModel.getPlayNum()+ ""));
        }


        List<PlayView> playList = getPlayList(compExtParam);
        //打平玩法数据并填充
        fillPlayInfo(playList, field);
    }

    private List<PlayView> getPlayList(CompExtParam compExtParam){
        int catId = FliggyParamUtil.getCategoryId(compExtParam);
        if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(String.valueOf(catId)))) {
            List<PlayType> playTypeTree = playTypeSwitchServiceRepo.getPlayTypeTree((long)catId, 1L, FliggyParamUtil.getUserId(compExtParam));
            if (CollectionUtils.isEmpty(playTypeTree)) {
                return null;
            }
            return playTypeTree.stream()
                .map(playType -> buildPlayView(playType))
                .collect(Collectors.toList());
        } else {
            //获取可配置玩法列表
            return playServiceRepo.getValidPlayList((long)catId);
        }
    }

    private PlayView buildPlayView(PlayType playType) {
        PlayView playView = new PlayView();

        playView.setName(playType.getName());
        playView.setValue(String.valueOf(playType.getId()));
        playView.setLabel(playType.getName());
        List<PlayType> children = playType.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            List<PlayView> childPlayView = children.stream()
                .map(childrenPlay -> buildPlayView(childrenPlay))
                .collect(Collectors.toList());
            playView.setChildren(childPlayView);
        }
        if (CollectionUtils.isEmpty(playView.getChildren())) {
            playView.setSelectable(true);
        }

        return playView;
    }

    private void fillPlayInfo(List<PlayView> playList, MultiCheckField field) {
        if (CollectionUtils.isEmpty(playList)){
            return;
        }
        fillSelectablePlayInfo(playList, field);
    }

    private void fillSelectablePlayInfo(List<PlayView> playList, MultiCheckField field) {
        if (CollectionUtils.isEmpty(playList)){
            return;
        }
        for (PlayView playView : playList){
            if (null == playView){
                continue;
            }
            if (playView.isSelectable()){
                field.addOption(playView.getName(),playView.getValue());
            }
            if (CollectionUtils.isNotEmpty(playView.getChildren())){
                fillSelectablePlayInfo(playView.getChildren(), field);
            }
        }
    }
}
