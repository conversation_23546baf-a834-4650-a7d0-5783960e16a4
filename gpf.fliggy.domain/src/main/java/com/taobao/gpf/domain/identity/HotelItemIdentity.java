package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * <AUTHOR>
 * @date 2020/8/14 10:56 AM
 **/
public class HotelItemIdentity extends BaseAppBizIdentity {

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        FliggyRequiredObject wro = (FliggyRequiredObject)param.getWro();

        StdCategoryDO stdCategoryDO = wro.getCategory();
        Long categoryId = (long) stdCategoryDO.getCategoryId();
        return FliggySwitchConfig.hotelCatIds.contains(categoryId) || FliggySwitchConfig.onlineApplyCatIds.contains(categoryId) || FliggySwitchConfig.intlOnlineApplyCatIds.contains(categoryId);
    }
}
