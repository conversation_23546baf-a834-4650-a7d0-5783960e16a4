package com.taobao.gpf.domain.component.cartype.service;

/**
 * 空座率枚举类
 *
 * <AUTHOR>
 */
public enum VacancyValueEnum {
    PERCENT_ZERO(1001,"每人一正座"),
    PERCENT_LESSTHANTEN(1002,"小于10%"),
    PERCENT_TENTOTWENTY(1003,"10%到20%"),
    PERCENT_TWENTYTOTHIRTY(1004,"20%到30%"),
    PERCENT_THIRTYTOFIFTY(1005,"30%到50%"),
    PERCENT_MORETHANFIFTY(1006,"大于50%"),
    ;

    private final String text;
    private final Integer value;


    private VacancyValueEnum( Integer value,String text) {
        this.text = text;
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public Integer getValue() {
        return value;
    }

    public static VacancyValueEnum getVacancyValue(int type) {
        for (VacancyValueEnum valueEnum : values()) {
            if (valueEnum.getValue() == type) {
                return valueEnum;
            }
        }
        return null;
    }
}
