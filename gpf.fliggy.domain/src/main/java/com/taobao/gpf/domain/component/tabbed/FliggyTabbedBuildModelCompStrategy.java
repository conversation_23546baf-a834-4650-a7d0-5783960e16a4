package com.taobao.gpf.domain.component.tabbed;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.TabbedCompDO;
import com.alibaba.gpf.sdk.config.CompConfig;
import com.alibaba.gpf.sdk.config.TabbedCompConfig;
import com.alibaba.gpf.sdk.extpoint.build.IBuildModelCompStrategy;
import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.shared.ExtensionRouterFatory;
import com.alibaba.gpf.shared.extpoint.build.BaseBuildModelCompStrategy;

import java.util.ArrayList;
import java.util.List;


/**
 * @description:
 * <AUTHOR>
 */

public class FliggyTabbedBuildModelCompStrategy<V> extends BaseBuildModelCompStrategy {

    @Override
    public String getName() {
        return "fliggyTabbedBuildModelCompStrategy";
    }
    
	@Override
	public AbstractCompDO buildCompDO(CompConfig cc, Param param) throws Exception {
		//构建parent
		TabbedCompDO parentDO = (TabbedCompDO) super.buildCompDO(cc, param);

		TabbedCompConfig<V> tabbedCompConfig = (TabbedCompConfig<V>) cc;
		register(param,parentDO,tabbedCompConfig.getChildren());
		if(tabbedCompConfig.getNav() != null) {
			CompConfig nav = tabbedCompConfig.getNav();
			IBuildModelCompStrategy navBuildCs = ExtensionRouterFatory.getPlugin(IBuildModelCompStrategy.class, nav.getBuildStrategy());
			AbstractCompDO navCompDO = null!=navBuildCs?navBuildCs.buildCompDO(nav,param):super.buildCompDO(nav, param);
			parentDO.setNavCompDO(navCompDO);
		}
		parentDO.setRelated(tabbedCompConfig.getRelated());
		return parentDO;
	}

	private void register(Param param, TabbedCompDO parentCompDO, List<CompConfig> children) throws Exception {
		if (children.isEmpty()) {
			return;
		}
		List<AbstractCompDO> list = new ArrayList<>();
		for (CompConfig cc : children) {
			IBuildModelCompStrategy buildCs = ExtensionRouterFatory.getPlugin(IBuildModelCompStrategy.class, cc.getBuildStrategy());
			AbstractCompDO compdo = buildCs.buildCompDO(cc, param);
			compdo.setParentCompDO(parentCompDO);
			list.add(compdo);
		}
		parentCompDO.setChildren(list);
	}


}
