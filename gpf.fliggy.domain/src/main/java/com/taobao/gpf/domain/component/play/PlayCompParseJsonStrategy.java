package com.taobao.gpf.domain.component.play;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.model.pagemodel.EnumTextListPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.shared.componet.BaseCompParseJsonStrategy;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.model.play.PlayConfigModel;
import com.taobao.gpf.domain.repository.PlayTypeSwitchServiceRepo;
import com.taobao.gpf.domain.shared.diamond.PlayConfigDiamondProcessor;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 玩法组件json转换
 */

public class PlayCompParseJsonStrategy extends BaseCompParseJsonStrategy<AbstractCompDO<List<TextValueModel>>> {

    @Resource
    PlayConfigDiamondProcessor playConfigDiamondProcessor;

    @Resource
    private PlayTypeSwitchServiceRepo playTypeSwitchServiceRepo;

    @Override
    public String getName() {
        return "playCompParseJsonStrategy";
    }

    @Override
    public void parseJson(AbstractCompDO<List<TextValueModel>> compDO, JsonNode node, CompExtParam param) {
        if (null == node || node.isNull()) {
            return;
        }
        //玩法存在多选与单选两种情况
        List<TextValueModel> values = Lists.newArrayList();
        //多选情况下
        if (node.isArray()) {
            Iterator<JsonNode> iterator = node.iterator();
            while (iterator.hasNext()) {
                TextValueModel valueModel = new TextValueModel();
                JsonNode value = iterator.next();
                valueModel.setValue(value.get("value").asText());
                valueModel.setText(value.get("text").asText());
                values.add(valueModel);
            }
        } else {
            TextValueModel valueModel = new TextValueModel();
            valueModel.setValue(node.get("value").asText());
            valueModel.setText(node.get("text").asText());
            values.add(valueModel);
        }
        compDO.setValue(values);
    }

    @Override
    public JsonNode renderJson(AbstractCompDO<List<TextValueModel>> compDO, CompExtParam param) {
        if (compDO.getPagemodel() == null) {
            return null;
        }
        //pageModel to json
        ObjectNode objectNode = compDO.getPagemodel().toJsonNode();

        int catId = FliggyParamUtil.getCategoryId(param);
        PlayConfigModel playConfigModel = playConfigDiamondProcessor.getPlayConfigModel(catId);
        if (null != playConfigModel) {
            objectNode.put("maxItems", playConfigModel.getPlayNum());
        }
        if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(catId))) {
            objectNode.put("maxItems", FliggySwitchConfig.useNewPlayCategoryMap.get(Long.valueOf(catId)));
        }

        //asyncModel to json
        if (compDO.getAsyncmodel() != null) {
            renderAsync(compDO, objectNode, param);
        }
        if (compDO.getValue() != null) {
            processNonLeafValue(compDO, catId, param);
            renderValue(compDO, objectNode, param);
        }

        if (CollectionUtils.isEmpty(compDO.getValue())) {
            return objectNode;
        }
        //玩法存在多选与单选两种情况
        if (!(compDO.getPagemodel() instanceof EnumTextListPageModel)) {
            return objectNode;
        }

        EnumTextListPageModel pageModel = (EnumTextListPageModel) compDO.getPagemodel();
        if (pageModel.isMultiSelect()) {
            ArrayNode arrayNode = JacksonUtil.createArrayNode();
            for (final TextValueModel valueModel : compDO.getValue()) {
                ObjectNode valueNode = JacksonUtil.createObjectNode();
                valueNode.putPOJO("value", valueModel.getValue());
                valueNode.putPOJO("text", valueModel.getText());
                arrayNode.addPOJO(valueNode);
            }
            objectNode.putPOJO("value", arrayNode);
        } else {
            ArrayNode arrayNode = JacksonUtil.createArrayNode();
            ObjectNode valueNode = JacksonUtil.createObjectNode();
            valueNode.putPOJO("value", compDO.getValue().get(0).getValue());
            valueNode.putPOJO("text", compDO.getValue().get(0).getText());
            arrayNode.add(valueNode);
            objectNode.putPOJO("value", arrayNode);
        }

        return objectNode;
    }

    private void processNonLeafValue(AbstractCompDO<List<TextValueModel>> compDO, int catId, CompExtParam param) {
        List<TextValueModel> value = compDO.getValue();
        if (value == null) {
            return;
        }

        Iterator<TextValueModel> iterator = value.iterator();
        while (iterator.hasNext()) {
            TextValueModel next = iterator.next();
            PlayType playType = playTypeSwitchServiceRepo.getPlayType(Long.parseLong(next.getValue()), FliggyParamUtil.getUserId(param));
            if (playType != null) {
                if (CollectionUtils.isNotEmpty(playType.getChildren()) && FliggySwitchConfig.checkLeafPlayPropCategoryIds.contains((long) catId)) {
                    iterator.remove();
                }
            }
        }
    }
}
