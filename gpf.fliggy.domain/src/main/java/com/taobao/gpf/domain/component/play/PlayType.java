package com.taobao.gpf.domain.component.play;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 玩法树节点，防腐层，用于适配外部接口
 * <AUTHOR> 2024-08-13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlayType implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 玩法名称
     */
    private String name;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 子节点
     */
    private List<PlayType> children = new ArrayList<>();

    /**
     * 属性
     */
    private List<PlayTypeAttributes> attributes = new ArrayList<>();

    public boolean isValidPlayProp() {
        return status == 1;
    }
}