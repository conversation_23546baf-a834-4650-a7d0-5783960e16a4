package com.taobao.gpf.domain.component.images;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.annotation.bind.User;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.dataobject.ImageValueDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.ImagePageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alitrip.travel.common.result.QuaResult;
import com.alitrip.travelitemsmanager.common.service.FirstScreenGuessService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.eagleeye.EagleEye;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggyGraySwitchConfig;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.uic.common.domain.BaseUserDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Component
public class ImagesDescBottomExtPoint implements IInjectExtension {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Resource
    private FirstScreenGuessService firstScreenGuessService;

    @PrepareForRender(key = "images-bottom-desc-addRules", desc = "商品图片底部文案")
    public void imagesDescBottomAddrules(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        InfoModel infoModel = new InfoModel();
        infoModel.setBottom(FliggySwitchConfig.imagesDescBottomTextList);
        compDO.getPagemodel().setInfo(infoModel);

        // 宝贝主图文案改成商品主图
        if (categoryDO.getCategoryId() == travelSellConfig.getGuoneiGentuanyouCatId()) {
            ImagePageModel pagemodel = (ImagePageModel) compDO.getPagemodel();
            pagemodel.setPrompts(Arrays.asList("商品主图"));
        }
    }

    @Check(key = "check-image-count-whe-in-first-screen-guess", desc = "命中首猜时图片数量校验")
    public CheckResult checkImageCountWhenInFirstScreenGuess(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO category, @User BaseUserDO baseUserDO) {
        CheckResult checkResult = new CheckResult();

        //top请求进来不校验
        if (FliggyParamUtil.isTopSource(param.getReq())) {
            return checkResult;
        }
        // API 直连不校验
        if(FliggyParamUtil.isApiDirect(param.getReq())) {
            return checkResult;
        }

        if (!FliggyGraySwitchConfig.OPEN_IMAGE_COUNT_NOTIFICATION) {
            return checkResult;
        }
        if (FliggyParamUtil.imageCountConfirmed(param)) {
            //已经弹过了，这是确认后的流程
            return checkResult;
        }
        //没弹过，这里去弹
        Object value = compDO.getValue();
        List<ImageValueDO> images = Lists.newArrayList();

        //安全操作
        if (!(value instanceof List)) {
            return checkResult;
        }
        List<?> tempList = (List<?>) value;

        if (tempList.isEmpty() || tempList.get(0) instanceof ImageValueDO) {
            images = (List<ImageValueDO>) tempList;
        }
        //安全操作

        boolean imageCountLt4 = CollectionUtils.size(images) < 4;
        Long itemId = (Long) param.getReq().getSystemParam().getParams().get("id");
        if (imageCountLt4 && itemInFirstGuessYouLikeConsideringMock(itemId)) {
            checkResult.addErrorCode(FliggyErrorEnum.CHK_IMAGE_COUNT_AND_NOTIFY.getErrorCode());
            return checkResult;
        }
        return checkResult;
    }

    private boolean itemInFirstGuessYouLikeConsideringMock(Long itemId) {
        if (itemId == null || itemId <= 0) {
            return false;
        }

        if (FliggyGraySwitchConfig.MOCK_IN_FIRST_GUESS_YOU_LIKE){
            return true;
        }

        Boolean inFirstGuessYouLike = itemInFirstGuessYouLike(itemId);
        return BooleanUtils.isTrue(inFirstGuessYouLike);
    }

    private Boolean itemInFirstGuessYouLike(Long itemId){
        QuaResult<Boolean> quaResult;
        try {
            quaResult = firstScreenGuessService.itemInFirstGuessYouLike(itemId);
        } catch (Exception e) {
            LogUtil.sysErrorLog("firstScreenGuessService.itemInFirstGuessYouLike sys fail", e, String.valueOf(itemId), EagleEye.getTraceId());
            //不是主要流程，可以返回false
            return false;
        }
        if (quaResult == null || !quaResult.isSuccess()) {
            LogUtil.sysErrorLog("firstScreenGuessService.itemInFirstGuessYouLike biz fail ", String.valueOf(itemId), EagleEye.getTraceId(), JSON.toJSONString(quaResult));
            return false;
        }
        return quaResult.getModule();
    }


    @AteyeInvoker(description = "商品是否在首猜中", paraDesc = "itemId")
    public Boolean ateyeItemInFirstGuessYouLike(Long itemId) {
        return itemInFirstGuessYouLike(itemId);
    }
}
