package com.taobao.gpf.domain.component.flmap;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.extpoint.compPlugin.ICompInitStrategy;
import com.taobao.gpf.domain.model.pagemodel.FliggyMapValueModel;

import java.util.List;

public class FliggyMapInitStrategy implements ICompInitStrategy<BasicCompDO<List<FliggyMapValueModel>>> {
    
    @Override
    public BasicCompDO<List<FliggyMapValueModel>> getInitCompDO() {
        BasicCompDO<List<FliggyMapValueModel>> compDO = new BasicCompDO<>();
        compDO.setControlDO(new ControlDO());
        return compDO;
    }

    @Override
    public String getName() {
        return "fliggyMapInitStrategy";
    }
}
