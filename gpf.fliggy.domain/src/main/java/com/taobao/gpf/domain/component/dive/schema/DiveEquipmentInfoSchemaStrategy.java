package com.taobao.gpf.domain.component.dive.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.multicomplexfield.MultiComplexFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.ICompParseSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.ExtensionRouterFatory;
import com.google.common.collect.Lists;
import com.taobao.top.schema.field.Field;
import com.taobao.top.schema.field.MultiComplexField;
import com.taobao.top.schema.value.ComplexValue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 潜水装备信息组件Schema策略
 * 处理catList类型的父组件，包含装备选择和装备说明两个子组件
 * 
 * <AUTHOR> Code
 * @date 2025-08-28
 */
public class DiveEquipmentInfoSchemaStrategy extends MultiComplexFieldSchemaStrategy<ParentCompDO<AbstractCompDO>> {

    @Override
    protected void customRenderField(MultiComplexField field, ParentCompDO<AbstractCompDO> compDO, 
                                   CompExtParam param, AdapterCompConfig compConfig, 
                                   SchemaParseContext context) {
        if (compDO == null || CollectionUtils.isEmpty(compDO.getChildComps())) {
            return;
        }

        // 遍历处理子组件，将子组件的schema添加到父组件的schema中
        compDO.getChildComps().forEach(childComp -> {
            AdapterCompConfig childConfig = compConfig.getChildren().stream()
                    .filter(config -> StringUtils.equals(config.getCompname(), childComp.getCompName()))
                    .findFirst().orElse(null);

            if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                return;
            }

            ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(
                    ICompParseSchemaStrategy.class, childConfig.getStrategy());
            if (schemaStrategy == null) {
                return;
            }

            // 创建子组件的schema
            SchemaParseContext cloneContext = context.clone();
            cloneContext.setShowValue(true);
            Field childField = schemaStrategy.transferSchema(childComp, param, childConfig, cloneContext);
            if (childField != null) {
                field.add(childField);
            }
        });
    }

    @Override
    protected void setCompValue(ParentCompDO<AbstractCompDO> compDO, MultiComplexField field, 
                              CompExtParam param, AdapterCompConfig compConfig, 
                              SchemaParseContext context) {
        if (field == null || CollectionUtils.isEmpty(field.getComplexValues())) {
            return;
        }

        // 遍历复杂字段中的值，解析到各个子组件
        List<ComplexValue> complexValues = field.getComplexValues();
        
        // 对于catList类型，通常只有一个ComplexValue，包含所有子组件的数据
        if (CollectionUtils.isNotEmpty(complexValues)) {
            ComplexValue complexValue = complexValues.get(0);
            
            // 处理每个子组件的值
            for (AbstractCompDO childrenCompDO : compDO.getChildren()) {
                AdapterCompConfig childConfig = compConfig.getChildren().stream()
                        .filter(e -> StringUtils.equals(childrenCompDO.getCompName(), e.getCompname()))
                        .findFirst().orElse(null);

                if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                    continue;
                }

                ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(
                        ICompParseSchemaStrategy.class, childConfig.getStrategy());
                if (schemaStrategy == null) {
                    continue;
                }

                // 从复杂值中获取子字段
                Field childField = complexValue.getValueField(getFieldId(childConfig, compDO));
                if (childField == null) {
                    continue;
                }

                // 解析子字段的值到子组件
                SchemaParseContext childContext = context.clone();
                schemaStrategy.parseSchema(childrenCompDO, childField, param, childConfig, childContext);
            }
        }
    }

    @Override
    protected void renderSchemaFieldValue(MultiComplexField field, ParentCompDO<AbstractCompDO> compDO,
                                        CompExtParam param, AdapterCompConfig compConfig,
                                        SchemaParseContext context) {
        // 如果没有值或者组件不存在则返回
        if (compDO == null || CollectionUtils.isEmpty(compDO.getChildComps())) {
            return;
        }

        // 检查子组件是否有值
        boolean hasValue = compDO.getChildComps().stream()
                .anyMatch(childComp -> !childComp.isValueEmpty());
        
        if (!hasValue) {
            return;
        }

        List<ComplexValue> valueList = Lists.newArrayList();

        // 创建一个ComplexValue来包含所有子组件的值
        ComplexValue complexValue = new ComplexValue();

        for (AbstractCompDO childereCompDO : compDO.getChildComps()) {
            AdapterCompConfig childConfig = compConfig.getChildren().stream()
                    .filter(e -> StringUtils.equals(childereCompDO.getCompName(), e.getCompname()))
                    .findFirst().orElse(null);
            
            if (childConfig == null || StringUtils.isBlank(childConfig.getStrategy())) {
                continue;
            }
            
            ICompParseSchemaStrategy schemaStrategy = ExtensionRouterFatory.getPlugin(
                    ICompParseSchemaStrategy.class, childConfig.getStrategy());
            if (schemaStrategy == null) {
                continue;
            }
            
            // 为子组件创建只显示值的schema
            SchemaParseContext cloneConfig = context.clone();
            cloneConfig.setOnlyShowValue();
            Field childField = schemaStrategy.transferSchema(childereCompDO, param, childConfig, cloneConfig);
            
            if (childField != null) {
                complexValue.put(childField);
            }
        }
        
        valueList.add(complexValue);
        
        // 将复杂值添加到字段中
        field.setComplexValues(valueList);
    }

    @Override
    public String getName() {
        return "diveEquipmentInfoSchemaStrategy";
    }
}