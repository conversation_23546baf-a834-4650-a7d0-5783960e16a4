package com.taobao.gpf.domain.component.images;

import com.alibaba.gpf.sdk.request.PublishRequest;
import com.alibaba.gpf.shared.extpoint.async.BaseRenderAsyncOpt;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: shushen
 * @date: 2020/6/5
 * @time: 下午7:28
 * @version:1.0
 */
@Slf4j
public class ImagesAsyncOpt extends BaseRenderAsyncOpt {

    @Override
    public List<String> getRunComp(PublishRequest publishRequest) {
        return Lists.newArrayList("images", "imageNoteSetting");
    }

    @Override
    public List<String> getOuterComp(PublishRequest publishRequest) {
        return Lists.newArrayList("images", "imageNoteSetting");
    }

    @Override
    public String getName() {
        return "imagesAsyncOpt";
    }

}