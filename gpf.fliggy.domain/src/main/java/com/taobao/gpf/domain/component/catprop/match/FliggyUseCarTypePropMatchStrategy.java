package com.taobao.gpf.domain.component.catprop.match;

import javax.annotation.Resource;

import com.alibaba.gpf.base.publish.sdk.extpoint.ICompTemplateMatchStrategy;
import com.alibaba.gpf.base.publish.sdk.param.MatchParam;
import com.alibaba.gpf.business.domain.component.catprop.materialProp.TaoMaterialPropertyUtil;
import com.alibaba.gpf.business.domain.util.ForestFeatureUtil;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyDO;
import com.taobao.gpf.domain.config.TravelSellConfig;

/**
 * @description:用车类型：https://tyler.alibaba-inc.com/stdcat/index.htm?cnId=1
 * <AUTHOR>
 */
public class FliggyUseCarTypePropMatchStrategy implements ICompTemplateMatchStrategy<StdCategoryPropertyDO> {


    @Resource
    private TravelSellConfig travelSellConfig;



    @Override
    public boolean match(MatchParam param, StdCategoryPropertyDO stdCategoryPropertyDO) {
        //TODO
        return stdCategoryPropertyDO.getPropertyId() == travelSellConfig.getCarPackageTypePid();
    }

    @Override
    public String getName() {
        return "fliggyUseCarTypePropMatchStrategy";
    }
}