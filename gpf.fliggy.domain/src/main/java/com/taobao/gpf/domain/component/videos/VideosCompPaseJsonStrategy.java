package com.taobao.gpf.domain.component.videos;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.gpf.business.domain.component.video.VideoDO;
import com.alibaba.gpf.business.domain.constant.BaseInfoCompConstant;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.shared.componet.BaseCompParseJsonStrategy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;


/**
 * @description: 视频组件parse策略 ，
 * 没有使用sdk中的，是因为sdk中的long videoId = jsonNode1.get("videoId").asLong(0) 有空指针
 * 根本原因是由于页面上的商品视频从无到有时
 * <AUTHOR>
 */

public class VideosCompPaseJsonStrategy extends BaseCompParseJsonStrategy<BasicCompDO<List<VideoDO>>> {

    @Override
    public String getName() {
        return "videosCompPaseJsonStrategy";
    }

    @Override
    public void parseJson(BasicCompDO<List<VideoDO>> compDO, JsonNode node, CompExtParam param) {
        if (!node.isArray() || node.size() <= 0) {
            return;
        }
        List<VideoDO> videoDOList = new ArrayList<>();
        VideoDO videoDO;
        for (JsonNode jsonNode1 : node) {
            if(jsonNode1.isNull()){
                continue;
            }
            videoDO = new VideoDO();
            JsonNode videoIdNode = jsonNode1.get("videoId");
            if(null == videoIdNode || videoIdNode.isNull()){
                continue;
            }
            long videoId = videoIdNode.asLong(0);
            videoDO.setVideoId(videoId);
            videoDOList.add(videoDO);
        }
        compDO.setValue(videoDOList);
    }

    @Override
    protected void renderValue(BasicCompDO<List<VideoDO>> compDO, ObjectNode objectNode, CompExtParam param) {
        ArrayNode list = objectNode.withArray("value");
        if (compDO.getValue() != null) {
            for (VideoDO video : compDO.getValue()) {
                ObjectNode obj = list.addObject();
                // 前端浏览器有个bug, 当long型数据值到达16个9之后, 会被变成一个莫名其妙的值, 这里用string
                obj.put("videoId", String.valueOf(video.getVideoId()));
                obj.put("videoPicUrl", video.getVideoPicUrl());
            }
        }
        //咨询了彼洋，前端暂未用到 isOrderVideo 这个参数，先去掉 2017年07月17日 苍茗
        //node.put("isOrderVideo", compDO.getIsOrder());
    }

}
