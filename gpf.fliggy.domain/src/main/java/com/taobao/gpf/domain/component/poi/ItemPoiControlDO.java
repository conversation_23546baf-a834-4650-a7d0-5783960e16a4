package com.taobao.gpf.domain.component.poi;

import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.usa.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by maoxinming on 2019/4/19.
 */
public class ItemPoiControlDO extends ControlDO {

    @Getter
    @Setter
    private String btnName ;

    @Getter @Setter
    private Integer sceneId ;

    @Getter @Setter
    private Integer poiCount ;

    @Getter @Setter
    private String searchUrl ;

    @Getter @Setter
    private String applyLink ;

    @Getter @Setter
    private String updateUrl ;


    @Override
    public void toJsonNode(ObjectNode objectNode){
        if(null == objectNode){
            objectNode = JacksonUtil.createObjectNode();
        }
        objectNode.put("applyUrl","<a href=\"//poiseller.fliggy.com/seller/page/task/add?firstCategory=5\">申请新的POI地点</a>");
        objectNode.put("updateUrl","//poiseller.fliggy.com/seller/page/poi/correct");
        if(StringUtils.isNotEmpty(btnName)){
            objectNode.put("btnName",btnName);
        }
        if(null != sceneId && sceneId>0){
            objectNode.put("sceneId",sceneId);
        }
        if(null != poiCount && poiCount>0){
            objectNode.put("poiCount",poiCount);
        }
        if(StringUtils.isNotEmpty(searchUrl)){
            objectNode.put("searchUrl",searchUrl);
        }
    }


}
