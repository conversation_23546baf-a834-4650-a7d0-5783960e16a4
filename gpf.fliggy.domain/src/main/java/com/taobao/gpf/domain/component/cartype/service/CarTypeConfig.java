package com.taobao.gpf.domain.component.cartype.service;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;


@Data
public class CarTypeConfig {
    /**
     * 车型id
     */
    Integer id;
    /**
     * 车型名称
     */
    String name;
    /**
     * 车型图片url
     */
    String imgUrl;

    /**
     * 发布端排序，这个字段不序列化保存到商品
     */
    @JSONField(serialize = false)
    Integer order;
}

