package com.taobao.gpf.domain.component.images;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.config.CompConfig;
import com.alibaba.gpf.sdk.dataobject.ImageValueDO;
import com.alibaba.gpf.sdk.param.CompExtParam;

import com.alitrip.travel.travelitems.model.TravelImage;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @description:
 * <AUTHOR>
 */

public class ImagesStoreStrategy<T> extends BaseFliggyCompStoreStrategy<BasicCompDO<List<ImageValueDO>>> {

    public static final String FLIGGY_IMAGES = "fliggy-images";

    @Override
    public String getName() {
        return FLIGGY_IMAGES;
    }

    @Override
    public void parseStore(BasicCompDO<List<ImageValueDO>> compDO, TravelItemStoreDO travelItemStoreDO, CompExtParam param) {
        if(CollectionUtils.isEmpty(travelItemStoreDO.getImages())){
            return;
        }
        List<ImageValueDO> imageDOList =  travelItemStoreDO.getImages()
            .stream()
            .filter(Objects::nonNull)
            .map(travelImage -> new ImageValueDO(travelImage.getUrl()))
            .collect(Collectors.toList());
        compDO.setValue(imageDOList);
    }

    @Override
    public void transferStore(BasicCompDO<List<ImageValueDO>>  compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if(null == compDO || CollectionUtils.isEmpty(compDO.getValue())){
            return;
        }
        List<TravelImage> travelImages =  compDO.getValue()
            .stream()
            .filter(Objects::nonNull)
            .map(imageDO -> {
                TravelImage travelImage = new TravelImage();
                travelImage.setUrl(imageDO.getUrl());
                return travelImage;
            }).collect(Collectors.toList());
        storeDO.setImages(travelImages);
    }

    @Override
    public void cleanStore(CompConfig compDO, TravelItemStoreDO storeDO, CompExtParam param) {

    }
}
