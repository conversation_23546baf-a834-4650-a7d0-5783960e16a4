package com.taobao.gpf.domain.identity;

import com.taobao.gpf.domain.config.TravelSellConfig;
import lombok.extern.slf4j.Slf4j;


/**
 * @description:充值业务身份识别器
 * <AUTHOR>
 */
@Slf4j
public class RechargeItemIdentity extends AbstractAppBizIdentity {

    @Override
    protected boolean doJudge(TravelSellConfig travelSellConfig, int categoryId) {
        return travelSellConfig.isRecharge(categoryId);
    }
}
