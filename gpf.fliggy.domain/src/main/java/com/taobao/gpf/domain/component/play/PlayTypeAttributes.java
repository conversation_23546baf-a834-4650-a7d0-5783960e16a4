package com.taobao.gpf.domain.component.play;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> 2024-08-13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlayTypeAttributes implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 属性名称
     */
    private String name;

    /**
     * 属性类型：1-单选框，2-复选框，3-文本框文字，4-文本框数字
     */
    private Integer type;

    /**
     * 是否必选
     */
    private Integer required;

    /**
     * 取值范围(type为4下)
     */
    private String valueScope;

    /**
     * 是否支持其他
     */
    private Integer canHaveCustomOption;

    /**
     * 可选数量(type为2下)
     */
    private String optionalQuantity;

    /**
     * 属性值
     */
    private String value;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 玩法类型
     */
    private Integer hasSpecialActivity;

    /**
     * 状态
     */
    private Integer status;


}