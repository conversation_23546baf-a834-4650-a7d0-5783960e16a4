package com.taobao.gpf.domain.component.flmap;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.shared.componet.StandardCompParseJsonStrategy;
import com.fasterxml.jackson.databind.JsonNode;
import com.taobao.gpf.domain.model.pagemodel.FliggyMapValueModel;

import java.util.List;

public class FliggyMapJsonStrategy extends StandardCompParseJsonStrategy<List<FliggyMapValueModel>> {
    
    @Override
    public void parseJson(BasicCompDO<List<FliggyMapValueModel>> compDO, JsonNode jsonNode, CompExtParam compExtParam) {
        if (!jsonNode.isNull() && jsonNode.isArray()) {
            List<FliggyMapValueModel> valueModelList = JacksonUtil.getListForJsonNode(jsonNode, FliggyMapValueModel.class);
            compDO.setValue(valueModelList);
        }
    }

    @Override
    public String getName() {
        return "fliggyMapJsonStrategy";
    }
}
