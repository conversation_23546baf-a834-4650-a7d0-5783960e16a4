package com.taobao.gpf.domain.identity;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * 境外交通业务身份识别
 * @Author: boyu.gjj
 * @Date: 2024-09-13
 */
public class TrafficAbroadItemIdentity extends BaseAppBizIdentity {
    @Override
    public boolean isFitByCustom(IdentityParam identityParam) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject) identityParam.getWro()).getCategory();
        Long categoryId = (long) stdCategoryDO.getCategoryId();
        return  FliggySwitchConfig.TRAFFIC_ABROAD_CAT.contains(categoryId);
    }
}
