package com.taobao.gpf.domain.component.id;

import javax.annotation.Resource;

import com.alibaba.gpf.business.domain.util.ParamUtil;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.config.CompConfig;
import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.sdk.param.SystemParam;
import com.alibaba.gpf.shared.extpoint.build.BaseBuildModelCompStrategy;

import com.taobao.gpf.domain.repository.ItemServiceRepo;
import com.taobao.uic.common.domain.BaseUserDO;

/**
 * <AUTHOR>
 * @Description:
 * @date Created in 2019-04-17 14:13
 */
public class ItemIdBuildStrategy extends BaseBuildModelCompStrategy {

    @Resource
    private ItemServiceRepo itemServiceRepo;

    @Override
    public String getName() {
        return "itemIdBuildStrategy";
    }

    @Override
    public AbstractCompDO buildCompDO(CompConfig cc, Param param) throws Exception {
        AbstractCompDO compDO = super.buildCompDO(cc, param);
        Long id = buildItemId(param);
        compDO.setValue(id);
        return compDO;
    }


    /**
     * 构建商品id
     * 1.优先 从request获取
     * 2.如果没有获取到且 开启了预生成模式
     *
     * @param
     * @param param
     * @return
     */
    private Long buildItemId(Param param) {

        Long id = param.getReq().getSystemParam().getLong(SystemParam.PRIMARY_ID);
        if(id != null && id > 0L){
            return id;
        }
        BaseUserDO baseUserDO = ParamUtil.getUserDO(param);
        id = itemServiceRepo.generateAuctionId(baseUserDO);
        return id;
    }
}
