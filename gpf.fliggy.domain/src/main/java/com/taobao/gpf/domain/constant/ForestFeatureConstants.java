package com.taobao.gpf.domain.constant;

/**
 * Forest中使用到的Feature的常量类
 * Created by linrui.lql on 27/12/16.
 */
public interface ForestFeatureConstants {

    String VALUE_ENABLED = "1";                       //当feature的值是true, false时, feature中使用 1 来表示

    String MULTI_SELECT_MAX_NUM = "multi_select_max_num";               //属性限制最多可选属性值的数量
    String SKU_PRICE_LIMIT = "sku_price_limit";                         //sku的价格区间
    String SALE_PROP_WITHOUR_PAIR = "salewithoutpair";                  //判断此类目下的销售属性是否需要成对出现（就是运营设置的销售属性要么全选，要么都不选）
    String BARCODE_CATEGORY_KEY = "open_barcode";                       //类目 支持条码
    String CUSTOM_MADE_KEY = "custom_design_flag";              //是否有定制市场许可
    String CAT_MARGIN_KEY = "pcs_limit";                        //类目限制保证金的key
    String prepay_cat_key = "prepay_cat";                       //消保类目
    String prepay_cat2_key = "prepay_2";                        //二手类目强制消保
    String PERMIT_CAT_KEY = "pub_permission";                   //类目对C商家屏蔽：c卖家不能看到的类目

    String NO_VIDEO_AS_PIC = "no_video_as_main_pic";            //类目不支持视频作为主图

    String O2OMS_CAT_KEY = "o2o_step_cfg";                      //O2O分阶段类目 标
    String CUSTOM_SALE_PROP = "udsaleprop";                    //自定义销售属性类目标

    String MULTI_CASCADE_CATEGORY = "multi_cascade_properties"; //是否有汽车级联属性
    String CASCADE_PROPS_ITEM_FEATURE_KEY = "cascade_properties";   //是否有汽车级联属性的商品feature key

    //tags
    String CAT_FEATUES_TAGS = "tags";

    /**
     * 无线描述不支持视频的类目标(黑名单)
     */
    String WIRELESS_VIDEO_NOT_SUPPORTED_CATEGORY = "multimedia_anquan";
    String WIRELESS_VIDEO_NOT_SUPPORT_VALUE = "1";

    /**
     * O2O分阶段类目 是否限制打标卖家使用
     */
    String O2OMS_CAT_NEED_CHECK_USER_TAG = "3";

    /**
     * 3c类目features的值
     */
    String VerticalMacket_Value = "3";

    /**
     * [多选]类目冻结
     */
    String FREEZE = "freeze";
    String FREEZE_VALUE_EDIT = "0";
    String FREEZE_VALUE_SHELF = "4";
    String FREEZE_VALUE_PUBLISH = "5";

    /**
     * 类目限制发布数量标
     */
    String CAT_LIMIT_QUANTITY_kEY = "cat_limit_q";
    String CAT_LIMIT_QUANTITY_VALUE = "1";

    /**
     * 新消保7天无理由
     */
    String NEW_PREPAY_CAT_KEY = "new_prepay";
    String NEW_PREPAY_CAT_VALUE_Y = "y";
    String NEW_PREPAY_CAT_VALUE_N = "n";
    String NEW_PREPAY_CAT_VALUE_R = "r";

    /**
     * 类目是否被锁定，不能变更类目
     */
    String LOCK_CATEGORY_FEATURE_KEY = "lockCat";
    String NOT_IN_NOT_OUT = "1";
    String CAN_IN_NOT_OUT = "2";
    String NOT_IN_CAN_OUT = "3";

    /**
     * [多选]商品关联属性的类目特征key
     */
    String ITEM_ASSOCIATION = "association";

    //关联本地商户，集市卖家可输入类目标
    String ITEM_ASSOCIATION_VALUE_TAOBAO_OPTIONAL = "1";
    //关联本地商户，相关淘宝卖家必填类目标
    String ITEM_ASSOCIATION_VALUE_TAOBAO_REQUIRED = "2";
    //关联本地商户，天猫卖家可输入类目标
    String ITEM_ASSOCIATION_VALUE_TIANMAO_OPTIONAL = "3";
    //关联本地商户，相关天猫卖家必填类目标
    String ITEM_ASSOCIATION_VALUE_TIANMAO_REQUIRED = "4";
    //关联本地商户，天猫白名单卖家
    String ITEM_ASSOCIATION_VALUE_TIANMAO_WHITELIST = "5";
    //关联本地商户，淘宝白名单卖家可输入类目标
    String ITEM_ASSOCIATION_VALUE_TAOBAO_WHITELIST = "6";

    /**
     * [单选]商品关联属性的类目特征key
     */
    String SECOND = "h_new_second";                     //只能发布2手的类目
    String SECOND_VALUE_NEW = "new";                    //只能发布全新
    String SECOND_VALUE_SECOND = "second";              //只能发布二手

    /**
     * [单选]类目启用预售限制规则
     */
    String LIMIT_PAYMENT = "yushou_2015";
    String LIMIT_PAYMENT_VALUE_FRESH = "1";           //强制必选_如生鲜类目 标值
    String LIMIT_PAYMENT_VALUE_ANIME = "2";           //发布可选_如动漫类 标值

    String QUALIFICATION_REQUIRED_FEATURE_KEY = "zizhi_force";            // 商品资质是否必填的标

    /**
     * 该类目商品发布时，"无线短标题"字段是否可见
     */
    String SHORT_TITLE_KEY = "short_title";
    String SHORT_TITLE_VALUE_ENABLED = "1";
    /**
     * 忽略类目商家授权的类目标记。
     */
    String IGNORE_GRANT_FEATURE_KEY = "ignore_grant";
    String IGNORE_GRANT_CAT_VALUE = "cat";

    /**
     * 新网厅合约业务类目标标记
     */
    String WTT_ITEM_CATEGORY_FEATURE_KEY = "heyueshangpin_leimu";
    String WTT_ITEM_CATEGORY_FEATURE_VALUE = "1";


    /**
     * 类目限制发布数量标
     */
    String CAT_LIMIT_QUANTITY_KEY = "cat_limit_q";


    String PREPAY_CAT_KEY = "prepay_cat";                       //消保类目
    String PREPAY_CAT2_KEY = "prepay_2";

    /**
     * 消费卡券（超市卡）
     */
   String  CARD_ENTER_QUALITY_FEATURE_KEY ="card_enter_quality";

    /**
     * 允许发布套装商品的类目标记
     */
    String COMMON_SUITE_CATEGORY_KEY = "allow_pub_suite";
    String COMMON_SUITE_CATEGORY_VALUE = "1";

    /**
     * 允许发布darwin组合商品的类目标记
     */
    String COMMON_DARWIN_SUITE_CATEGORY_KEY = "allow_pub_darwin_suite";
    String COMMON_DARWIN_SUITE_CATEGORY_VALUE = "1";
}
