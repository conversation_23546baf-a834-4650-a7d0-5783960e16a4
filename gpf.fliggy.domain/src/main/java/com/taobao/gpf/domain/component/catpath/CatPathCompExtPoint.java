package com.taobao.gpf.domain.component.catpath;

import com.alibaba.gpf.base.publish.domain.component.catpath.CatPathControlDO;
import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.annotation.bind.User;
import com.alibaba.gpf.sdk.annotation.ext.ProcessForRender;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.exception.GpfException;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.SwitchCatWhiteListConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.helper.TripItemInfoHelper;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.uic.common.domain.BaseUserDO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @description:
 * <AUTHOR>
 */
@Slf4j
public class CatPathCompExtPoint implements IInjectExtension {

    @Resource
    private TripItemInfoHelper tripItemInfoHelper;

    @Resource
    private SwitchCatWhiteListConfig switchCatWhiteListConfig;


    @ProcessForRender(key = "catPath-fliggy-CanChangeCategoryProp", desc = "是否允许切换类目")
    public void setCanChangeCategoryProp(CompExtParam param, BasicCompDO<String> compDO, @Category StdCategoryDO category, @User BaseUserDO baseUserDO) {
        if(null == compDO.getControlDO()){
            compDO.setControlDO(new CatPathControlDO());
        }
        //特定商家有权限，
        boolean hasPermission = switchCatWhiteListConfig.hasPermissions(baseUserDO.getUserId());
        //而且99兑换商品不可切换类目
        if(hasPermission && tripItemInfoHelper.isCouponItem(param)){
            hasPermission = false;
        }
        ((CatPathControlDO)compDO.getControlDO()).setCanChangeCategory(hasPermission);
        //如果是切换类目场景
        if(FliggyParamUtil.isChangeCatRender(param)){
            ((CatPathControlDO)compDO.getControlDO()).setCanChangeCategory(false);
            //如果没有权限的情况下，通过篡切参数，则需要报错
            if(!hasPermission){
                throw new GpfException(FliggyErrorEnum.SYS_FORBIDDEN_SWITCHCATE.getErrorCode());
            }
        }
    }
}
