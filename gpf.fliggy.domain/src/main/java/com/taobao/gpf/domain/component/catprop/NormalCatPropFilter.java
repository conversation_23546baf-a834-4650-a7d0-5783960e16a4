package com.taobao.gpf.domain.component.catprop;

import com.alibaba.gpf.base.publish.sdk.extpoint.catprop.ICatPropFilter;
import com.alibaba.gpf.base.publish.sdk.param.CatPropParam;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.utils.StdCategoryPropertyUtils;

/**
 * @description:
 * <AUTHOR>
 */

public class NormalCatPropFilter implements ICatPropFilter<StdCategoryDO,StdCategoryPropertyDO> {

    @Override
    public boolean filtered(StdCategoryPropertyDO stdCategoryPropertyDO, CatPropParam param) {
        /**
         * 匹配的不过滤，则返回false，
         * 不匹配的过滤掉，则返回true
         */
        return !match(stdCategoryPropertyDO);
    }

    /**
     * 是否匹配
     * @param propertyDO
     * @return true:匹配,false：不匹配
     */
    public static boolean match(StdCategoryPropertyDO propertyDO){
        return StdCategoryPropertyUtils.isItemProp(propertyDO) || StdCategoryPropertyUtils.isBindProp(propertyDO);
    }


    @Override
    public String getName() {
        return "normalCatPropFilter";
    }
}
