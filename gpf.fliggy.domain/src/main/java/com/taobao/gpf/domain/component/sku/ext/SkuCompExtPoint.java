package com.taobao.gpf.domain.component.sku.ext;

import com.alibaba.gpf.base.publish.domain.component.sku.BasicSkuTableRowDO;
import com.alibaba.gpf.base.publish.domain.component.sku.SkuTableCompDO;
import com.alibaba.gpf.base.publish.sdk.compdo.CatPropCompDO;
import com.alibaba.gpf.base.publish.sdk.model.pagemodel.SkuTablePageModel;
import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.TableColumnCompDO;
import com.alibaba.gpf.sdk.dataobject.MoneyValueDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.alitrip.travel.common.util.QuaDateUtils;
import com.alitrip.travel.travelitems.model.TravelItem;
import com.alitrip.travel.travelitems.model.TravelItemSku;
import com.fliggy.travel.data.platform.client.basedata.rule.params.RuleConstants;
import com.fliggy.travel.data.platform.client.basedata.rule.params.RuleCotext;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.common.util.CommonStringUtil;
import com.taobao.gpf.domain.component.calendarprice.CalendarPriceDO;
import com.taobao.gpf.domain.component.saleproperty.SalePropUtil;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.constant.SaleWayConstant;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.helper.ErrorCategoryHelper;
import com.taobao.gpf.domain.helper.lowpricecontrol.ItemLowPriceControlHelper;
import com.taobao.gpf.domain.model.ding.PriceChangeRangeDingMsgImpl;
import com.taobao.gpf.domain.repository.TravelRuleServiceRepo;
import com.taobao.gpf.domain.utils.AddPriceModelUtil;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.LowPriceUtils;
import com.taobao.gpf.domain.utils.common.MoneyUtil;
import com.taobao.gpf.domain.utils.ding.DingMsgUtils;
import com.taobao.travel.common.TravelResult;
import com.taobao.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.testng.collections.Maps;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;

import static com.taobao.gpf.domain.constant.IcmpGlobalConstants.COPY_ID;

/**
 * @description:sku组件的扩展点
 * <AUTHOR>
 */
@Slf4j
public class SkuCompExtPoint implements IInjectExtension {


    @Resource
    private TravelRuleServiceRepo travelRuleServiceRepo ;
    @Resource
    private ItemLowPriceControlHelper itemLowPriceControlHelper;
    @Resource
    private ErrorCategoryHelper errorCategoryHelper;

    @PrepareForRender(key = "sku-attributes-fliggy-readonly-setting", desc = "参加大促的商品需要锁定销售属性信息")
    public void skuAttributesReadonlySetting(CompExtParam param, AbstractCompDO compDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        if (Objects.nonNull(param.getReq().getExtParam().get(COPY_ID))) {
            return;
        }
        ((BasicPageModel)compDO.getPagemodel()).setReadonly(!SalePropUtil.allowUpdateSaleInfo(param));
    }

    @PrepareForRender(key = "sku-attributes-fliggy-readonly-setting-with-cat", desc = "参加大促的商品需要锁定销售属性信息")
    public void skuAttributesReadonlySettingWithCat(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if (!FliggySwitchConfig.BIG_PROMOTION_LOCK_COMP_CATEGORY.contains((long)categoryDO.getCategoryId())) {
            return;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        if (Objects.nonNull(param.getReq().getExtParam().get(COPY_ID))) {
            return;
        }
        ((BasicPageModel)compDO.getPagemodel()).setReadonly(!SalePropUtil.allowUpdateSaleInfo(param));
    }


    @Deprecated
    @PrepareForRender(key = "sku-fliggy-hideAction", desc = "sku组件在参加大促的商品时需要锁定")
    public void skuHideAction(CompExtParam param, AbstractCompDO compDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        if (Objects.nonNull(param.getReq().getExtParam().get(COPY_ID))) {
            return;
        }
        ((SkuTablePageModel)compDO.getPagemodel()).setHideAction(!SalePropUtil.allowUpdateSaleInfo(param));
    }

    @Check(key = "check-tripcar-lowprice")
    public CheckResult checkTripCarLowPrice(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO category){
        CheckResult checkResult = new CheckResult() ;
        long userId = FliggyParamUtil.getUserId(param) ;
        if (FliggySwitchConfig.testSellerIdInfo.contains(String.valueOf(userId))) {
            return checkResult;
        }
        if (!FliggySwitchConfig.lowPriceControlCatIds.contains((long) category.getCategoryId())) {
            return checkResult;
        }
        //获取目的地信息
        List<TextValueModel> fliggyDestValues = (List<TextValueModel>)(param.getDependCompDO("tripCarDestProp").getValue());
        //获取商品属性值
        CatPropCompDO catPropCompDO = (CatPropCompDO)param.getDependCompDO("catProp");
        RuleCotext ruleCotext  = RuleCotext.builder()
                .ruleScene(String.valueOf(category.getCategoryId()))
                .sellerNick(FliggyParamUtil.getUserDO(param).getNick())
                .toLocation(LowPriceUtils.toString(fliggyDestValues))
                .carType(LowPriceUtils.getPVText(catPropCompDO,"fliggyUseCarTypeProp"))
                .vehicleType(LowPriceUtils.getPVText(catPropCompDO,"fliggyVehicleTypeProp"))
                .tripDays(LowPriceUtils.getPVText(catPropCompDO,"fliggyTripDayPropProp"))
                .build();

        RuleCotext matchedRuleCotext = itemLowPriceControlHelper.matchLowPrice(category.getCategoryId(), ruleCotext);
        if(matchedRuleCotext == null){
            return checkResult;
        }

        List<BasicSkuTableRowDO> list = (List<BasicSkuTableRowDO>)compDO.getValue() ;
        BasicCompDO<Integer> saleWayCompDo = (BasicCompDO)param.getDependCompDO("saleWay");
        //如果不是普通库存
        if(null == saleWayCompDo || null == saleWayCompDo.getValue()){
            return checkResult;
        }
        //如果不是普通库存
        TreeSet<Long> prices = new TreeSet<Long>() ;
        if(1 == saleWayCompDo.getValue()){
            for(BasicSkuTableRowDO basicSkuTableRowDO : list){
                MoneyValueDO price = (MoneyValueDO)basicSkuTableRowDO.getData("commonStockPrice");
                prices.add(price.getCent());
            }
        }else{
            for(BasicSkuTableRowDO basicSkuTableRowDO : list){
                if (saleWayCompDo.getValue() == 6) {
                    //二次预约
                    MoneyValueDO commonStockPrice = (MoneyValueDO)basicSkuTableRowDO.getData("commonStockPrice");
                    if (commonStockPrice != null) {
                        prices.add(commonStockPrice.getCent());
                    }
                } else if(saleWayCompDo.getValue() == 5) {
                    //日历
                    CalendarPriceDO calendarPriceDO = (CalendarPriceDO)basicSkuTableRowDO.getData("calendarPrice");
                    for(CalendarPriceDO.Diff diff : calendarPriceDO.getDiff()){
                        if(null != diff.getPrice()){
                            prices.add(CommonStringUtil.Long2CentOfMoney(diff.getPrice()));
                        }
                    }
                }

            }
        }
        if(prices.size() == 0){
            return checkResult ;
        }

        Integer skuPrice = Optional.ofNullable(matchedRuleCotext.getParam().get(RuleConstants.SKU_PRICE))
                .map(Object::toString)
                .filter(NumberUtils::isNumber)
                .map(MoneyUtil::string2CentOfMoney)
                .map(Long::intValue)
                .orElse(null);

        if(itemLowPriceControlHelper.comparePrice(prices.first(), skuPrice)){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_ITEM_PRICE_NOT_ACCORD_WITH_PRICE_PRICE.getErrorCode());
        }

        return checkResult ;
    }

    @Check(key="check-cruise-ticket-lowprice")
    public CheckResult checkCruiseTicketLowPrice(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO categoryDO){
        CheckResult checkResult = new CheckResult() ;
        if(categoryDO.getCategoryId() != FliggySwitchConfig.cruiseTicketCatid){
            return checkResult ;
        }

        long userId = FliggyParamUtil.getUserId(param) ;
        if (FliggySwitchConfig.testSellerIdInfo.contains(String.valueOf(userId))) {
            return checkResult;
        }
        if (!FliggySwitchConfig.checkLowPrice) {
            return checkResult;
        }
        //获取目的地信息
        List<TextValueModel> fliggyDestValues = (List<TextValueModel>)(param.getDependCompDO("fliggyDestProp").getValue());
        RuleCotext ruleCotext  = RuleCotext.builder()
                .ruleScene(String.valueOf(categoryDO.getCategoryId()))
                .sellerNick(FliggyParamUtil.getUserDO(param).getNick())
                .toLocation(LowPriceUtils.toString(fliggyDestValues))
                .build() ;
        List<BasicSkuTableRowDO> list = (List<BasicSkuTableRowDO>)compDO.getValue() ;
        BasicCompDO<Integer> saleWayCompDo = (BasicCompDO)param.getDependCompDO("saleWay");
        if(null == saleWayCompDo || null == saleWayCompDo.getValue()){
            return checkResult;
        }
        TreeSet<Long> prices = new TreeSet<Long>() ;
        if(1 == saleWayCompDo.getValue()){
            for(BasicSkuTableRowDO basicSkuTableRowDO : list){
                MoneyValueDO price = (MoneyValueDO)basicSkuTableRowDO.getData("commonStockPrice");
                prices.add(price.getCent());
            }
        }else{
            for(BasicSkuTableRowDO basicSkuTableRowDO : list){
                CalendarPriceDO calendarPriceDO = (CalendarPriceDO)basicSkuTableRowDO.getData("calendarPrice");
                for(CalendarPriceDO.Diff diff : calendarPriceDO.getDiff()){
                    if(null != diff.getPrice()){
                        prices.add(CommonStringUtil.Long2CentOfMoney(diff.getPrice()));
                    }
                }
            }
        }
        if(prices.size() == 0){
            return checkResult ;
        }
        ruleCotext.set(RuleConstants.SKU_PRICE,prices.first());
        TravelResult<Boolean> result = travelRuleServiceRepo.exeRule(ruleCotext);
        if(null != result && result.isSuccess() && result.getModule()){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_ITEM_PRICE_NOT_ACCORD_WITH_PRICE_PRICE.getErrorCode());
        }

        return checkResult ;
    }

    @Check(key="check-general-lowprice")
    public CheckResult checkLowPrice(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO category){
        CheckResult checkResult = new CheckResult();
        if(!FliggySwitchConfig.serviceRuleActiveSwitch){
            return checkResult;
        }
        long catId = FliggyParamUtil.getCategoryId(param);
        if(!FliggySwitchConfig.lowPriceControlCatIds.contains(catId)){
            return checkResult;
        }

        if(param.getReq().getPageParam() == null){
            return checkResult;
        }

        //获取目的地信息
        List<TextValueModel> fliggyDestValues = (List<TextValueModel>)(param.getDependCompDO("fliggyDestProp").getValue());

        String title = FliggyParamUtil.getParamData(param, "title");
        String desc = FliggyParamUtil.getParamData(param, "desc");
        String keyWord = title + "," + desc;
        RuleCotext ruleCotext  = RuleCotext.builder()
                .toLocation(LowPriceUtils.toString(fliggyDestValues))
                .keyWord(keyWord)
                .build();

        RuleCotext matchedRuleCotext = itemLowPriceControlHelper.matchLowPrice(catId, ruleCotext);
        if(matchedRuleCotext == null){
            return checkResult;
        }

        List<BasicSkuTableRowDO> list = (List<BasicSkuTableRowDO>)compDO.getValue() ;
        BasicCompDO<Integer> saleWayCompDo = (BasicCompDO)param.getDependCompDO("saleWay");
        if(null == saleWayCompDo || null == saleWayCompDo.getValue()){
            return checkResult;
        }
        TreeSet<Long> prices = new TreeSet<Long>() ;
        if(1 == saleWayCompDo.getValue()){
            for(BasicSkuTableRowDO basicSkuTableRowDO : list){
                MoneyValueDO price = (MoneyValueDO)basicSkuTableRowDO.getData("commonStockPrice");
                prices.add(price.getCent());
            }
        }else{
            for(BasicSkuTableRowDO basicSkuTableRowDO : list){
                CalendarPriceDO calendarPriceDO = (CalendarPriceDO)basicSkuTableRowDO.getData("calendarPrice");
                for(CalendarPriceDO.Diff diff : calendarPriceDO.getDiff()){
                    if(null != diff.getPrice()){
                        prices.add(CommonStringUtil.Long2CentOfMoney(diff.getPrice()));
                    }
                }
            }
        }
        if(prices.size() == 0){
            return checkResult ;
        }

        Integer skuPrice = Optional.ofNullable(matchedRuleCotext.getParam().get(RuleConstants.SKU_PRICE))
                .map(Object::toString)
                .filter(NumberUtils::isNumber)
                .map(MoneyUtil::string2CentOfMoney)
                .map(Long::intValue)
                .orElse(null);

        if(itemLowPriceControlHelper.comparePrice(prices.first(), skuPrice)){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_ITEM_PRICE_NOT_ACCORD_WITH_PRICE_PRICE.getErrorCode());
        }
        return checkResult;
    }

    /**
     * 价格变动校验
     * @param compDO
     * @param param
     * @param categoryDO
     * @return
     */
    @Check(key="check-general-price-change")
    public CheckResult checkGeneralPriceChange(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO categoryDO) {
        CheckResult checkResult = new CheckResult();
        long cateId = categoryDO.getCategoryId();
        if(!errorCategoryHelper.isNeedErrorCategoryCheck(cateId)){
            return checkResult;
        }
        BasicCompDO<Integer> saleWayCompDo = (BasicCompDO)param.getDependCompDO("saleWay");
        if(null == saleWayCompDo || null == saleWayCompDo.getValue()){
            return checkResult;
        }
        Integer saleWay = saleWayCompDo.getValue();
        if(saleWay == null || !saleWay.equals(SaleWayConstant.NORMAL_STOCK)){
            //当前需要校验的类目下只有普通库存
            return checkResult;
        }

        TravelItem travelItem = FliggyParamUtil.getDbTravelItemDO(param);
        if(travelItem == null){
            return checkResult;
        }
        List<BasicSkuTableRowDO> list = (List<BasicSkuTableRowDO>)compDO.getValue();
        if(CollectionUtil.isEmpty(travelItem.getSkuList()) || CollectionUtil.isEmpty(list)){
            return checkResult;
        }

        //根据skuId分别比对各自的价格变动
        Map<Long, Long> skuPriceMap = Maps.newHashMap();
        for (TravelItemSku travelItemSku : travelItem.getSkuList()) {
            skuPriceMap.put(travelItemSku.getSkuId(), travelItemSku.getPrice().longValue());
        }

        for(BasicSkuTableRowDO basicSkuTableRowDO : list){
            Long skuId = basicSkuTableRowDO.getSkuId();
            if(!skuPriceMap.containsKey(skuId)){
                continue;
            }
            MoneyValueDO price = (MoneyValueDO)basicSkuTableRowDO.getData("commonStockPrice");
            Long oldPrice = skuPriceMap.get(skuId);
            long newPrice = price.getCent();
            //降价幅度超过60%，则需要发送钉钉消息，0.4*oldPrice >= newPrice ==> 2*oldPrice >= 5*newPrice
            if((oldPrice<<1) >= (5*newPrice)){
                PriceChangeRangeDingMsgImpl dingMsg =
                        PriceChangeRangeDingMsgImpl.builder()
                                .catId(cateId)
                                .itemId(travelItem.getItemId())
                                .itemName(travelItem.getTitle())
                                .newPrice(newPrice)
                                .oldPrice(oldPrice)
                                .sellNick(FliggyParamUtil.getUserDO(param).getNick())
                                .time(QuaDateUtils.dateToString(Calendar.getInstance().getTime()))
                                .url(FliggySwitchConfig.publishItemUrl + "?itemId=" + travelItem.getItemId())
                                .build();
                DingMsgUtils.sendMarkDownMsgWithTemplate(dingMsg);
                return checkResult;
            }
        }
        return checkResult;
    }
}
