package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

import javax.annotation.Resource;
import java.util.Set;

public class DaytourItemIdentity extends BaseAppBizIdentity {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)param.getWro()).getCategory() ;
        //return travelSellConfig.getJingwaiZiyouxingCatId() == stdCategoryDO.getCategoryId();
        return FliggySwitchConfig.dayTourCatIds.contains(stdCategoryDO.getCategoryId());
    }
}
