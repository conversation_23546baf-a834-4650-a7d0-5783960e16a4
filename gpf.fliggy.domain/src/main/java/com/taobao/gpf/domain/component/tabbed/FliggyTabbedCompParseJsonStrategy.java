package com.taobao.gpf.domain.component.tabbed;

import com.alibaba.gpf.sdk.compdo.TabbedCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.componet.tabbed.TabbedCompParseJsonStrategy;

import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;

import static com.alibaba.gpf.sdk.constant.PageModelConstant.KEY_VALUE;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/07/18/下午3:09
 */
public class FliggyTabbedCompParseJsonStrategy extends TabbedCompParseJsonStrategy {

    @Override
    public String getName() {
        return "fliggyTabbedCompParseJsonStrategy";
    }

    @Override
    public void parseJson(TabbedCompDO compDO, JsonNode jsonNode, CompExtParam param) {
        super.parseJson(compDO, jsonNode, param);
        // 根据约定, 这里的value为选中的组件名
        String selectCompName = jsonNode.path(KEY_VALUE).textValue();
        if(StringUtils.isNotBlank(selectCompName)){
            compDO.setValue(selectCompName);
        }
    }
}
