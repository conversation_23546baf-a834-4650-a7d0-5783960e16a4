package com.taobao.gpf.domain.component.shenbi;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.compPlugin.ICompInitStrategy;

public class DescForShenbiMobileInitStrategy implements ICompInitStrategy<BasicCompDO<String>> {
    @Override
    public BasicCompDO<String> getInitCompDO() {
        BasicCompDO<String> compDO = new BasicCompDO<>();
        compDO.setControlDO(new DescForShenbiControlDO());
        return compDO;
    }


    @Override
    public DescForShenbiControlDO getInitControlDO() {
        return new DescForShenbiControlDO();
    }

    @Override
    public String getName() {
        return "descForShenbiMobileInitStrategy";
    }

}

