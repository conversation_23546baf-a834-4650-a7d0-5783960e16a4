package com.taobao.gpf.domain.component.play.async;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.sdk.request.PublishRequest;
import com.alibaba.gpf.shared.extpoint.async.BaseRenderAsyncOpt;
import com.fasterxml.jackson.databind.JsonNode;
import com.fliggy.travel.data.platform.client.basedata.constants.PlayRiskLevelEnum;
import com.fliggy.travel.data.platform.client.basedata.play.params.PlayVO;
import com.google.common.collect.Lists;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.component.insurance.InsuraceControlDO;
import com.taobao.gpf.domain.component.insurance.InsuranceValueDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.repository.PlayServiceRepo;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 玩法和保险关联异步渲染
 * @author: luoli
 * @create: 2019-08-15
 */
public class PlayRelateInsuranceAsyncQueryOpt extends BaseRenderAsyncOpt {
    private static String PLAYPROP_COMPNAME = "playProp";
    private static String INSURANCE_COMPNAME = "needInsuranceConcrete";
    private static String INSURANCE_CONCRETE_AUTO = "insuranceConcreteAuto";
    private static String DAYTOUR_TYPE = "dayTourType";

    @Resource
    private PlayServiceRepo playServiceRepo;

    @Override
    public List<String> getRunComp(PublishRequest req) {
        return Lists.newArrayList(PLAYPROP_COMPNAME,DAYTOUR_TYPE,INSURANCE_COMPNAME,INSURANCE_CONCRETE_AUTO, CompConstants.PLATFORM_INSURANCE_PRODUCT_INFO, CompConstants.DEST_COMPNAME, CompConstants.DEST_COMPNAME_V2);
    }

    @Override
    public List<String> getOuterComp(PublishRequest req) {
        return Lists.newArrayList(PLAYPROP_COMPNAME,INSURANCE_COMPNAME,INSURANCE_CONCRETE_AUTO);
    }

    @Override
    public String getName() {
        return "playRelateInsuranceAsyncQueryOpt";
    }

    @Override
    public void processRender(Param param) {
        if (!FliggySwitchConfig.playRelateInsurance){
            return;
        }
        //如果选择的玩法中含高危玩法，保险默认选择自动投保，且不可修改
        JsonNode node = (JsonNode)param.getReq().getPageParam().getData();
        JsonNode playArray = node.path("playProp");
        if (playArray == null) {
            return;
        }
        List<Long> playIdList = new ArrayList<>();
        if (playArray.isArray()) {
            for (JsonNode play : playArray) {
                playIdList.add(play.path("value").asLong());
            }
        }
        //解析模板值
        JsonNode insuranceArray = node.path("insuranceConcreteAuto");
        List<InsuranceValueDO> insuranceList = JSON.parseArray(insuranceArray.toString(),InsuranceValueDO.class);
        BasicCompDO<String> insuranceCompDO = (BasicCompDO<String>)param.getPublishDO().getCompContents().get(INSURANCE_COMPNAME);
        BasicCompDO<String> insuranceConcreteCompDO = (BasicCompDO<String>)param.getPublishDO().getCompContents().get(INSURANCE_CONCRETE_AUTO);
        //默认值可修改
        if (null == insuranceCompDO || null == insuranceConcreteCompDO){
            return;
        }
        ((BasicPageModel)insuranceCompDO.getPagemodel()).setReadonly(false);
        if (CollectionUtils.isNotEmpty(playIdList)){
            for (Long playId : playIdList){
                PlayVO playVO = playServiceRepo.getPlayVOById(Long.valueOf(playId));
                if (null == playVO){
                    continue;
                }
                /**
                 * 若为高风险:
                 * 1、保险自动设置为自动投保，且不可修改;
                 * 2、保险模板获取url新增高风险参数;
                 * 3、检查原有保险模板，去除需要屏蔽的保险
                 * 不再支持自动投保 https://aone.alibaba-inc.com/req/39757893
                 * 需要改为默认勾选人工投保，且不可修改
                 */
                if (PlayRiskLevelEnum.HIGH_RISK.getType().equals(playVO.getRiskLevel())){
                    insuranceCompDO.setValue("1");
                    ((BasicPageModel)insuranceCompDO.getPagemodel()).setReadonly(true);
                    ((BasicPageModel)insuranceCompDO.getPagemodel()).getInfo().addRight("<span style=\"color: #ff1818\">商品套餐中含有高危玩法项目，需给客人购买保险；</span><a target='blank' href='https://seller.fliggy.com/ruleDiff/seller/#/detail?id=11001340'>飞猪商家规则</a> <a target='blank' href='https://rule.fliggy.hk/search?&domainfield=319&ruleId=11005848&type=content'>飞猪国际商家</a>");
                    String templateUrl = ((InsuraceControlDO)insuranceConcreteCompDO.getControlDO()).getTemplateUrl();
                    templateUrl += "&highRisk=true";
                    ((InsuraceControlDO)insuranceConcreteCompDO.getControlDO()).setTemplateUrl(templateUrl);
                    if (CollectionUtils.isNotEmpty(insuranceList)){
                        //自动投保类型，设值
                        insuranceList.removeIf(insuranceValueDO -> FliggySwitchConfig.playInsuranceHideProNo
                            .contains(insuranceValueDO.getProductNo()));
                        ((InsuraceControlDO)insuranceConcreteCompDO.getControlDO()).setList(insuranceList);
                    }
                    return;
                }
            }
        }
    }
}
