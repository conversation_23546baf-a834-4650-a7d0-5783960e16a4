package com.taobao.gpf.domain.component.etc;

import com.alibaba.gpf.business.domain.component.etc.EtcCompParseJsonStrategy;
import com.alibaba.gpf.business.domain.component.etc.EtcControlDO;
import com.alibaba.gpf.business.domain.component.extractWay.DeliverWayEnum;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.taobao.gpf.domain.component.extractway.DeliverWayUtil;
import com.taobao.gpf.domain.constant.delivery.DeliveryWayConstants;

import java.util.Objects;

public class FliggyEtcCompParseJsonStrategy extends EtcCompParseJsonStrategy {

    @Override
    public void parseJson(BasicCompDO<String> compDO, JsonNode node, CompExtParam param) {

        if (!DeliverWayUtil.checkCompResult(param, DeliveryWayConstants.ECT_VALUE)) {
            return;
        }
        if(node instanceof TextNode){
            node = JacksonUtil.readTree(node.asText());
        }
        if (Objects.isNull(node)) {
            node = NullNode.getInstance();
        }
        super.parseJson(compDO, node, param);
        if (compDO.getControlDO() != null) {
            ((EtcControlDO)compDO.getControlDO()).setSelected(true);
            ((EtcControlDO)compDO.getControlDO()).setWay(DeliverWayEnum.ETC);
        }
    }

    @Override
    public JsonNode renderJson(BasicCompDO<String> compDO, CompExtParam param) {
        ObjectNode objectNode = (ObjectNode)super.renderJson(compDO,param);
        if(compDO.getValue() != null) {
            objectNode.put("value", compDO.getValue());
        }
        return objectNode;
    }

    @Override
    public String getName() {
        return "fliggyEtcCompJsonStrategy";
    }
}
