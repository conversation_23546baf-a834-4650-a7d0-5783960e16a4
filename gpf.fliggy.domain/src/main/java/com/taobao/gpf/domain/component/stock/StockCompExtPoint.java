package com.taobao.gpf.domain.component.stock;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.compdo.TableColumnCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.model.pagemodel.NumberPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.RuleModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.common.constants.CompConstants;
import com.taobao.gpf.domain.component.saleproperty.SalePropUtil;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.utils.DependCompoUtil;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * @description:普通库存组件的扩展点
 * <AUTHOR>
 */
@Slf4j
public class StockCompExtPoint implements IInjectExtension {

    @Check(key = "stock-fliggy-submit-datacheck")
    public CheckResult check(CompExtParam param,TableColumnCompDO<Long> compDO) {
        CheckResult checkResult = new CheckResult();
        BasicCompDO<Integer> saleWayCompDo = (BasicCompDO)param.getDependCompDO("saleWay");
        //如果不是普通库存
        if(null == saleWayCompDo || null == saleWayCompDo.getValue() || 1 != saleWayCompDo.getValue()){
            return checkResult;
        }
        final List<Long> value = compDO.getValue();
        if(CollectionUtils.isEmpty(value)){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_SKU_LACK_STOCK.getErrorCode());
            return checkResult;
        }
        if(value.contains(null)){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_SKU_LACK_STOCK.getErrorCode());
            return checkResult;
        }
        //如果在商品参加大促活动中导致库存被锁，又不能修改销售信息的情况下，允许库存值等于0(自然购买减库存导致的)，但不能小于0，
        //非大促商品必须是大于0
        boolean inLock = SalePropUtil.isInSalesPromotionActivity(param.getReq().isEdit(), FliggyParamUtil.getDbTravelItemDO(param));
        Optional<Long> errorValue = value.stream().filter(aLong -> null==aLong || (inLock?aLong<0:aLong<=0)).findFirst();
        if(errorValue.isPresent()){
            checkResult.addErrorCode(FliggyErrorEnum.CHK_SKU_LACK_STOCK.getErrorCode());
            return checkResult;
        }
        return checkResult;
    }

    @PrepareForRender(key="editable-for-lottery")
    public void editableForLottery(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO category) {
        if (param.getReq().isEdit()) {
            if (DependCompoUtil.isLotteryType(param)) {
                BasicPageModel basicPageModel = (BasicPageModel) compDO.getPagemodel();
                basicPageModel.setReadonly(false);
                List<RuleModel> ruleModelList = basicPageModel.getRules();
                if (CollectionUtils.isNotEmpty(ruleModelList)) {
                    for (RuleModel ruleModel : ruleModelList) {
                        if (ruleModel.getTarget() == null) {
                            break;
                        }
                        ruleModel.getTarget().put("readonly", false);
                    }
                }
            }
        }
    }

    /**
     * 有价券商品需要追加信息：
     * 说明：商品库存等于所有优惠券中的最小库存数
     */
    @PrepareForRender(key = "add-info-for-stock")
    public void addInfoForCouponStock(BasicCompDO<Integer> compDO, CompExtParam param, @Category StdCategoryDO category) {

        AbstractCompDO dependCompDO = param.getDependCompDO(CompConstants.COUPON_ITEM_TYPE);
        if (dependCompDO != null && dependCompDO.getValue() != null && dependCompDO.getValue().equals(1)) {
            InfoModel infoModel = compDO.getPagemodel().getInfo();
            if (infoModel == null) {
                infoModel = new InfoModel();
                infoModel.setRight(Lists.newArrayList(FliggySwitchConfig.blindBoxStockTips));
                compDO.getPagemodel().setInfo(infoModel);
            } else {
                infoModel.setRight(Lists.newArrayList(FliggySwitchConfig.blindBoxStockTips));
            }
            return;
        }

        // 抽奖的情况下设置可编辑库存，同时针对酒店权益卡也可编辑库存
        if (dependCompDO != null && dependCompDO.getValue() != null && dependCompDO.getValue().equals(2)) {
            NumberPageModel numberPageModel= (NumberPageModel) compDO.getPagemodel();
            numberPageModel.setReadonly(false);
            InfoModel infoModel = new InfoModel();
            numberPageModel.setInfo(infoModel);
            return;
        } else if (dependCompDO != null && dependCompDO.getValue() != null && dependCompDO.getValue().equals(200)) {
            NumberPageModel numberPageModel = (NumberPageModel) compDO.getPagemodel();
            numberPageModel.setReadonly(false);
            numberPageModel.setPlaceholder("请输入库存");
            numberPageModel.setInfo(new InfoModel());
            return;
        } else if (category.getCategoryId()==FliggySwitchConfig.hotelBenefitCardCatIds) {
            NumberPageModel numberPageModel= (NumberPageModel) compDO.getPagemodel();
            numberPageModel.setReadonly(false);
            InfoModel infoModel = new InfoModel();
            numberPageModel.setInfo(infoModel);
            numberPageModel.setPlaceholder("酒店权益卡库存请自行输入");
            return;
        }

        NumberPageModel numberPageModel= (NumberPageModel) compDO.getPagemodel();
        if (numberPageModel.getInfo()!=null) {
            numberPageModel.getInfo().setRight(Lists.newArrayList("说明：商品库存等于所有优惠券中的最小库存数"));
        } else {
            InfoModel infoModel=new InfoModel();
            infoModel.setRight(Lists.newArrayList("说明：商品库存等于所有优惠券中的最小库存数"));
            numberPageModel.setInfo(infoModel);
        }
    }
}
