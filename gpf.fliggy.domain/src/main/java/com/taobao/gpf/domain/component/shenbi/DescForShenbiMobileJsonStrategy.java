package com.taobao.gpf.domain.component.shenbi;

import com.alibaba.gpf.business.domain.util.ParamUtil;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.shared.componet.BaseCompParseJsonStrategy;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.config.FliggySwitchConfig;

public class DescForShenbiMobileJsonStrategy extends BaseCompParseJsonStrategy<BasicCompDO<String>> {

    @Override
    public String getName() {
        return "descForShenbiMobileJsonStrategy";
    }

    @Override
    public JsonNode renderJson(BasicCompDO<String> compDO , CompExtParam param) {
        BasicCompDO<Long> primaryCompDo = (BasicCompDO)param.getDependCompDO("id");
        Long primaryId = null;
        if  (primaryCompDo != null) {
            primaryId = primaryCompDo.getValue();
        }

        ObjectNode objectNode = compDO.getPagemodel().toJsonNode();
        objectNode.put("itemId", primaryId);
        objectNode.put("catId", ParamUtil.getCategoryDO(param).getCategoryId());

        ObjectNode iframeParamsNode = objectNode.with("iframeParams");
        iframeParamsNode.put("bizSource", "fliggy");
        if (FliggySwitchConfig.USE_OLD_DETAIL_EDIT) {
            iframeParamsNode.put("back2OldVersion", true);
        }

        /*ObjectNode nativeDetailNode = containerNode.with("descContainer");
        nativeDetailNode.put("nativeDetail", compDO.getValue());*/

        ObjectNode containerNode = JacksonUtil.createObjectNode();
        DescForShenbiControlDO controlDO = compDO.getControlDO();
        ShenbiDescContainer shenbiDescContainer = controlDO.getDescContainer() == null ? new ShenbiDescContainer() : controlDO.getDescContainer();
        ObjectNode nativeDetailNode = containerNode.with("descContainer");
        nativeDetailNode.put("nativeDetail", shenbiDescContainer.getNativeDetail());
        nativeDetailNode.put("detail", shenbiDescContainer.getDetail());
        nativeDetailNode.put("moduleList", shenbiDescContainer.getModuleList());
        nativeDetailNode.put("customizationData", shenbiDescContainer.getCustomizationData());

        objectNode.putPOJO("value", containerNode);

        return objectNode;
    }

    @Override
    public void parseJson(BasicCompDO<String> compDO, JsonNode node, CompExtParam param) {
        if (!node.isNull()) {
            compDO.setValue(node.toString());
        }

    }

}


