package com.taobao.gpf.domain.component.user;

import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.config.CompConfig;
import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.shared.extpoint.build.BaseBuildModelCompStrategy;


/**
 * <AUTHOR>
 * @Description:
 * @date Created in 2019-04-17 14:13
 */
public class UserIdBuildStrategy extends BaseBuildModelCompStrategy {

    @Override
    public String getName() {
        return "userIdBuildStrategy";
    }

    @Override
    public AbstractCompDO buildCompDO(CompConfig cc, Param param) throws Exception {
        AbstractCompDO compDO = super.buildCompDO(cc, param);
        Long id = buildUserId(param);
        compDO.setValue(id);
        return compDO;
    }

    /**
     * 构建商品id
     * 1.优先 从request获取
     * 2.如果没有获取到且 开启了预生成模式
     *
     * @param
     * @param param
     * @return
     */
    private Long buildUserId(Param param) {
        Long id = param.getReq().getUserId();
        return id;
    }
}
