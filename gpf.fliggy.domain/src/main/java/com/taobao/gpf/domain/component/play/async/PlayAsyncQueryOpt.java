package com.taobao.gpf.domain.component.play.async;

import com.alibaba.gpf.sdk.param.Param;
import com.alibaba.gpf.sdk.response.AsyncResult;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alibaba.gpf.shared.extpoint.async.BaseQueryAsyncOpt;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.component.play.PlayType;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.repository.PlayServiceRepo;
import com.taobao.gpf.domain.model.play.PlayView;
import com.taobao.gpf.domain.repository.PlayTypeHelpServiceRepo;
import com.taobao.gpf.domain.repository.PlayTypeSwitchServiceRepo;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 异步获取玩法信息
 * <AUTHOR>
 */
@Slf4j
public class PlayAsyncQueryOpt extends BaseQueryAsyncOpt<JsonNode> {

    private final static String PLAY_ASYNC_QUERY_OPT = "playAsyncQueryOpt";

    @Resource
    private PlayServiceRepo playServiceRepo;

    @Resource
    private TravelSellConfig travelSellConfig;

    @Resource
    private PlayTypeSwitchServiceRepo playTypeSwitchServiceRepo;

    @Override
    public String getName() {
        return PLAY_ASYNC_QUERY_OPT;
    }

    @Override
    public AsyncResult<JsonNode> process(Param param) {
        JsonNode jsonBody = (JsonNode) param.getReq().getPageParam().getData();
        if(null == jsonBody){
            return new AsyncResult<>();
        }
        Long catId = jsonBody.get("catId").asLong(0);
        JsonNode data = queryDaysSug(catId, param);
        return new AsyncResult<>(data);
    }


    private JsonNode queryDaysSug(Long catId, Param param) {
        if(null == catId || catId <= 0){
            return null;
        }
        ObjectNode data = JacksonUtil.createObjectNode();
        if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(catId)) {
            List<PlayType> playTypeTree = playTypeSwitchServiceRepo.getPlayTypeTree(catId, 1L, FliggyParamUtil.getUserId(param));
            if (CollectionUtils.isEmpty(playTypeTree)) {
                return null;
            }
            List<PlayView> playViewList = playTypeTree.stream()
                    .map(playType -> buildPlayView(playType))
                    .collect(Collectors.toList());

            data.putPOJO("dataSource",playViewList);
        } else {
            //获取可配置玩法列表
            List<PlayView> playList = playServiceRepo.getValidPlayList(catId);

            data.putPOJO("dataSource",playList);
        }
        return data;
    }

    private PlayView buildPlayView(PlayType playType) {
        PlayView playView = new PlayView();

        playView.setName(playType.getName());
        playView.setValue(String.valueOf(playType.getId()));
        playView.setLabel(playType.getName());
        List<PlayType> children = playType.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            List<PlayView> childPlayView = children.stream()
                    .map(childrenPlay -> buildPlayView(childrenPlay))
                    .collect(Collectors.toList());
            playView.setChildren(childPlayView);
        }
        if (CollectionUtils.isEmpty(playView.getChildren())) {
            playView.setSelectable(true);
        }

        return playView;
    }


}
