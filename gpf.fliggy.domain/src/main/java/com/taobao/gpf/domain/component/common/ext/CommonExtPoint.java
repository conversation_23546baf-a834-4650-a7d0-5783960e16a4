package com.taobao.gpf.domain.component.common.ext;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.annotation.bind.User;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.Enable;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.ParentCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.param.CompExtParam;

import com.alibaba.gpf.sdk.response.CheckResult;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.component.saleproperty.SalePropUtil;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.helper.TripItemInfoHelper;
import com.taobao.gpf.domain.helper.TripSellerInfoHelper;
import com.taobao.gpf.domain.model.pagemodel.FliggyPackageInfoPageModel;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.UserUtil;
import com.taobao.uic.common.domain.BaseUserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import static com.taobao.gpf.domain.constant.IcmpGlobalConstants.COPY_ID;

/**
 * @description:可复用的公共扩展点
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommonExtPoint implements IInjectExtension {

    @Resource
    private TripItemInfoHelper tripItemInfoHelper;

    @Resource
    private TripSellerInfoHelper tripSellerInfoHelper;


    @Enable(key = "common-fliggy-supplier-disEnable", desc = "通用供应商屏蔽扩展点实现")
    public boolean enableMinBookDays(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO,@User BaseUserDO baseUserDO){
        boolean notTicket = FliggyParamUtil.getCategoryId(param) != FliggySwitchConfig.ticketCatId;
        boolean isSupplier  = tripItemInfoHelper.isSupplierItem(param);
        return !(isSupplier && notTicket);
    }

    @Enable(key = "common-fliggy-coupon-enable", desc = "通用梦想券展示扩展点实现")
    public boolean enableCoupon(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO,@User BaseUserDO baseUserDO){
        //不支持99梦想券的类目，无需处理
        if (CollectionUtils.isEmpty(FliggySwitchConfig.supportDreamExchangeCatIds)){
            return false;
        }
        //api不允许发99兑换券
        if(FliggyParamUtil.isTopSource(param.getReq())){
            return false;
        }
        if (!FliggySwitchConfig.supportDreamExchangeCatIds.contains(categoryDO.getCategoryId())){
            return false;
        }
        if (!FliggySwitchConfig.isOpenDreamItem){
            Long userId = FliggyParamUtil.getUserDO(param).getUserId();
            if (!FliggySwitchConfig.supportDreamTestUserId.contains(userId)){
                return false;
            }
        }
        return tripItemInfoHelper.isCouponItem(param);
    }

    @Enable(key = "common-fliggy-coupon-disable", desc = "与梦想券冲突的组件disable掉")
    public boolean enableNoCoupon(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO,@User BaseUserDO baseUserDO){
        return !enableCoupon(param, compDO, categoryDO, baseUserDO);
    }

    @Enable(key = "enable-common-support-coupon-comp", desc = "支持99兑换商品的类目-组件可用")
    public boolean enableSupportCoupon(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO,@User BaseUserDO baseUserDO){
        if (!FliggySwitchConfig.isOpenDreamItem){
            Long userId = FliggyParamUtil.getUserDO(param).getUserId();
            if (!FliggySwitchConfig.supportDreamTestUserId.contains(userId)){
                return false;
            }
        }
        return CollectionUtils.isNotEmpty(FliggySwitchConfig.supportDreamExchangeCatIds) && FliggySwitchConfig.supportDreamExchangeCatIds.contains(categoryDO.getCategoryId());
    }

    @PrepareForRender(key = "fliggy-common-readonly-setting", desc = "通用只读设置")
    public void commonReadonlySetting(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        ((BasicPageModel)compDO.getPagemodel()).setReadonly(true);
    }

    @PrepareForRender(key = "compInfo-fliggy-coupon-readonly-setting", desc = "通用梦想券-组件只读设置")
    public void couponCompInfoReadonlySetting(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (!FliggySwitchConfig.isOpenDreamItem){
            Long userId = FliggyParamUtil.getUserDO(param).getUserId();
            if (!FliggySwitchConfig.supportDreamTestUserId.contains(userId)){
                return;
            }
        }
        //不支持99梦想券的类目，无需处理
        if (CollectionUtils.isEmpty(FliggySwitchConfig.supportDreamExchangeCatIds)){
            return;
        }
        if (!FliggySwitchConfig.supportDreamExchangeCatIds.contains(categoryDO.getCategoryId())){
            return;
        }
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        //梦想券
        if(tripItemInfoHelper.isCouponItem(param)){
            ((BasicPageModel)compDO.getPagemodel()).setReadonly(true);
        }
    }

    @PrepareForRender(key = "common-fliggy-setRequired", desc = "通用设置必填")
    public void commonSetRequired(CompExtParam param, AbstractCompDO compDO) {
        ((BasicPageModel)compDO.getPagemodel()).setRequired(true);
    }

    @Check(key = "required-pickupmyself-check", desc = "自取说明校验")
    public CheckResult check(CompExtParam param, AbstractCompDO compDO){
        CheckResult result = new CheckResult();
        Boolean required = ((BasicPageModel) compDO.getPagemodel()).getRequired();
        if (BooleanUtils.isTrue(required) && Objects.isNull(compDO.getValue())) {
            result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("pickUpMyself",
                    "自取说明不能为空"));
            return result;
        }
        return CheckResult.success();
    }

    @PrepareForRender(key = "saleInfo-fliggy-readonly-setting", desc = "参加大促的商品需要锁定销售属性信息")
    public void saleInfoReadonlySetting(CompExtParam param, AbstractCompDO compDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }

        if (!SalePropUtil.allowUpdateSaleInfo(param)) {
            ((BasicPageModel)compDO.getPagemodel()).setReadonly(true);
        }
    }

    @PrepareForRender(key="comp-required-setting",desc = "针对一些组件设置必填，比如避免隐藏组件校验问题")
    public void setElementRequired(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO){
        if (compDO.getPagemodel() instanceof BasicPageModel) {
            ((BasicPageModel) compDO.getPagemodel()).setRequired(true);
        }
    }

    @PrepareForRender(key = "compInfo-fliggy-dealerItem-readonly-setting", desc = "分销商品身份组件只读设置")
    public void compInfoReadonlySetting(CompExtParam param, AbstractCompDO compDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        //分销商品身份
        if(tripItemInfoHelper.isDealerItem(param)){
            ((BasicPageModel)compDO.getPagemodel()).setReadonly(true);
        }

    }

    @PrepareForRender(key = "compInfo-fliggy-dealerItem-visible-setting", desc = "分销商品身份组件不可见设置")
    public void compInfoVisibleSetting(CompExtParam param, AbstractCompDO compDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        //分销商品身份
        if(tripItemInfoHelper.isDealerItem(param)){
            ((BasicPageModel)compDO.getPagemodel()).setVisible(false);
            ((BasicPageModel)compDO.getPagemodel()).setRequired(false);
        }
    }


    @PrepareForRender(key = "compInfo-fliggy-supplier-visible-setting", desc = "供应商身份组件隐藏设置")
    public void compInfoVisibleSettingBySupplier(CompExtParam param, AbstractCompDO compDO) {
        if(null == compDO || null == compDO.getPagemodel()){
            return ;
        }
        if(!(compDO.getPagemodel() instanceof BasicPageModel)){
            return;
        }
        //供应商身份
        if(tripSellerInfoHelper.isSupplier(FliggyParamUtil.getUserDO(param).getUserId())){
            ((BasicPageModel)compDO.getPagemodel()).setVisible(false);
            ((BasicPageModel)compDO.getPagemodel()).setRequired(false);
        }
    }

    @Enable(key = "common-fliggy-supplier-shield", desc = "供应商身份组件disEnable")
    public boolean commonFliggySupplierDisEnable(CompExtParam param, AbstractCompDO compDO) {
        boolean notTicket = FliggyParamUtil.getCategoryId(param) != FliggySwitchConfig.ticketCatId;
        Long userId = FliggyParamUtil.getUserDO(param).getUserId();
        return !(tripSellerInfoHelper.isSupplier(userId) && notTicket);
    }

    @PrepareForRender(key = "saleProp-fliggy-addLockSaleInfoTips")
    public void addLockSaleInfoTips(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO category) {
        boolean isLock = SalePropUtil.isInSalesPromotionActivity(param.getReq().isEdit(), FliggyParamUtil.getDbTravelItemDO(param));
        if(isLock){
            String itemIdStr = String.valueOf(FliggyParamUtil.getDbTravelItemDO(param).getItemId());
            InfoModel infoModel = compDO.getPagemodel().getInfo();
            infoModel.getTop().add(FliggySwitchConfig.lockSaleInfoTip.replace("{itemId}", itemIdStr));
            compDO.getPagemodel().setInfo(infoModel);
        }
    }

    @Enable(key = "common-fliggy-internationalUser-shield", desc = "飞猪国际商家屏蔽")
    public boolean commonFliggyInternationalUserDisEnable(CompExtParam param, AbstractCompDO compDO,@Category StdCategoryDO category,@User BaseUserDO baseUserDO) {
        return internationalUserEnableCheck(baseUserDO,category.getCategoryId());
    }

    public static boolean internationalUserEnableCheck(BaseUserDO baseUserDO,int catId){
        if(null == baseUserDO){
            return true;
        }
        if(FliggySwitchConfig.alipayAgreementCheckCatIdList.contains((long)catId)){
            boolean isQuarInternationalUser = UserUtil.isQuarInternationalUser(baseUserDO);
            return !isQuarInternationalUser;
        }else{
            return true;
        }
    }

    @PrepareForRender(key = "packageStock-low-price-tips-addRules", desc = "套餐库存info不合理低价说明")
    public void packageStockAddrules(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO){
        if (compDO.getPagemodel() == null) {
            return;
        }
        InfoModel infoModel = compDO.getPagemodel().getInfo() == null ? new InfoModel() : compDO.getPagemodel().getInfo();
        List<String> infos = infoModel.getRight();
        if (CollectionUtils.isNotEmpty(infos)){
            infos = Lists.newArrayList();
        }
        infos.add(FliggySwitchConfig.lowPriceTips);
        infoModel.setRight(infos);
        compDO.getPagemodel().setInfo(infoModel);
    }

    private static void processBigPromotionCompReadonly(CompExtParam param, AbstractCompDO compDO, StdCategoryDO categoryDO, Integer tag) {
        if (compDO.getPagemodel() == null) {
            return;
        }
        if (!param.getReq().isEdit()) {
            return;
        }
        if (Objects.nonNull(param.getReq().getExtParam().get(COPY_ID))) {
            return;
        }
        if (!FliggySwitchConfig.BIG_PROMOTION_LOCK_COMP_CATEGORY.contains((long)categoryDO.getCategoryId())) {
            return;
        }
        if (!FliggyParamUtil.isContainTag(param, tag)) {
            return;
        }
        if (compDO.getPagemodel() instanceof BasicPageModel) {
            ((BasicPageModel) compDO.getPagemodel()).setReadonly(true);
        }
    }

    @PrepareForRender(key = "big-promotion-shelfTime-readonly-setting", desc = "大促期间锁定上下架组件只读")
    public void setShelfTimeReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_SHELF_TIME_LOCKED_FOR_EDIT_TAG);
    }

    @PrepareForRender(key = "big-promotion-fee-include-readonly-setting", desc = "大促期间锁定费用包含组件只读")
    public void setFeeIncludeReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_FEE_INCLUDE_LOCKED_FOR_EDIT_TAG);
    }

    @PrepareForRender(key = "big-promotion-refund-rule-readonly-setting", desc = "大促期间锁定费用包含组件只读")
    public void setRefundRuleReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_REFUND_RULE_LOCKED_FOR_EDIT_TAG);
    }

    @PrepareForRender(key = "big-promotion-sku-num-readonly-setting", desc = "大促期间锁定SKU数量不可加减")
    public void setSkuNumReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        if (compDO.getPagemodel() == null) {
            return;
        }
        if (!param.getReq().isEdit()) {
            return;
        }
        if (Objects.nonNull(param.getReq().getExtParam().get(COPY_ID))) {
            return;
        }
        if (!FliggySwitchConfig.BIG_PROMOTION_LOCK_COMP_CATEGORY.contains((long)categoryDO.getCategoryId())) {
            return;
        }
        if (CollectionUtils.isEmpty(FliggySwitchConfig.ITEM_SKU_NUM_LOCKED_FOR_EDIT_TAG)) {
            return;
        }
        for (Integer tag : FliggySwitchConfig.ITEM_SKU_NUM_LOCKED_FOR_EDIT_TAG) {
            if (!FliggyParamUtil.isContainTag(param, tag)) {
                continue;
            }
            if (compDO.getPagemodel() instanceof FliggyPackageInfoPageModel) {
                ((FliggyPackageInfoPageModel) compDO.getPagemodel()).setNoAdd(true);
                ((FliggyPackageInfoPageModel) compDO.getPagemodel()).setNoDelete(true);
                // 复制 排序功能也同步关闭
                ((FliggyPackageInfoPageModel) compDO.getPagemodel()).setCustomOrder(false);
                ((FliggyPackageInfoPageModel) compDO.getPagemodel()).setRenderCopyBtn(false);
            }
        }
    }

    @PrepareForRender(key = "big-promotion-sku-name-readonly-setting", desc = "大促期间锁定SKU名称")
    public void setSkuNameReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_SKU_NAME_LOCKED_FOR_EDIT_TAG);
    }

    @PrepareForRender(key = "big-promotion-sku-price-readonly-setting", desc = "大促期间锁定SKU非日历价格")
    public void setSkuPriceReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_SKU_PRICE_LOCKED_FOR_EDIT_TAG);
    }

    @PrepareForRender(key = "big-promotion-sku-without-price-name-readonly-setting", desc = "大促期间锁定SKU除价格名称外所有信息")
    public void setSkuReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_SKU_WITHOUT_PRICE_NAME_LOCKED_FOR_EDIT_TAG);
    }

    @PrepareForRender(key = "big-promotion-sku-without-price-name-currency-readonly-setting", desc = "大促期间锁定SKU除价格名称币种外所有信息")
    public void setSkuWithoutCurrencyReadOnlyOnBigPromotion(CompExtParam param, AbstractCompDO compDO, @Category StdCategoryDO categoryDO) {
        processBigPromotionCompReadonly(param, compDO, categoryDO, FliggySwitchConfig.ITEM_SKU_WITHOUT_PRICE_NAME_CURRENCY_LOCKED_FOR_EDIT_TAG);
    }
}
