package com.taobao.gpf.domain.component.sku;

import java.util.Map;

import com.alibaba.gpf.base.publish.domain.component.sku.BasicSkuRowStoreDO;

import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.dataobject.MoneyValueDO;
import com.google.common.collect.Maps;
import com.taobao.gpf.domain.component.calendarprice.CalendarPriceDO;
import com.taobao.gpf.domain.itemdirect.model.PriceStockDirectModel;
import com.taobao.gpf.domain.itemdirect.model.SkuRuleDirectModel;
import com.taobao.gpf.domain.publish.store.packageinfo.TicketRuleDomain;
import com.taobao.gpf.share.directItem.model.DirectInfoDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/04/22/下午4:11
 */
@Data
public class FliggySkuRowStoreDO extends BasicSkuRowStoreDO {


    /**
     * 日历价格
     */
    private CalendarPriceDO calendarPrice;

    /**
     * 票种vid
     */
    private Long ticketVid;

    //套餐说明
    private String packageDesc ;

    //费用包含
    private String feeInclude ;

    //补充说明
    private String extraDesc ;

    //当日票提价
    private Double premium;

    //提价商家
    private String premiumCode;

    private TextValueModel ticketRule ;

    /**
     * 门票规则领域模型
     */
    private TicketRuleDomain ticketRuleDomain;

    /**
     * 采购价格
     */
    private MoneyValueDO purchasePrice ;

    /**
     * sku维度的feature
     */
    private Map<String, String> skuFeature;

    /**
     * 关联的产品Id
     */
    private Long relateProductId;


    private PriceStockDirectModel priceStockDirect;

    /**
     * 直连信息
     */
    private DirectInfoDO directInfoDO;

    /**
     * 规则直连
     */
    private SkuRuleDirectModel skuRuleDirect;

    /**
     * sku状态：
     * 1-正常，0-冻结，-1-删除,-2-下架
     */
    private Integer status;

    public Map<String, String> getSkuFeature() {
        if(null == skuFeature){
            skuFeature = Maps.newHashMap();
        }
        return skuFeature;
    }

    private String ticketSkuExt;

    public void addSkuFeature(String key, String value){
        if(StringUtils.isAnyBlank(key,value)){
            return;
        }
        getSkuFeature().put(key,value);
    }

    public String getSkuFeature(String key){
        if(StringUtils.isBlank(key)){
            return null;
        }
        return getSkuFeature().get(key);
    }


}
