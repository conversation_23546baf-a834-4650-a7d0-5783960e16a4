package com.taobao.gpf.domain.component.shenbi;

import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.google.common.collect.Lists;
import com.taobao.gpf.domain.config.FliggySwitchConfig;

public class DescForShenbiExtPoint implements IInjectExtension {

    @PrepareForRender(key = "shenbi-prepare-render")
    public void  addInfoModel(CompExtParam param, BasicCompDO<String> compDO) {
        if (compDO.getPagemodel() == null) {
            return;
        }
        InfoModel infoModel = compDO.getPagemodel().getInfo() == null ? new InfoModel() : compDO.getPagemodel().getInfo();
        infoModel.setTop(Lists.newArrayList(FliggySwitchConfig.shenbiTopText));
        compDO.getPagemodel().setInfo(infoModel);
    }
}
