package com.taobao.gpf.domain.component.price;

import com.alibaba.gpf.sdk.annotation.ExtendClassDesc;
import com.alibaba.gpf.sdk.model.pagemodel.NumberPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.PricePageModel;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by maoxinming on 2019/7/18.
 */
@ExtendClassDesc("pricePageModel")
public class FliggyPricePageModel extends PricePageModel {

    @Getter
    @Setter
    private Integer precision ;

    @Getter
    @Setter
    private String htmlType ;

    @Getter
    @Setter
    private  Boolean showMsg=false;


    public ObjectNode toJsonNode() {
        ObjectNode jsonNodes = super.toJsonNode();
        if(this.precision != null) {
            jsonNodes.put("precision", this.precision);
        }
        if(this.htmlType != null) {
            jsonNodes.put("htmlType", this.htmlType);
        }
        jsonNodes.put("showMsg",showMsg);

        return jsonNodes;
    }
}
