package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Created on 2019-06-12 14:10:14
 */
public class FliggyCruiseItemIdentity extends BaseAppBizIdentity {
    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)param.getWro()).getCategory() ;
        return FliggySwitchConfig.cruiseCatIds.contains(Long.valueOf(stdCategoryDO.getCategoryId()+""));
    }
}
