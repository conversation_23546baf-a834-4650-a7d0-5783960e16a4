package com.taobao.gpf.domain.component.dive.schema;

import com.alibaba.gpf.base.top.domain.extpoint.compparser.inputfield.InputFieldSchemaStrategy;
import com.alibaba.gpf.base.top.sdk.extpoint.SchemaParseContext;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.config.AdapterCompConfig;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.top.schema.enums.ValueTypeEnum;
import com.taobao.top.schema.field.InputField;
import com.taobao.top.schema.rule.ValueTypeRule;
import org.apache.commons.lang3.StringUtils;

/**
 * 水下拍摄时长组件Schema策略
 * 处理拍摄时长的输入字段，数据类型为整数（分钟）
 * 
 * <AUTHOR> Code
 * @date 2025-08-28
 */
public class DiveShootDurationFieldSchemaStrategy extends InputFieldSchemaStrategy<AbstractCompDO<String>> {

    @Override
    protected void setCompValue(AbstractCompDO<String> compDO, InputField field, 
                              CompExtParam param, AdapterCompConfig compConfig, 
                              SchemaParseContext context) {
        if (field == null || field.getValue() == null || StringUtils.isBlank(field.getValue().getValue())) {
            return;
        }
        
        // 从InputField中获取值并设置到组件中
        String value = field.getValue().getValue();
        compDO.setValue(value);
    }

    @Override
    protected void renderSchemaFieldValue(InputField field, AbstractCompDO<String> compDO,
                                        CompExtParam param, AdapterCompConfig compConfig,
                                        SchemaParseContext context) {
        if (compDO == null || StringUtils.isBlank(compDO.getValue())) {
            return;
        }
        
        // 从组件中获取值并设置到InputField中
        field.setValue(compDO.getValue());
    }

    @Override
    protected ValueTypeRule genCompValueTypeRule() {
        // 拍摄时长应该是整数类型（分钟）
        return new ValueTypeRule(ValueTypeEnum.INTEGER.value());
    }

    @Override
    public String getName() {
        return "diveShootDurationFieldSchemaStrategy";
    }
}