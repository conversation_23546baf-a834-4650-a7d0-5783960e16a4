package com.taobao.gpf.domain.component.catprop.match;

import javax.annotation.Resource;

import com.alibaba.gpf.base.publish.sdk.extpoint.ICompTemplateMatchStrategy;
import com.alibaba.gpf.base.publish.sdk.param.MatchParam;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyDO;
import com.taobao.gpf.domain.config.TravelSellConfig;


/**
 * @description:目的地类目属性
 * <AUTHOR>
 */
public class FliggyDestPropMatchStrategy implements ICompTemplateMatchStrategy<StdCategoryPropertyDO> {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public boolean match(MatchParam param, StdCategoryPropertyDO stdCategoryPropertyDO) {
        //TODO
        return stdCategoryPropertyDO.getPropertyId() == travelSellConfig.getGuoneiToPid();
    }

    @Override
    public String getName() {
        return "fliggyDestPropMatchStrategy";
    }
}