package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * @class: CollageItemIdentity
 * @description:
 * @author: jiuyi-qyj
 * @date: 2022/8/1
 */
public class CollageItemIdentity extends BaseAppBizIdentity {
    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)param.getWro()).getCategory() ;
        return FliggySwitchConfig.collageItemCatIds.contains(Long.parseLong(String.valueOf(stdCategoryDO.getCategoryId())));
    }
}
