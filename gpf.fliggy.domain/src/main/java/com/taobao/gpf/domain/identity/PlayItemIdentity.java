package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;
import com.taobao.gpf.domain.utils.FliggyParamUtil;

import javax.annotation.Resource;

/**
 * 玩乐套餐身份验证
 */
public class PlayItemIdentity extends BaseAppBizIdentity {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject) param.getWro()).getCategory();
        return travelSellConfig.getGuoneiDangdiwanleCatId() == stdCategoryDO.getCategoryId() ||
                //境外玩乐且非api直连，可能有一些top直连的接口过来的请求？区别于PlayAbroadItemIdentity
                (travelSellConfig.getJingwaiWanletaocanCatId() == stdCategoryDO.getCategoryId() && !FliggyParamUtil.isApiDirect(((FliggyRequiredObject) param.getWro()).getRequest())) ||
                        // 20220621 新增露营三级类目
                        FliggySwitchConfig.CAMP_CATEGORY_ID == stdCategoryDO.getCategoryId() ||
                        FliggySwitchConfig.hotelSceneryCatId == stdCategoryDO.getCategoryId();
    }
}
