package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;
import com.taobao.gpf.domain.utils.IdentityUtil;

/**
 * Created by maoxinming on 2019/3/20.
 */
public class TicketItemIdentity extends BaseAppBizIdentity {
    @Override
    public boolean isFitByCustom(IdentityParam identityParam) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject) identityParam.getWro()).getCategory();
        return IdentityUtil.isTicket(stdCategoryDO);
    }
}
