package com.taobao.gpf.domain.identity;

import javax.annotation.Resource;

import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Created on 2019-06-12 14:10:14
 */
public class TripDiveItemIdentity extends BaseAppBizIdentity {

    @Resource
    private TravelSellConfig travelSellConfig;

    @Override
    public boolean isFitByCustom(IdentityParam param) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)param.getWro()).getCategory();
        return travelSellConfig.isDive(stdCategoryDO.getCategoryId());
    }
}
