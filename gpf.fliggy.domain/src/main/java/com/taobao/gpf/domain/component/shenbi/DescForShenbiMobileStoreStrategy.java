package com.taobao.gpf.domain.component.shenbi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.travelitems.model.ExtraUpdateInfo;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import org.apache.commons.lang3.StringUtils;

public class DescForShenbiMobileStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<String>> {

    @Override
    public String getName() {
        return "descForShenbiMobileStoreStrategy";
    }

    @Override
    public void parseStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        DescForShenbiControlDO descForShenbiControlDO = compDO.getControlDO();
        ShenbiDescContainer shenbiDescContainer = new ShenbiDescContainer();
        descForShenbiControlDO.setDescContainer(shenbiDescContainer);
        if (StringUtils.isEmpty(storeDO.getNativeDetailJson())) {
            return;
        }
        shenbiDescContainer.setModuleList(storeDO.getModuleList());
        shenbiDescContainer.setCustomizationData(storeDO.getCustomizationData());
        shenbiDescContainer.setNativeDetail(storeDO.getNativeDetailJson());
        shenbiDescContainer.setDetail(storeDO.getMobileDescr());
    }

    @Override
    public void transferStore(BasicCompDO<String> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (StringUtils.isNotEmpty(compDO.getValue())) {
            ShenBiDecorateData shenBiDecorateData = JSON.parseObject(compDO.getValue(), ShenBiDecorateData.class);
            //用于shenbiListener处理
            storeDO.getAlitripFeatures().put("shenbiCid", shenBiDecorateData.getCid() == null ? null : String.valueOf(shenBiDecorateData.getCid()));
            buildDetailShenBiDecorate(storeDO, shenBiDecorateData);
            //旺铺装修的h5 xml格式
            if (shenBiDecorateData.getDescContainer() != null && StringUtils.isNotEmpty(shenBiDecorateData.getDescContainer().getDetail())) {
                storeDO.setMobileDescr(shenBiDecorateData.getDescContainer().getDetail());
            }
        } /*else {
            buildOldDetailShenBiDecorate(item, Contexts.getTravelItem());
        }*/
        //处理无线图文(top/旧编辑器)
        //processMobileDescr(item);
    }

    /**
     * 从前端传值获取装修数据
     * @param shenBiDecorateData
     * @return
     */
    private void buildDetailShenBiDecorate(TravelItemStoreDO storeDO, ShenBiDecorateData shenBiDecorateData) {
        ShenbiDescContainer shenbiDescContainer = shenBiDecorateData.getDescContainer();
        if (null != shenbiDescContainer) {
            storeDO.setCustomizationData(shenbiDescContainer.getCustomizationData());
            storeDO.setModuleList(shenbiDescContainer.getModuleList());
            storeDO.setNativeDetailJson(shenbiDescContainer.getNativeDetail());
            storeDO.setExtraUpdateInfo(buildExtraUpdateInfo(shenbiDescContainer, storeDO));
        }
    }

    private ExtraUpdateInfo buildExtraUpdateInfo(ShenbiDescContainer shenbiDescContainer, TravelItemStoreDO storeDO) {
        ExtraUpdateInfo extraUpdateInfo = storeDO.getExtraUpdateInfo();
        if(null == extraUpdateInfo){
            extraUpdateInfo = new ExtraUpdateInfo();
        }
        if (shenbiDescContainer.getRemoveTags() != null) {
            extraUpdateInfo.getRemoveTags().addAll(shenbiDescContainer.getRemoveTags());
        }
        if (shenbiDescContainer.getTags() != null) {
            extraUpdateInfo.getAddTags().addAll(shenbiDescContainer.getTags());
        }

        ShenbiFeatures features = shenbiDescContainer.getFeatures();

        if (features == null) {
            return extraUpdateInfo;
        }
        if (StringUtils.isNotBlank(features.getTspeditor_sell_wl_push())){
            extraUpdateInfo.getAddFeatures().put("tspeditor_sell_wl_push",
                    shenbiDescContainer.getFeatures().getTspeditor_sell_wl_push());
        }
        if (StringUtils.isNotBlank(features.getTspeditor_wl_template())) {
            extraUpdateInfo.getAddFeatures().put("tspeditor_wl_template",
                    shenbiDescContainer.getFeatures().getTspeditor_wl_template());
        }

        return extraUpdateInfo;
    }

    /**
     * 解析传递给前端的ShenbiJsonTfsUrl
     * @param nativeDetailJson
     * @return
     */
    private String parseShenbiJsonTfsUrl(String nativeDetailJson) {
        if (StringUtils.isBlank(nativeDetailJson)){
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(nativeDetailJson);
        JSONObject data = jsonObject.getJSONObject("data");
        if (null != data){
            JSONObject params = data.getJSONObject("params");
            if (params != null){
                String shenbiJsonTfsUrl = params.getString("shenbiJsonTfsUrl");
                return shenbiJsonTfsUrl;
            }
        }
        return null;
    }

}

