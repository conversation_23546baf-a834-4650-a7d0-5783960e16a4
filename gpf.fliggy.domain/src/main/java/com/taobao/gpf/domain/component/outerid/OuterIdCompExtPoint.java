package com.taobao.gpf.domain.component.outerid;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasePageModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggySwitchConfig;

/**
 * Created by maoxinming on 2020/3/12.
 */
public class OuterIdCompExtPoint implements IInjectExtension {

    @PrepareForRender(key="add-outerid-info",desc = "给商家编码设置不同的文案提醒")
    public void addInfo(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO categoryDO){
        BasePageModel basePageModel = compDO.getPagemodel() ;
         if(FliggySwitchConfig.globalTourCatIds.contains((long)categoryDO.getCategoryId())){
            basePageModel.getInfo().addBottom("非必填，帮助商家对应自有库存系统内的货品编码。" );
        }
    }
}
