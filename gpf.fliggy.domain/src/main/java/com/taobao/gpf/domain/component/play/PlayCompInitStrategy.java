package com.taobao.gpf.domain.component.play;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.extpoint.compPlugin.ICompInitStrategy;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 */

public class PlayCompInitStrategy implements ICompInitStrategy<BasicCompDO<List<TextValueModel>>> {

    @Override
    public String getName() {
        return "playCompInitStrategy";
    }

    @Override
    public BasicCompDO<List<TextValueModel>> getInitCompDO() {
        BasicCompDO<List<TextValueModel>> compDO = new BasicCompDO();
        compDO.setControlDO(new ControlDO());
        return compDO;
    }
}
