package com.taobao.gpf.domain.component.cartype.uitypes;

import com.alibaba.gpf.sdk.annotation.ExtendClassDesc;
import com.alibaba.gpf.sdk.model.pagemodel.DataSourcePageModel;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.model.pagemodel.FliggyRadioCardValueModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/14 00:12
 */
@ExtendClassDesc("carTypePageModel")
@Data
public class CarTypePageModel extends DataSourcePageModel<FliggyRadioCardValueModel> {
    private Boolean groupTou;
    private Integer maxAllowed;

    @Override
    public ObjectNode toJsonNode() {
        ObjectNode jsonNode = super.toJsonNode();
        if (groupTou != null) {
            jsonNode.put("groupTou", groupTou);
        }
        if (maxAllowed != null) {
            jsonNode.put("maxAllowed", maxAllowed);
        }
        return jsonNode;
    }
}
