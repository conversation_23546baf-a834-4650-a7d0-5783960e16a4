package com.taobao.gpf.domain.component.play;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.business.domain.annotation.bind.User;
import com.alibaba.gpf.sdk.annotation.ext.Check;
import com.alibaba.gpf.sdk.annotation.ext.Enable;
import com.alibaba.gpf.sdk.annotation.ext.PrepareForRender;
import com.alibaba.gpf.sdk.annotation.ext.ProcessForRender;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.model.pagemodel.BasicPageModel;
import com.alibaba.gpf.sdk.model.pagemodel.InfoModel;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.sdk.response.CheckResult;
import com.google.common.collect.Lists;
import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.FliggyGraySwitchConfig;
import com.taobao.gpf.domain.config.FliggySwitchConfig;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.model.play.PlayConfigModel;
import com.taobao.gpf.domain.repository.PlayTypeSwitchServiceRepo;
import com.taobao.gpf.domain.shared.diamond.PlayConfigDiamondProcessor;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.GraySwitchUtils;
import com.taobao.gpf.domain.utils.PlayTipsUtils;
import com.taobao.gpf.domain.utils.UserUtil;
import com.taobao.uic.common.domain.BaseUserDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * @description: 玩法扩展点
 * @author: luoli
 * @create: 2019-07-24
 */
@Slf4j
public class PlayCompExtPoint implements IInjectExtension {

    @Resource
    PlayConfigDiamondProcessor playConfigDiamondProcessor;

    @Resource
    PlayTypeSwitchServiceRepo playTypeSwitchServiceRepo;

    @Resource
    private TravelSellConfig travelSellConfig;

    @Enable(key = "play-fliggy-disable", desc = "是否启用玩法组件")
    public boolean setEnable(CompExtParam param, AbstractCompDO<String> compDO) {
        long catId = FliggyParamUtil.getCategoryId(param);
        if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(catId))) {
            // 日游是sku级别的玩法，因此特殊关闭
            if (catId == 50276003L) {
                return false;
            }
            return true;
        }
        PlayConfigModel playConfigModel = playConfigDiamondProcessor.getPlayConfigModel(catId);
        return playConfigModel != null;
    }

    @ProcessForRender(key = "play-fliggy-Required", desc = "玩法是否必填")
    public void initRequiredProps(CompExtParam param, AbstractCompDO<String> compDO) {
        String catId = param.getReq().getSystemParam().getString("catId");
        // 因为境内跟团游的情况下会存在一日游多日游的判断，因此不能使用enable来进行组件是否启用的判断标准
        if (Long.parseLong(catId) != travelSellConfig.getGuoneiGentuanyouCatId()) {
            if (FliggySwitchConfig.playRequired) {
                ((BasicPageModel) compDO.getPagemodel()).setRequired(true);
            }
        }
    }

    @ProcessForRender(key = "update-playPropName", desc = "更新玩法名称")
    public void updatePlayPropName(AbstractCompDO compDO, CompExtParam param, @Category StdCategoryDO categoryDO, @User BaseUserDO baseUserDO) {
        String catId = param.getReq().getSystemParam().getString("catId");
        if (Long.parseLong(catId) == travelSellConfig.getChujingGentuanyouCatId()) {
            compDO.getPagemodel().setLabel("线路主题");
        }
        if (Long.parseLong(catId) == travelSellConfig.getGuoneiGentuanyouCatId()) {
            compDO.getPagemodel().setLabel("线路主题");
        }
        if (FliggySwitchConfig.customTripCats.contains(Long.parseLong(catId))) {
            compDO.getPagemodel().setLabel("推荐主题");
        }
    }

    @Enable(key = "play-fliggy-add-text", desc = "玩法文案提醒")
    public void addText(CompExtParam param, AbstractCompDO<String> compDO) {
        if (null == compDO.getPagemodel()) {
            return;
        }
        InfoModel infoModel = new InfoModel();
        int catId = FliggyParamUtil.getCategoryId(param);
        PlayConfigModel playConfigModel = playConfigDiamondProcessor.getPlayConfigModel(catId);
        StringBuilder tips = new StringBuilder();
        tips.append("<span style='line-height:32px'>");
        // 如果有新玩法，则使用新玩法
        if (null != playConfigModel && !FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(catId))) {
            tips.append("最多勾选").append(playConfigModel.getPlayNum()).append("个玩法,");
        }
        if (FliggySwitchConfig.useNewPlayCategoryMap.containsKey(Long.valueOf(catId))) {
            tips.append("最多勾选").append(FliggySwitchConfig.useNewPlayCategoryMap.get(Long.valueOf(catId))).append("个玩法,");
        }
        tips.append(PlayTipsUtils.getTips(FliggyParamUtil.getUserDO(param)));
        tips.append("</span>");
        infoModel.setRight(Lists.newArrayList(tips.toString()));
        compDO.getPagemodel().setInfo(infoModel);
    }

    @Check(key = "play-playProp-check-rule", desc = "玩法必填校验")
    public CheckResult addCheckRule(CompExtParam param, AbstractCompDO<List<TextValueModel>> compDO) {
        CheckResult result = new CheckResult();
        int categoryId = Integer.valueOf(param.getReq().getSystemParam().getString("catId"));
        // 酒景top接口不做校验
        if (FliggyParamUtil.isTopSource(param.getReq()) && FliggySwitchConfig.hotelSceneryCatId == categoryId) {
            return result;
        }
        if (travelSellConfig.getGuoneiGentuanyouCatId() == categoryId) {
            BasicCompDO<Integer> dayTourTypeCompDO = param.getDependCompDO("dayTourType");
            Integer value = dayTourTypeCompDO.getValue();
            if (value == 1) {
                if (FliggySwitchConfig.playRequired && CollectionUtils.isEmpty(compDO.getValue())) {
                    result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("playProp",
                            "玩法不可为空，请修改后再发布"));
                }
            }
        } else {
            if (FliggySwitchConfig.playRequired && CollectionUtils.isEmpty(compDO.getValue())) {
                result.addErrorCode(FliggyErrorEnum.SYS_PARAM_CHECK_FAILED.getErrorCode("playProp",
                        "玩法不可为空，请修改后再发布"));
            }
        }
        return result;
    }

    @PrepareForRender(key = "play-add-invalid-info", desc = "失效玩法加提示且删除values")
    public void addInvalidInfo(CompExtParam param, AbstractCompDO<List<TextValueModel>> compDO) {
        if (null == compDO.getPagemodel()) {
            return;
        }
        if (!FliggySwitchConfig.useNewPlayCategoryMap.containsKey((long) FliggyParamUtil.getCategoryId(param))) {
            return;
        }
        if (!GraySwitchUtils.isOnFor(FliggyGraySwitchConfig.playInteractionOptimizeSwitch, FliggyParamUtil.getUserId(param))) {
            return;
        }
        InfoModel infoModel = compDO.getPagemodel().getInfo();
        List<TextValueModel> value = compDO.getValue();
        if (CollectionUtils.isEmpty(value)) {
            return;
        }
        Iterator<TextValueModel> iterator = value.iterator();
        // 如果玩法已经失效，则删除values且增加提示
        while (iterator.hasNext()) {
            TextValueModel playProp = iterator.next();
            if (!NumberUtils.isDigits(playProp.getValue())) {
                iterator.remove();
            } else {
                Long playPropId = Long.valueOf(playProp.getValue());
                PlayType playType = playTypeSwitchServiceRepo.getPlayType(playPropId, FliggyParamUtil.getUserId(param));
                if (playType == null) {
                    iterator.remove();
                    String playPropValue = playProp.getText();
                    if (StringUtils.isNotBlank(playPropValue)) {
                        infoModel.addBottom(String.format(FliggySwitchConfig.PLAY_PROP_INVALID_INFO, playPropValue));
                    }
                }
            }
        }
    }

    @Check(key = "check-play-type-is-valid", desc = "校验玩法是否有效/改名")
    public CheckResult checkPlayTypeIsValid(CompExtParam param, AbstractCompDO<List<TextValueModel>> compDO) {
        CheckResult result = new CheckResult();
        long userId = FliggyParamUtil.getUserId(param);
        int categoryId = FliggyParamUtil.getCategoryId(param);
        if (!FliggySwitchConfig.useNewPlayCategoryMap.containsKey((long) FliggyParamUtil.getCategoryId(param))) {
            return result;
        }
        // 校验是否切流中，非切流，则直接返回
        if (!GraySwitchUtils.isOnFor(FliggyGraySwitchConfig.playInteractionOptimizeSwitch, userId)) {
            return result;
        }
        List<TextValueModel> playPropList = compDO.getValue();
        if (CollectionUtils.isEmpty(playPropList)) {
            return result;
        }
        for (TextValueModel playProp: playPropList) {
            // id并非数字，意味前端有bug
            if (!NumberUtils.isDigits(playProp.getValue())) {
                continue;
            }

            // 查询id对应的玩法
            PlayType playType = playTypeSwitchServiceRepo.getPlayType(Long.valueOf(playProp.getValue()), userId);
            if (playType == null || !Objects.equals(playType.getName(), playProp.getText())) {
                // 校验玩法的标题, 玩法词「****」已失效，请重新选择！
                result.addErrorCode(FliggyErrorEnum.PLAY_PROP_INVALID.getErrorCode(compDO.getCompName()).putErrorParam("playProp",  playProp.getText()));
            }
        }
        return result;
    }
}
