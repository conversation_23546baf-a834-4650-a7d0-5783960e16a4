package com.taobao.gpf.domain.component.prebook;

import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alitrip.travel.common.util.QuaDateUtils;
import com.taobao.gpf.domain.publish.store.BaseFliggyCompStoreStrategy;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Slf4j
public class BookableStartTimeStoreStrategy extends BaseFliggyCompStoreStrategy<BasicCompDO<Date>> {


    @Override
    public String getName() {
        return "bookableStartTimeStoreStrategy";
    }

    @Override
    public void parseStore(BasicCompDO<Date> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        if (storeDO.getBcStartTime() != null) {
            //compDO.setValue(QuaDateUtils.dateToString(storeDO.getBcStartTime(), "yyyy-MM-dd HH:mm"));
            compDO.setValue(storeDO.getBcStartTime());
        }
    }

    @Override
    public void transferStore(BasicCompDO<Date> compDO, TravelItemStoreDO storeDO, CompExtParam param) {
        try {
            if (compDO.getValue() != null) {
                /*Date bcStartDate = QuaDateUtils.stringToDate(compDO.getValue(), "yyyy-MM-dd HH:mm");
                storeDO.setBcStartTime(bcStartDate);*/
                storeDO.setBcStartTime(compDO.getValue());
            }
        } catch (Exception e) {
            log.warn("method:bookableStartTimeStoreStrategy,e.getMessage():"+e.getMessage(),e);
        }

    }
}
