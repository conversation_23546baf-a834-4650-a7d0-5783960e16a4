package com.taobao.gpf.domain.component.poi;

import com.alibaba.gpf.base.publish.domain.extpoint.store.BaseCompStoreStrategy;
import com.alibaba.gpf.base.publish.sdk.storedo.WholeStoreDO;
import com.alibaba.gpf.sdk.compdo.BasicCompDO;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.taobao.gpf.domain.publish.store.TravelItemStoreDO;
import com.taobao.util.CollectionUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Created by maoxinming on 2019/4/22.
 */
public class ItemPoiStoreStragegy extends BaseCompStoreStrategy<BasicCompDO<String>> {

    @Override
    public void parseStore(BasicCompDO<String> compDO, WholeStoreDO storeDO, CompExtParam param) {
        TravelItemStoreDO travelItemStoreDO = (TravelItemStoreDO)storeDO ;
        Map<String, String> alitripFeatures = travelItemStoreDO.getAlitripFeatures();
        if (CollectionUtil.isNotEmpty(alitripFeatures) && StringUtils.isNotEmpty(alitripFeatures.get("poi"))) {
            compDO.setValue(alitripFeatures.get("poi"));
        }
    }

    @Override
    public void transferStore(BasicCompDO<String> compDO, WholeStoreDO storeDO, CompExtParam param) {
        TravelItemStoreDO travelItemStoreDO = (TravelItemStoreDO)storeDO ;
        if(StringUtils.isNotEmpty(compDO.getValue())){
            travelItemStoreDO.addAlitripFeature("poi",compDO.getValue());
        }

    }


    @Override
    public String getName() {
        return "itemPoiStoreStragegy";
    }
}
