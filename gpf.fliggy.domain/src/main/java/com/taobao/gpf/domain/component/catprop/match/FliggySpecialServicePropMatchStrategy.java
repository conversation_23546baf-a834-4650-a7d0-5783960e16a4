package com.taobao.gpf.domain.component.catprop.match;

import javax.annotation.Resource;

import com.alibaba.gpf.base.publish.sdk.extpoint.ICompTemplateMatchStrategy;
import com.alibaba.gpf.base.publish.sdk.param.MatchParam;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryPropertyDO;
import com.taobao.gpf.domain.config.TravelSellConfig;

/**
 * @description:特色服务
 * <AUTHOR>
 */
public class FliggySpecialServicePropMatchStrategy implements ICompTemplateMatchStrategy<StdCategoryPropertyDO> {


    @Resource
    private TravelSellConfig travelSellConfig;



    @Override
    public boolean match(MatchParam param, StdCategoryPropertyDO stdCategoryPropertyDO) {
        //TODO
        return stdCategoryPropertyDO.getPropertyId() == travelSellConfig.getSpecialServicePid();
    }

    @Override
    public String getName() {
        return "fliggySpecialServicePropMatchStrategy";
    }
}