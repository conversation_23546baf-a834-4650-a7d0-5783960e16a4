package com.taobao.gpf.domain.component.sku.strategy;

import com.alibaba.gpf.base.publish.domain.component.sku.SkuTableCompDO;
import com.alibaba.gpf.base.publish.sdk.storedo.WholeStoreDO;
import com.alibaba.gpf.sdk.storedo.StoreDO;
import com.alibaba.gpf.shared.extpoint.store.ReflectTableStoreStrategy;
import com.taobao.gpf.domain.component.sku.FliggySkuRowStoreDO;

/**
 * @description:
 * <AUTHOR>
 */

public class FliggySkuTableReflectStoreStrategy<TABLE extends SkuTableCompDO<?,?>,STORE extends WholeStoreDO> extends ReflectTableStoreStrategy<TABLE,STORE> {

    @Override
    public String getName() {
        return "fliggySkuTableReflectStoreStrategy";
    }


    @Override
    public StoreDO createRowStoreDO(TABLE compDO, STORE tableStoreDO) {
        return new FliggySkuRowStoreDO();
    }
}
