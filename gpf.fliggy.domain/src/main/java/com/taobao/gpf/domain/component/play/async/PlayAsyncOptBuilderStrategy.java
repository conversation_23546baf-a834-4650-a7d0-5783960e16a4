package com.taobao.gpf.domain.component.play.async;

import com.alibaba.gpf.business.domain.util.ParamUtil;
import com.alibaba.gpf.sdk.compdo.AbstractCompDO;
import com.alibaba.gpf.sdk.dataobject.ControlDO;
import com.alibaba.gpf.sdk.model.AsyncOptModel;
import com.alibaba.gpf.sdk.model.pagemodel.TextValueModel;
import com.alibaba.gpf.sdk.param.CompExtParam;
import com.alibaba.gpf.shared.extpoint.build.BaseAsyncOptBuilderStrategy;

import java.util.List;

/**
 * @description: 玩法的异步url的
 * <AUTHOR>
 */
public class PlayAsyncOptBuilderStrategy extends BaseAsyncOptBuilderStrategy<AbstractCompDO<List<TextValueModel>>> {

    @Override
    public String getName() {
        return "playAsyncOptBuilderStrategy";
    }

    @Override
    protected String replaceUrl(String url, CompExtParam param, AbstractCompDO<List<TextValueModel>> compDO, AsyncOptModel model) {
        //替换url中的各种参数
        long categoryId = ParamUtil.getCategoryDO(param).getCategoryId();
        return url.replace("{catId}", categoryId + "");
    }

}
