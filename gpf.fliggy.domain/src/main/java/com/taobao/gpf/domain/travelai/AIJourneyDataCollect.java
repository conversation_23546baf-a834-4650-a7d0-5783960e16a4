package com.taobao.gpf.domain.travelai;

import com.taobao.gpf.domain.ots.annotation.OtsColumn;
import com.taobao.gpf.domain.ots.annotation.OtsPrimaryKey;
import com.taobao.gpf.domain.ots.annotation.OtsTableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@OtsTableName("ai_journey_data_collect")
public class AIJourneyDataCollect implements Serializable {

    private static final long serialVersionUID = -5826669543674743512L;

    /**
     * 行程ID
     */
    @OtsPrimaryKey(order = 1)
    @OtsColumn("journey_id")
    private String journeyId;

    @OtsPrimaryKey(order = 2)
    @OtsColumn(value = "id", autoIncrement = true)
    private String id;

    /**
     * 行程第几天
     */
    @OtsColumn("day_no")
    private Integer dayNo;


    @OtsColumn("llm_result")
    private String llmResult;

    @OtsColumn("search_result")
    private String searchResult;

    @OtsColumn("final_result")
    private String finalResult;

    @OtsColumn("element_key")
    private String elementKey;

    @OtsColumn("type")
    private String type;

    @OtsColumn("env")
    private String env;

    @OtsColumn("gmt_create")
    private String gmtCreate;

    @OtsColumn("seller_id")
    private String sellerId;

    @OtsColumn("document_id")
    private String documentId;


}
