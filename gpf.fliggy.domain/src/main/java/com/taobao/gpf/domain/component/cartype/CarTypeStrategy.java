package com.taobao.gpf.domain.component.cartype;

import com.alibaba.fastjson.JSON;
import com.alibaba.gpf.sdk.util.JacksonUtil;
import com.alitrip.travel.travelitems.model.structured.Combo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.taobao.gpf.domain.component.cartype.service.CarTypeConfig;
import com.taobao.gpf.domain.component.cartype.service.CarTypeFeatureModel;
import com.taobao.gpf.domain.component.cartype.service.CarTypePicture;
import com.taobao.gpf.domain.component.cartype.service.VacancyValueEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/13 19:31
 */
@Component
public class CarTypeStrategy {

    public void transCarTypeInner(JsonNode comboJson, Combo combo,Map<Integer, CarTypeConfig> configMap){
        Map<String, String> comboFeatures = combo.getComboFeatures();
        CarTypeFeatureModel featureModel = transCarTypeFeature(comboJson, configMap);
        if(CollectionUtils.isNotEmpty(featureModel.getCarType())){
            comboFeatures.put("carType", JSON.toJSONString(featureModel.getCarType().get(0)));
        }
        if(CollectionUtils.isNotEmpty(featureModel.getCarTypePicture())){
            comboFeatures.put("carTypePicture", JSON.toJSONString(featureModel.getCarTypePicture()));
        }
        if(Strings.isNotBlank(featureModel.getVacancyRate())){
            comboFeatures.put("vacancyRate", featureModel.getVacancyRate());

        }
        comboFeatures.put("carTypes", JSON.toJSONString(featureModel));
    }

    public CarTypeFeatureModel transCarTypeFeature(JsonNode comboJson, Map<Integer, CarTypeConfig> configMap){
        CarTypeFeatureModel featureModel = new CarTypeFeatureModel();
        JsonNode carTypeJson = comboJson.path("carType");
        if(carTypeJson != null && !carTypeJson.isMissingNode()){
            featureModel.setCarType(new ArrayList<CarTypeConfig>());
            if(carTypeJson.isArray()){
                for (JsonNode carType : carTypeJson) {
                    if(carType.path("value").isInt()){
                        CarTypeConfig carTypeConfig = configMap.get(carType.path("value").asInt());
                        featureModel.getCarType().add(carTypeConfig);
                    }
                }
            }else if(carTypeJson.path("value").isInt()){
                //兼容单选
                CarTypeConfig carTypeConfig = configMap.get(carTypeJson.path("value").asInt());
                featureModel.getCarType().add(carTypeConfig);
            }
        }

        JsonNode vacancyRateJson = comboJson.path("vacancyRate");
        if (vacancyRateJson != null && !vacancyRateJson.isMissingNode() && vacancyRateJson.get("value") != null) {
            //兼容历史数据
            featureModel.setVacancyRate(vacancyRateJson.get("value").asText());
        }
        JsonNode carTypePicture = comboJson.path("carTypePicture");
        if (carTypePicture != null && !carTypePicture.isMissingNode()) {
            featureModel.setCarTypePicture(new ArrayList<>());
            if(carTypePicture.isArray()){
                for (JsonNode picture : carTypePicture) {
                    CarTypePicture p = JSON.parseObject(picture.toString(), CarTypePicture.class);
                    featureModel.getCarTypePicture().add(p);
                }
            }else if(carTypePicture.isObject()){
                CarTypePicture e = JSON.parseObject(carTypePicture.toString(), CarTypePicture.class);
                featureModel.getCarTypePicture().add(e);
            }
        }
        return featureModel;
    }

    public void parseCarTypeInner(ObjectNode comboJson, Combo combo){
        CarTypeFeatureModel featureModel = parseCarTypeFeature(combo);
        if(CollectionUtils.isNotEmpty(featureModel.getCarType())){
            ArrayNode carTypeArray = JacksonUtil.createArrayNode();
            for (CarTypeConfig carTypeConfig : featureModel.getCarType()) {
                ObjectNode carTypeNode = JacksonUtil.createObjectNode();
                carTypeNode.put("value", carTypeConfig.getId());
                carTypeNode.put("text", carTypeConfig.getName());
                carTypeArray.add(carTypeNode);
            }
            comboJson.put("carType", carTypeArray);
        }
        if(Strings.isNotBlank(featureModel.getVacancyRate())){
            VacancyValueEnum vacancyRate = vacancyParseRateMergeHistoricalData(featureModel.getVacancyRate());
            if (vacancyRate != null) {
                ObjectNode vacancyRateJson = JacksonUtil.createObjectNode();
                vacancyRateJson.put("value", vacancyRate.getValue());
                vacancyRateJson.put("text", vacancyRate.getText());
                comboJson.put("vacancyRate", vacancyRateJson);
            }
        }
        if(CollectionUtils.isNotEmpty(featureModel.getCarTypePicture())){
            ArrayNode carTypePictureArray = JacksonUtil.createArrayNode();
            for (CarTypePicture carTypePicture : featureModel.getCarTypePicture()) {
                carTypePictureArray.addPOJO(carTypePicture);
            }
            comboJson.put("carTypePicture", carTypePictureArray);
        }
    }

    public CarTypeFeatureModel parseCarTypeFeature(Combo combo){
        Map<String, String> comboFeatures = combo.getComboFeatures();
        CarTypeFeatureModel featureModel = new CarTypeFeatureModel();
        if(comboFeatures.containsKey("carTypes")){
            try{
                return JSON.parseObject(comboFeatures.get("carTypes"), CarTypeFeatureModel.class);
            }catch (Exception e){
                return featureModel;
            }
        }else{
            Integer carType = null;
            if(comboFeatures.containsKey("carType")){
                featureModel.setCarType(new ArrayList<>());
                CarTypeConfig carTypeConfig = JSON.parseObject(comboFeatures.get("carType"), CarTypeConfig.class);
                if(carTypeConfig != null){
                    carType = carTypeConfig.getId();
                }
                featureModel.getCarType().add(carTypeConfig);
            }
            if(comboFeatures.containsKey("carTypePicture")){
                featureModel.setCarTypePicture(new ArrayList<>());
                List<CarTypePicture> carTypePictures = JSON.parseArray(comboFeatures.get("carTypePicture"), CarTypePicture.class);
                if(CollectionUtils.isNotEmpty(carTypePictures)){
                    for (CarTypePicture carTypePicture : carTypePictures) {
                        carTypePicture.setCarType(carType);
                        featureModel.getCarTypePicture().add(carTypePicture);
                    }
                };

            }
            if(comboFeatures.containsKey("vacancyRate")){
                featureModel.setVacancyRate(comboFeatures.get("vacancyRate"));
            }
            return featureModel;
        }
    }

    /**
     *
     * @param vacancyRateValue
     * 新数据：
     * 1001,"每人一正座"
     * 1002,"空座率＜10%"
     * 1003,"10%≤空座率＜20%"
     * 1004,"20%≤空座率＜30%"
     * 1005,"30%≤空座率＜50%"
     * 1006,"50%≤空座率"
     * @return
     */
    private VacancyValueEnum vacancyParseRateMergeHistoricalData(String vacancyRateValue) {
        if (StringUtils.isBlank(vacancyRateValue) || "null".equals(vacancyRateValue)) {
            return null;
        } else {
            /*
            因历史数据对数值没有做检验，当数据大于100应为无效数据，现为了兼容历史数据大于50的并且数值不在1001-1006范围内的数值都转化为大于50%
            当在1001-1006范围内的数值，在允许丢失精度的情况下，转化为新的枚举值列表，对数据进行转化
             */
            Double vacancyRate = Double.valueOf(vacancyRateValue);
            VacancyValueEnum vacancyValue = VacancyValueEnum.getVacancyValue(vacancyRate.intValue());
            if (vacancyValue != null) {
                return vacancyValue;
            }
            Integer vacancyRateInt;
            if (vacancyRate <= 0) {
                vacancyValue = VacancyValueEnum.PERCENT_ZERO ;
            } else if (vacancyRate < 10) {
                vacancyValue = VacancyValueEnum.PERCENT_LESSTHANTEN ;
            } else if (vacancyRate < 20) {
                vacancyValue = VacancyValueEnum.PERCENT_TENTOTWENTY ;
            } else if (vacancyRate < 30) {
                vacancyValue = VacancyValueEnum.PERCENT_TWENTYTOTHIRTY ;
            } else if (vacancyRate < 50) {
                vacancyValue = VacancyValueEnum.PERCENT_THIRTYTOFIFTY ;
            } else {
                vacancyValue = VacancyValueEnum.PERCENT_MORETHANFIFTY ;
            }
            return vacancyValue;
        }
    }
}
