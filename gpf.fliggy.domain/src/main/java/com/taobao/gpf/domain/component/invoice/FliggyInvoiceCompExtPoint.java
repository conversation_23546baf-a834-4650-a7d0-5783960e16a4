package com.taobao.gpf.domain.component.invoice;

import com.alibaba.gpf.business.domain.annotation.bind.Category;
import com.alibaba.gpf.sdk.annotation.ext.Enable;
import com.alibaba.gpf.sdk.extpoint.inject.IInjectExtension;
import com.alibaba.gpf.sdk.param.CompExtParam;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.utils.FliggyParamUtil;
import com.taobao.gpf.domain.utils.UserUtil;
import com.taobao.uic.common.domain.BaseUserDO;

/**
 * @description:发票扩展项
 * <AUTHOR>
 */
public class FliggyInvoiceCompExtPoint implements IInjectExtension {

    @Enable(key = "invoice-fliggy-enable", desc = "是否启用发票")
    public boolean setCompEnable(CompExtParam param, @Category StdCategoryDO categoryDO) {
        final BaseUserDO loginUser = FliggyParamUtil.getUserDO(param);
        return null != loginUser && !UserUtil.isB2CSeller(loginUser);
    }
}
