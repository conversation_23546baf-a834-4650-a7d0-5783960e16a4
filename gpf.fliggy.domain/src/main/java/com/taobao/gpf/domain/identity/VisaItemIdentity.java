package com.taobao.gpf.domain.identity;

import com.alibaba.gpf.sdk.exception.GpfException;
import com.alibaba.gpf.sdk.param.IdentityParam;
import com.alibaba.gpf.sdk.util.LogUtil;
import com.alibaba.gpf.shared.extpoint.BaseAppBizIdentity;
import com.alibaba.gpf.shared.util.BeanUtil;

import com.taobao.forest.domain.dataobject.std.read.StdCategoryDO;
import com.taobao.gpf.domain.config.TravelSellConfig;
import com.taobao.gpf.domain.exception.FliggyErrorEnum;
import com.taobao.gpf.domain.publish.init.FliggyRequiredObject;
import lombok.extern.slf4j.Slf4j;


/**
 * @description:
 * <AUTHOR>
 */

@Slf4j
public class VisaItemIdentity extends BaseAppBizIdentity {

    @Override
    public boolean isFitByCustom(IdentityParam identityParam) {
        StdCategoryDO stdCategoryDO = ((FliggyRequiredObject)identityParam.getWro()).getCategory() ;
        if(null == stdCategoryDO){
            LogUtil.sysErrorLog("VisaItemIdentity.isFitByCustom","stdCategoryDO is null");
            return false;
        }
        TravelSellConfig travelSellConfig = BeanUtil.getBean("travelSellConfig",TravelSellConfig.class);
        if(null == travelSellConfig){
            LogUtil.sysErrorLog("VisaItemIdentity.isFitByCustom","get bean(travelSellConfig) is null");
            throw new GpfException(FliggyErrorEnum.SYS_ERROR.getErrorCode());
        }
        return travelSellConfig.isVisaCat(stdCategoryDO.getCategoryId());
    }
}
