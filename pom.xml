<?xml version="1.0" encoding="utf-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.0</version>
    </parent>
    <groupId>com.taobao.gpf</groupId>
    <artifactId>gpf.fliggy</artifactId>
    <packaging>pom</packaging>
    <version>1.0.13</version>
    <name>gpf.fliggy</name>
    <modules>
        <module>gpf.fliggy.aspect</module>
        <module>gpf.fliggy.common</module>
        <module>gpf.fliggy.client</module>
        <module>gpf.fliggy.lite</module>
        <module>gpf.fliggy.domain</module>
        <module>gpf.fliggy.service</module>
        <module>gpf.fliggy.apps</module>
        <module>gpf.fliggy.web</module>
        <module>gpf.fliggy.biz</module>
        <module>gpf.fliggy.sdk</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.version>4.3.16.RELEASE</spring.version>
        <pandora-boot.version>2021-11-release</pandora-boot.version>

        <gpf-framework-version>4.3.2-visual</gpf-framework-version>
        <gpf-business-version>4.4.2-visual</gpf-business-version>
        <metis-version>1.4.4-filggy-SNAPSHOT</metis-version>

        <fliggy-solution-version>1.0.13</fliggy-solution-version>

        <fliggy-client-version>1.0.25</fliggy-client-version>
        <itemcenterclient.version>3.33.2</itemcenterclient.version>
        <gpf-manager-version>3.0.1-gpf3.8-SNAPSHOT</gpf-manager-version>
        <travelitems-common-version>2.03.20</travelitems-common-version>
        <travelitems-client-version>2.02.86</travelitems-client-version>
        <travelitems-platform-version>3.01.61</travelitems-platform-version>
        <travel-ticket-app-version>3.01.05</travel-ticket-app-version>
        <traveltic-biz-version>1.6.69</traveltic-biz-version>
        <traveltic-client-version>1.8.33</traveltic-client-version>
        <traveltic-domain-version>1.8.4</traveltic-domain-version>
        <traveltic-match-version>1.8.1</traveltic-match-version>
        <traveltic-search-version>1.8.1</traveltic-search-version>
        <scm-product-version>*********</scm-product-version>
        <hsac-client-version>1.3.2</hsac-client-version>
        <fsc-purchase-version>1.0.15</fsc-purchase-version>
        <fsc-client-basic-version>1.0.2</fsc-client-basic-version>
        <aspectj.version>1.8.13</aspectj.version>
        <travel-systemvendor-version>1.1.5</travel-systemvendor-version>
        <linecenter.api.version>1.0.6</linecenter.api.version>
        <charter-car-client-version>1.0.1</charter-car-client-version>
        <tripcenter-client-version>3.25.21</tripcenter-client-version>
        <tripcenter-common-version>3.1.45</tripcenter-common-version>
        <fic-common.version>1.0.99</fic-common.version>
        <fic-biz-common.version>1.0.52</fic-biz-common.version>
        <jpbb-client.version>2.5.8</jpbb-client.version>
        <nspurchase-client.version>1.1.7</nspurchase-client.version>
        <tuan-merchant-api.version>1.0.6</tuan-merchant-api.version>
        <tuan-item-api.version>1.0.13</tuan-item-api.version>
        <tuan-data-api.version>1.1.2</tuan-data-api.version>
        <tuan-data-model.version>1.0.7</tuan-data-model.version>
        <tuan-item-api-hotel.version>1.0.30</tuan-item-api-hotel.version>
        <tuan-item-model.version>1.0.49</tuan-item-model.version>
        <tuan-boot-starter-rule-engine.version>1.0.5</tuan-boot-starter-rule-engine.version>
        <travelvc-client-version>1.1.61</travelvc-client-version>
        <fliggy-settle-client.version>1.0.90</fliggy-settle-client.version>
        <hta-client.version>1.1.51</hta-client.version>
        <tablestore.version>5.13.11</tablestore.version>
        <aliyun-security-client-ots.version>1.0.3</aliyun-security-client-ots.version>
        <hutool.version>5.8.22</hutool.version>
        <travel-xiaoer.version>1.0.44</travel-xiaoer.version>
        <flybd-client-version>1.3.40</flybd-client-version>
        <atssf-client-version>2.1.2-SNAPSHOT</atssf-client-version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alitrip.atssf</groupId>
                <artifactId>atssf-client</artifactId>
                <version>${atssf-client-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.trip.gds</groupId>
                        <artifactId>gds-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>buc.sso.client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>buc.acl.api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>buc.acl.client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-mc-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.inventory</groupId>
                        <artifactId>iphotel-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fliggy.flight.flybd</groupId>
                <artifactId>flybd-client-api</artifactId>
                <version>${flybd-client-version}</version>
            </dependency>

            <!-- gpf.sdk start -->
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.publish</artifactId>
                <version>${gpf-framework-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel</groupId>
                <artifactId>travel-xiaoer-client</artifactId>
                <version>${travel-xiaoer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.admin</artifactId>
                <version>${gpf-framework-version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>buc.sso.client.plugin</artifactId>
                        <groupId>com.taobao.pandora.plugin</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.bob</artifactId>
                <version>${gpf-framework-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.stable</artifactId>
                <version>${gpf-framework-version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>vipserver-client</artifactId>
                        <groupId>com.taobao.vipserver</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.manager.service</artifactId>
                <version>${gpf-framework-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.manager.domain</artifactId>
                <version>${gpf-framework-version}</version>
            </dependency>
            <!-- gpf.sdk  end -->

            <!-- gpf.business start -->

            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.business.publish.item.domain</artifactId>
                <version>${gpf-business-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jetbrains</groupId>
                        <artifactId>annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-jelly</groupId>
                        <artifactId>commons-jelly</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware</groupId>
                        <artifactId>hsf-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.category</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.shopcenter</groupId>
                        <artifactId>core-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.istack</groupId>
                        <artifactId>istack-commons-runtime</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.fastinfoset</groupId>
                        <artifactId>FastInfoset</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.shopcenter</groupId>
                <artifactId>core-client</artifactId>
                <version>4.9.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.shopcenter</groupId>
                <artifactId>common-client</artifactId>
                <version>3.5.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.common.division</groupId>
                <artifactId>common-division-plus</artifactId>
                <version>4.1.2</version>
            </dependency>

            <!-- gpf.business end -->

            <!-- gpf-top  start-->
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.top.service</artifactId>
                <version>${gpf-framework-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>gpf.base.top.domain</artifactId>
                <version>${gpf-framework-version}</version>
            </dependency>
            <!-- gpf-top  end-->


            <dependency>
                <groupId>com.alibaba.gpf</groupId>
                <artifactId>solution-runtime</artifactId>
                <version>${metis-version}</version>
            </dependency>

            <!-- pandora-boot -->
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>${pandora-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-loader</artifactId>
                <version>2.1.11.1</version>
            </dependency>


            <!-- 之前在gpf.business内部依赖的包 -->
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>22.0.0</version>
            </dependency>
            <!-- end -->

            <!-- gpf-fliggy start-->
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.common</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.domain</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.lite</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.client</artifactId>
                <version>${fliggy-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.aspect</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.service</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.biz</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-fliggy</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-recharge</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-tripcar</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-chartercar</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-visa</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-dive</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-line</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-line-abroad</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-ticket</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-cruise</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-phonecard</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-play</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-play-abroad</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-commons</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-general</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-daytour</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-coupon</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-globaltour</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-hotel</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-collage</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-flyjpbb</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-traffic-abroad</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.gpf</groupId>
                <artifactId>gpf.fliggy.app-line-customized</artifactId>
                <version>${fliggy-solution-version}</version>
            </dependency>
            <!-- gpf-fliggy end-->
            <dependency>
                <groupId>com.fliggy.travel.guide</groupId>
                <artifactId>travel-guide-api</artifactId>
                <version>1.2.31</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.external</groupId>
                <artifactId>jakarta.commons.collections</artifactId>
                <version>999-not-exist</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travel-ticket-app</artifactId>
                <version>${travel-ticket-app-version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>traveladmin-api</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.remus</groupId>
                <artifactId>china-rc-client</artifactId>
                <version>1.0.4</version>
            </dependency>
            <!-- common-division运行时强依赖 -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-messaging</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-pool</groupId>
                <artifactId>commons-pool</artifactId>
                <version>1.5.5</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-commons</artifactId>
                <version>1.0.15</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.10</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <!-- taobao 三方包 -->
            <dependency>
                <groupId>com.taobao.util</groupId>
                <artifactId>util</artifactId>
                <version>1.2.6</version>
            </dependency>
            <!-- HSF sdk-->
            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>hsf-sdk</artifactId>
                <version>3.1.4.2--2022-07-release</version>
            </dependency>
            <!-- 图片空间 -->
            <dependency>
                <groupId>com.taobao.picturecenter</groupId>
                <artifactId>pic-client</artifactId>
                <version>1.9.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson-core-asl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson-mapper-asl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jruby</groupId>
                        <artifactId>jruby-complete</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.picturecenter</groupId>
                <artifactId>pic-common</artifactId>
                <version>1.8.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.common.tfs</groupId>
                <artifactId>tfs-client-restful</artifactId>
                <version>1.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.alitrip.hotel</groupId>
                <artifactId>hitop-client</artifactId>
                <version>1.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-gateway-seller-api</artifactId>
                <version>1.0.20</version>
            </dependency>
            <!--淘视频-->
            <dependency>
                <groupId>com.taobao.video</groupId>
                <artifactId>videoapi-common</artifactId>
                <version>2.0.12</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.wireless</groupId>
                        <artifactId>mtop-common-service</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--IC client -->
            <dependency>
                <groupId>com.taobao.itemcenter</groupId>
                <artifactId>itemcenter-client</artifactId>
                <version>${itemcenterclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-jexl</groupId>
                        <artifactId>commons-jexl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.notify.spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.wlb.account</groupId>
                        <artifactId>wlb-account-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>xalan</groupId>
                        <artifactId>xalan</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.liaoyuan</groupId>
                        <artifactId>liaoyuan-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jboss.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <!-- 采用独立依赖 -->
                        <groupId>com.taobao.delivery</groupId>
                        <artifactId>delivery-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.place</groupId>
                        <artifactId>place-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.place</groupId>
                <artifactId>place-client</artifactId>
                <version>2.5.2</version>
            </dependency>
            <!--New IC client -->
            <dependency>
                <groupId>com.taobao.itemcenter</groupId>
                <artifactId>itemcenter-common</artifactId>
                <version>${itemcenterclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.citrus</groupId>
                        <artifactId>citrus-webx-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>apache-codec</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.taxcenter</groupId>
                        <artifactId>taxcenter-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.mina</groupId>
                        <artifactId>mina-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.tissot</groupId>
                        <artifactId>tissot-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jruby</groupId>
                        <artifactId>jruby-complete</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cainiao</groupId>
                        <artifactId>cndcp-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.util</groupId>
                        <artifactId>taobao-express</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.notify.spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.services</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf.hessian</groupId>
                        <artifactId>hessian</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tddl</groupId>
                        <artifactId>tddl-sequence</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.catserver</groupId>
                        <artifactId>millau-bo</artifactId>
                    </exclusion>
                    <exclusion>
                        <!-- terminator 带的 -->
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.app.spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <!-- pricecenter-common依赖进来的 -->
                        <groupId>com.taobao.thboss</groupId>
                        <artifactId>thboss-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <!-- scsku 依赖进来的-->
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>fel</groupId>
                        <artifactId>fel-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-fileupload</groupId>
                        <artifactId>commons-fileupload</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.service</groupId>
                        <artifactId>price-service-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.fge</groupId>
                        <artifactId>json-schema-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cloning</artifactId>
                        <groupId>uk.com.robust-it</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>LightApi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.itemcenter</groupId>
                <artifactId>itemcenter-service-model</artifactId>
                <version>${itemcenterclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 运费模板 -->
            <dependency>
                <groupId>com.taobao.delivery</groupId>
                <artifactId>delivery-common</artifactId>
                <version>4.0.0.13</version>
                <exclusions>
                    <exclusion>
                        <groupId>aspectj</groupId>
                        <artifactId>aspectjrt</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.toolkit.webx</groupId>
                        <artifactId>webx.filter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.trade.platform</groupId>
                        <artifactId>tp-client-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.gecko</groupId>
                        <artifactId>gecko</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.ump</groupId>
                        <artifactId>ump-common-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.item.darwin</groupId>
                        <artifactId>darwin-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.promotion</groupId>
                        <artifactId>tmallpromotion-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.promotion</groupId>
                        <artifactId>tmallpromotion-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.trade.platform</groupId>
                        <artifactId>tp-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.ump</groupId>
                        <artifactId>ump-core-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.app.spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>LightApi</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.delivery</groupId>
                <artifactId>warehouse-route-client</artifactId>
                <version>1.4.5</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.inventory</groupId>
                <artifactId>ip-commons</artifactId>
                <version>5.7.86</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.inventory</groupId>
                <artifactId>ip-client</artifactId>
                <version>5.7.86</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.wlb</groupId>
                        <artifactId>wlb-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.terminator</groupId>
                        <artifactId>terminator-client-standalone</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.monitor</groupId>
                        <artifactId>common-monitor</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>quartz</groupId>
                        <artifactId>quartz</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tddl</groupId>
                        <artifactId>tddl-sequence</artifactId>
                    </exclusion>
                    <!--		##ic强依赖ip, ip强依赖 sic-client.xml, 而sic-client强依赖cdc-common.StorePriorityCache -->
                    <exclusion>
                        <groupId>com.tmall.sic</groupId>
                        <artifactId>sic-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.cdc</groupId>
                        <artifactId>cdc-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.cdc</groupId>
                        <artifactId>cdc-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.metaq</groupId>
                        <artifactId>metaq-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-fileupload</groupId>
                        <artifactId>commons-fileupload</artifactId>
                    </exclusion>
                    <exclusion>
                        <!-- 这里排除, 走单独依赖 -->
                        <groupId>com.taobao.delivery</groupId>
                        <artifactId>delivery-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>LightApi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.category</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>halo-agent</artifactId>
                        <groupId>com.alibaba.halo</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <!-- ip client 强依赖 -->
                <groupId>com.taobao.scsku</groupId>
                <artifactId>scsku-common</artifactId>
                <version>1.2.3-final</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <!-- ip client 强依赖 -->
                <groupId>com.taobao.scsku</groupId>
                <artifactId>scsku-client</artifactId>
                <version>1.2.3-final</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- ip client 强依赖 -->
            <dependency>
                <groupId>com.tmall.sic</groupId>
                <artifactId>sic-client</artifactId>
                <version>1.5.82</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.mallitemcenter</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.sic</groupId>
                        <artifactId>sic-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.mallitemcenter</groupId>
                        <artifactId>mic-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.mallitemcenter</groupId>
                        <artifactId>mic-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.catserver</groupId>
                        <artifactId>catserver-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tmall.sic</groupId>
                <artifactId>sic-common</artifactId>
                <version>1.5.82</version>
            </dependency>
            <dependency>
                <!-- ip client 保依赖 -->
                <groupId>com.cainiao.cdc</groupId>
                <artifactId>cdc-common</artifactId>
                <version>1.2.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <!-- ip client 保依赖 -->
                <groupId>com.cainiao.cdc</groupId>
                <artifactId>cdc-client</artifactId>
                <version>1.2.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>********</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.mallitemcenter</groupId>
                <artifactId>mic-common</artifactId>
                <version>2.5.1</version>
                <!--
                                  - 传递依赖: com.taobao.scsku:scsku-client:jar:1.1.9-SNAPSHOT
                                  -          com.taobao.wlb:wlb-client:jar:*******-SNAPSHOT
                                  -          com.taobao.tmutil:tmutil:jar:1.0.1-SNAPSHOT
                                  - 升级时请同步升级传递依赖
                                  -->
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.division</groupId>
                        <artifactId>common-division</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cainiao</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 媒体(图片/视频)服务库 -->
            <dependency>
                <groupId>com.taobao.media</groupId>
                <artifactId>media-client</artifactId>
                <version>2.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>simpleimage</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 运费模板依赖, 邮寄组件依赖, 物流平台卖家用户 -->
            <dependency>
                <groupId>com.taobao.logistics</groupId>
                <artifactId>logistics-client</artifactId>
                <version>2.3.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.logisbus</groupId>
                        <artifactId>logisbus-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.wl</groupId>
                        <artifactId>wl-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <!-- 必须强制指定common版本,使client与common一致 -->
                <groupId>com.taobao.logistics</groupId>
                <artifactId>logistics-common</artifactId>
                <version>2.3.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.trade.platform</groupId>
                        <artifactId>tp-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.tmpp</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 税率计算 -->
            <dependency>
                <groupId>com.alibaba.taxcenter</groupId>
                <artifactId>taxcenter-client</artifactId>
                <version>1.2.42</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.csp</groupId>
                        <artifactId>switchcenter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-mc-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cainiao</groupId>
                        <artifactId>customsplatform-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- scm 货品相关-->
            <dependency>
                <groupId>com.alibaba.scm.item</groupId>
                <artifactId>scmitem-client</artifactId>
                <version>${scm-product-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.app.spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 地址库服务 -->
            <dependency>
                <groupId>com.taobao.common.division</groupId>
                <artifactId>common-division</artifactId>
                <version>3.0.28</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 米卢升级燎原 -->
            <dependency>
                <!-- 升级后沿用原DO -->
                <groupId>com.taobao.common.catserver</groupId>
                <artifactId>catserver-client</artifactId>
                <version>2.4.7</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware.millau2liaoyuan</groupId>
                <artifactId>millau2liaoyuan-client</artifactId>
                <version>1.1.1</version>
                <!-- 1.1.1 移除了jdbc依赖 -->
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.middleware.liaoyuan</groupId>
                        <artifactId>liaoyuan-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 子账号 -->
            <dependency>
                <groupId>com.taobao.mmp</groupId>
                <artifactId>mmp-client-web</artifactId>
                <version>3.6.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.mmp</groupId>
                <artifactId>mmp-client-core</artifactId>
                <version>3.6.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-mc-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 二次验证-->
            <dependency>
                <groupId>com.taobao.durex</groupId>
                <artifactId>durex-common</artifactId>
                <version>1.2.0</version>
            </dependency>
            <!--umid-->
            <dependency>
                <groupId>com.alibaba.umid</groupId>
                <artifactId>umid-client</artifactId>
                <version>1.0.20</version>
            </dependency>
            <!-- zzz -->
            <!-- 新版模块无线详情模板 -->
            <dependency>
                <groupId>com.taobao.detail</groupId>
                <artifactId>wireless-detail-model</artifactId>
                <version>2.0.4-multiText-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- https 相关 -->
            <dependency>
                <groupId>com.taobao.top</groupId>
                <artifactId>tbml-diamond-https</artifactId>
                <version>1.0.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 无线描述自动生成服务 -->
            <dependency>
                <groupId>com.taobao.detail</groupId>
                <artifactId>wireless-detail-desc</artifactId>
                <version>1.0.7-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <!-- 与ic的冲突 -->
                        <groupId>com.taobao.common.tfs</groupId>
                        <artifactId>tfs-client-restful</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.barcode</groupId>
                <artifactId>barcode-util</artifactId>
                <version>0.7</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>3.6.5.Final</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.68.noneautotype</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-access</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-utils</artifactId>
                <version>3.0.16</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.tair</groupId>
                <artifactId>tair-client</artifactId>
                <version>2.6.22</version>
            </dependency>
            <!-- 商家类目 -->
            <dependency>
                <groupId>com.alibaba.category</groupId>
                <artifactId>category-client</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.qdc</groupId>
                <artifactId>qualitydatacenter-common</artifactId>
                <version>1.5.1-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <artifactId>buc.sso.client</artifactId>
                        <groupId>com.alibaba.platform.shared</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-orm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-tx</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-asm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 达尔文系统 质量信息服务等等-->
            <dependency>
                <groupId>com.tmall.mip</groupId>
                <artifactId>mip-client</artifactId>
                <version>*******-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>darwin-algorithm-client</artifactId>
                        <groupId>com.tmall.darwin.algorithm</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>forest-client</artifactId>
                        <groupId>com.taobao.forest</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hsf.notify.spring</artifactId>
                        <groupId>com.taobao.hsf</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-beans</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>beehive-system</artifactId>
                        <groupId>com.tmall.beehive</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>beehive-util</artifactId>
                        <groupId>com.tmall.beehive</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>beehive-common</artifactId>
                        <groupId>com.tmall.beehive</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cglib-nodep</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>itemcenter-common</artifactId>
                        <groupId>com.taobao.itemcenter</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>millau-bo</artifactId>
                        <groupId>com.taobao.common.catserver</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>xalan</artifactId>
                        <groupId>xalan</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.taobao.quality</groupId>
                <artifactId>qualitycenter-common</artifactId>
                <version>1.0.3-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-orm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-tx</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-asm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ibm.icu</groupId>
                <artifactId>icu4j</artifactId>
                <version>58.2</version>
            </dependency>
            <!--饿了么对外API，参考 https://coding.net/u/napos_openapi/p/eleme-openapi-java-sdk/git -->
            <dependency>
                <groupId>me.ele.openapi</groupId>
                <artifactId>eleme-openapi-sdk</artifactId>
                <version>1.26.3</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-worker</artifactId>
                <version>1.11.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.brand</groupId>
                <artifactId>brand-sdk</artifactId>
                <version>1.2.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.goodsplatform</groupId>
                <artifactId>goodsplatform-client</artifactId>
                <version>1.2.9-forest-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>ip-model</artifactId>
                        <groupId>com.taobao.inventory</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>itemcenter-client</artifactId>
                        <groupId>com.taobao.itemcenter</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>ip-misc</artifactId>
                        <groupId>com.taobao.inventory</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tair-client</artifactId>
                        <groupId>com.taobao.tair</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tair-mc-client</artifactId>
                        <groupId>com.taobao.tair</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tagcenter-common</artifactId>
                        <groupId>com.taobao.tagcenter</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tagcenter-client</artifactId>
                        <groupId>com.taobao.tagcenter</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>forest-client</artifactId>
                        <groupId>com.taobao.forest</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-uic-common</artifactId>
                        <groupId>com.taobao.common.uic.uic-common</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>itemcenter-common</artifactId>
                        <groupId>com.taobao.itemcenter</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-keycenter-client</artifactId>
                        <groupId>com.taobao.common.keycenter.keycenter-client</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-keycenter-common</artifactId>
                        <groupId>com.taobao.common.keycenter.keycenter-common</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>faraday-logger-framework</artifactId>
                        <groupId>com.tmall.faraday.logger</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>core-client</artifactId>
                        <groupId>com.taobao.sellercenter</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>com.tmall.rac</artifactId>
                        <groupId>relationauth-client</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mtop-hsf-agent</artifactId>
                        <groupId>com.taobao.wireless</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- ea 账号 start-->
            <dependency>
                <groupId>com.taobao.session</groupId>
                <artifactId>easession</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.uni</groupId>
                <artifactId>ea-client</artifactId>
                <version>1.0.19-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.alimp</groupId>
                        <artifactId>bentley-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.alimp</groupId>
                <artifactId>bentley-client</artifactId>
                <version>2.4.80</version>
            </dependency>
            <!-- 天猫卖家授权 -->
            <dependency>
                <groupId>com.taobao.sellercenter</groupId>
                <artifactId>sellercenter-grant-client</artifactId>
                <version>1.0.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.util</groupId>
                        <artifactId>util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.toolkit.common.logging</groupId>
                        <artifactId>logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-mc-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- uic -->
            <dependency>
                <groupId>com.taobao.common.uic.uic-common</groupId>
                <artifactId>common-uic-common</artifactId>
                <version>3.8.4-isolation-release</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency><!-- 本地cache版本 -->
                <groupId>com.taobao.common.uic.uic-localcache</groupId>
                <artifactId>common-uic-localcache</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!-- 居然之家 -->
            <dependency>
                <groupId>com.alibaba.rt.thrones</groupId>
                <artifactId>thrones-api</artifactId>
                <version>0.5.11-jrcity-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.mallitemcenter</groupId>
                <artifactId>mic2-client</artifactId>
                <version>3.1.5-easyhome_city-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.tmall.spaceloop</groupId>
                <artifactId>spaceloop-client</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travel-client</artifactId>
                <version>1.10.10</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- travelitems -->
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitems-common</artifactId>
                <version>${travelitems-common-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.liaoyuan</groupId>
                        <artifactId>liaoyuan-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dts-client</artifactId>
                        <groupId>com.alibaba.dts</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>apache-collections</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>buc.sso.client</artifactId>
                        <groupId>com.alibaba.platform.shared</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.category</groupId>
                        <artifactId>category-package-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.category</groupId>
                        <artifactId>category-client-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitems-client</artifactId>
                <version>${travelitems-client-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.category</groupId>
                        <artifactId>category-package-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.category</groupId>
                        <artifactId>category-client-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitems-platform-common</artifactId>
                <version>${travelitems-platform-version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>traveltic-client</artifactId>
                        <groupId>com.taobao.traveltic</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.bbq</groupId>
                        <artifactId>bbq-execute</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.travel</groupId>
                        <artifactId>travelitems-platform-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alitrip.travel</groupId>
                <artifactId>fliggy-gateway-client</artifactId>
                <version>1.5.7</version>
            </dependency>

            <!-- Fic-->
            <dependency>
                <groupId>com.fliggy.fic</groupId>
                <artifactId>fic-common</artifactId>
                <version>${fic-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.fic</groupId>
                <artifactId>fic-client</artifactId>
                <version>${fic-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.fic</groupId>
                <artifactId>fic-biz-common</artifactId>
                <version>${fic-biz-common.version}</version>
            </dependency>

            <!-- Flyjpbb-->
            <dependency>
                <groupId>com.fliggy</groupId>
                <artifactId>jpbb-client</artifactId>
                <version>${jpbb-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-messaging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fliggy.nspurchase</groupId>
                <artifactId>nspurchase-client</artifactId>
                <version>${nspurchase-client.version}</version>
            </dependency>

            <!--卖点信息-->
            <dependency>
                <groupId>com.alibaba.fliggy.shopper.guide</groupId>
                <artifactId>guide-common</artifactId>
                <version>1.0.12</version>
            </dependency>
            <!--基础信息服务-->
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>tripcenter-basic-client</artifactId>
                <version>${tripcenter-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>tripcenter-common</artifactId>
                <version>${tripcenter-common-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.common.catserver</groupId>
                        <artifactId>catserver-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.catserver</groupId>
                        <artifactId>catserver-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.travel</groupId>
                        <artifactId>travel-common-validation</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.division</groupId>
                        <artifactId>common-division</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.millau2liaoyuan</groupId>
                        <artifactId>millau2liaoyuan-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.millau2liaoyuan</groupId>
                        <artifactId>millau2liaoyuan</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.liaoyuan</groupId>
                        <artifactId>liaoyuan-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>vipserver-client</artifactId>
                        <groupId>com.taobao.vipserver</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>tripcenter-sdk</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>trip-client</artifactId>
                <version>2.8.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.millau2liaoyuan</groupId>
                        <artifactId>millau2liaoyuan-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.middleware.liaoyuan</groupId>
                        <artifactId>liaoyuan-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 地址库服务 -->
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travel-common-common</artifactId>
                <version>1.2.1</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>999-not-exist</version>
            </dependency>

            <!--导购审核&秒杀报名-->
            <dependency>
                <groupId>com.fliggy.fceadmin</groupId>
                <artifactId>fceadmin-client-seckill</artifactId>
                <version>1.1.10</version>
            </dependency>

            <!--keycenter-->
            <dependency>
                <groupId>com.taobao.keycenter</groupId>
                <artifactId>keycenter-client</artifactId>
                <version>2.2.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--traveltic服务-->
            <dependency>
                <groupId>com.taobao.traveltic</groupId>
                <artifactId>traveltic-domain</artifactId>
                <version>${traveltic-domain-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.traveltic</groupId>
                <artifactId>traveltic-client</artifactId>
                <version>${traveltic-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.traveltic</groupId>
                <artifactId>traveltic-search</artifactId>
                <version>${traveltic-search-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.traveltic</groupId>
                <artifactId>traveltic-match</artifactId>
                <version>${traveltic-match-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.middleware.liaoyuan</groupId>
                        <artifactId>liaoyuan-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastJson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.traveltic</groupId>
                <artifactId>traveltic-biz</artifactId>
                <version>${traveltic-biz-version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mmp-client-core</artifactId>
                        <groupId>com.taobao.mmp</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.trip</groupId>
                        <artifactId>travelcom-forest</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>xml-apis</groupId>
                        <artifactId>xml-apis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.trip</groupId>
                        <artifactId>travelcom-item</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.trip</groupId>
                        <artifactId>travelcom-user</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.inventory</groupId>
                        <artifactId>ip-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.namelist</groupId>
                        <artifactId>namelist-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.media</groupId>
                        <artifactId>media-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.picturecenter</groupId>
                        <artifactId>pic-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.picturecenter</groupId>
                        <artifactId>pic-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.schedulerx</groupId>
                        <artifactId>schedulerx-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.kfc</groupId>
                        <artifactId>client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.taobao.vipserver</groupId>
                <artifactId>vipserver-client</artifactId>
                <version>4.8.0</version>
            </dependency>
            <!-- 引入traveldata-->
            <dependency>
                <groupId>com.fliggy.travel.data.platform</groupId>
                <artifactId>travel-data-platform-client</artifactId>
                <version>2.0.47</version>
                <exclusions>
                    <exclusion>
                        <artifactId>buc.sso.client.plugin-sdk</artifactId>
                        <groupId>com.alibaba.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-jelly</groupId>
                        <artifactId>commons-jelly</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba.trip.trippoi</groupId>
                <artifactId>trippoi-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.trip.trippoi</groupId>
                <artifactId>fliggypoi-client</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.trip.tripdivision</groupId>
                <artifactId>tripdivision-sdk</artifactId>
                <version>1.0.5-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.trip.tripdivision</groupId>
                <artifactId>tripdivision-client</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware.liaoyuan</groupId>
                <artifactId>liaoyuan-client</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.relationrecommend</groupId>
                <artifactId>relationrecommend-client</artifactId>
                <version>4.1.7</version> <!-- 请使用最新版本，自行到页面最下方查找 -->
            </dependency>
            <!-- 商家资质 -->
            <dependency>
                <groupId>com.taobao.tripsm</groupId>
                <artifactId>tripsm-client</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>commons-jelly</groupId>
                        <artifactId>commons-jelly</artifactId>
                    </exclusion>
                </exclusions>
                <version>2.18.18</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.tripsc</groupId>
                <artifactId>tripsc-client</artifactId>
                <version>2.16.0</version>
            </dependency>
            <!-- 系统商直连服务 -->
            <dependency>
                <groupId>com.alitrip.travel</groupId>
                <artifactId>travel-systemvendor-client</artifactId>
                <version>${travel-systemvendor-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-jelly</groupId>
                        <artifactId>commons-jelly</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alitrip.travel</groupId>
                <artifactId>travelapicenter-common</artifactId>
                <version>1.0.15</version>
            </dependency>
            <!-- 招商接口 -->
            <dependency>
                <groupId>com.tmall.sellerservice.client.unifapi</groupId>
                <artifactId>sellerservice-unifapi-client</artifactId>
                <version>1.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>travelcom-user</artifactId>
                <version>1.4.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.citrus.tool</groupId>
                        <artifactId>antx-autoconfig</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>1.8.13</version>
            </dependency>

            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjtools</artifactId>
                <version>1.8.13</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>1.8.13</version>
            </dependency>

            <!-- ================================================= -->
            <!-- 机票suggest接口依赖 -->
            <!-- ================================================= -->
            <dependency>
                <groupId>com.taobao.trip.atip</groupId>
                <artifactId>atsc-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.igdss</groupId>
                <artifactId>igdss-client</artifactId>
                <version>1.0.5-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.iecrm</groupId>
                <artifactId>iecrm-server</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.trip.itdata</groupId>
                <artifactId>itdata-client</artifactId>
                <version>1.6.1</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelbc-common</artifactId>
                <version>1.10.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.wireless</groupId>
                        <artifactId>mtop-hsf-agent</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dts-client</artifactId>
                        <groupId>com.alibaba.dts</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastJson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.csp</groupId>
                        <artifactId>sentinel</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 航班信息接口-->
            <dependency>
                <groupId>com.fliggy.flight.flybd</groupId>
                <artifactId>flybd-client</artifactId>
                <version>1.1.14</version>
            </dependency>

            <!-- 旺铺装修-->
            <dependency>
                <groupId>com.alibaba.shop</groupId>
                <artifactId>alidetaildecorate-client</artifactId>
                <version>1.3.6</version>
            </dependency>

            <!--飞猪分销市场，产品下载为分销商品依赖-->
            <dependency>
                <groupId>com.tmall.usc</groupId>
                <artifactId>product-client</artifactId>
                <version>1.0.23</version>
            </dependency>

            <dependency>

                <groupId>com.fliggy.scm.platform</groupId>
                <artifactId>fsc-platform-client</artifactId>
                <version>1.0.10</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.boot</groupId>
                        <artifactId>pandora-tddl-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.forest</groupId>
                <artifactId>forest-client</artifactId>
                <version>7.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.forest</groupId>
                <artifactId>forest-realtime</artifactId>
                <version>7.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.forest</groupId>
                <artifactId>forest-common</artifactId>
                <version>7.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.forest</groupId>
                <artifactId>forest-store</artifactId>
                <version>7.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.forest</groupId>
                <artifactId>forest-domain</artifactId>
                <version>7.1.12</version>
            </dependency>
            <!-- 星环-天启2.0 -->
            <dependency>
                <groupId>com.alibaba.business.qa</groupId>
                <artifactId>biz-simulator-sdk</artifactId>
                <version>3.0.2</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.ateye</groupId>
                <artifactId>ateye-client</artifactId>
                <version>2.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>sentinel</artifactId>
                <version>3.9.15</version>
            </dependency>
            <dependency>
                <groupId>com.alitrip.tripbp</groupId>
                <artifactId>tripbp-client</artifactId>
                <version>1.3.14</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tddl-client</artifactId>
                        <groupId>com.taobao.pandora.plugin</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba.trippoi</groupId>
                <artifactId>trippoiadmin-facade</artifactId>
                <version>1.1.3</version>
                <exclusions>
                    <exclusion>
                        <artifactId>buc.sso.client</artifactId>
                        <groupId>com.alibaba.platform.shared</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--签证标准库服务-->
            <dependency>
                <groupId>com.taobao.trip.stddatabase</groupId>
                <artifactId>std-database-client</artifactId>
                <version>1.2.7</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.distribution</groupId>
                <artifactId>traveldc-client</artifactId>
                <version>1.1.33</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.fceadmin</groupId>
                <artifactId>fceadmin-client-static-resource</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hsc-client</artifactId>
                <version>1.1.19</version>
            </dependency>


            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hsac-client</artifactId>
                <version>${hsac-client-version}</version>
            </dependency>


            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hic-client</artifactId>
                <version>3.2.12</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.hotel</groupId>
                <artifactId>dc-framework-switch</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.alitrip.hotel</groupId>
                <artifactId>hrg-client</artifactId>
                <version>1.2.4</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.settle</groupId>
                <artifactId>fliggy-settle-client</artifactId>
                <version>${fliggy-settle-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy.boot</groupId>
                <artifactId>fliggy-boot-common</artifactId>
                <version>2.1.3</version>
            </dependency>

            <!-- hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hotel-tag-client</artifactId>
                <version>1.0.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.taobao.trip.hstdcommon</groupId>
                <artifactId>diff-common-utils</artifactId>
                <version>1.2.2</version>
            </dependency>
            <dependency>
                <artifactId>fsc-purchase-client</artifactId>
                <groupId>com.fliggy.scm</groupId>
                <version>1.0.15</version>
            </dependency>

            <!-- common package start -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.13</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-security-client-common</artifactId>
                <version>1.2.10</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.17</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- common package end -->

            <!-- aliyun product or component package start -->
            <!-- oss服务 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.10.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>aliyun-java-sdk-kms</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-security-client-oss</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- aliyun product or component package end -->


            <dependency>
                <groupId>com.fliggy.scm</groupId>
                <artifactId>fsc-resource-client</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.scm</groupId>
                <artifactId>fsc-basic-client</artifactId>
                <version>${fsc-client-basic-version}</version>
            </dependency>

            <dependency>
                <groupId>com.alitrip.common</groupId>
                <artifactId>trip-log-tools</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitems-hsf</artifactId>
                <version>1.0.11</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitemsmanager-common</artifactId>
                <version>1.2.29</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>triptp-order-client</artifactId>
                <version>2.6.16</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.macenter</groupId>
                <artifactId>macenter-client</artifactId>
                <version>1.4.30</version>
            </dependency>

            <!--平台保险信息获取 -->
            <dependency>
                <groupId>com.fliggy.platform.ins</groupId>
                <artifactId>f-ins-web-client</artifactId>
                <version>1.3.4</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.travel.product</groupId>
                <artifactId>travel-product-client</artifactId>
                <version>1.4.6</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.security</groupId>
                <artifactId>security-all</artifactId>
                <version>2.0.6-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>fliggy-lottery-client</artifactId>
                <version>1.0.1-mysterybox</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.media.fileserver</groupId>
                <artifactId>fileserver.code</artifactId>
                <version>3.4.11</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.hpc</groupId>
                <artifactId>hpc-client</artifactId>
                <version>1.3.78</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel.product</groupId>
                <artifactId>travel-product-api-client</artifactId>
                <version>1.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel.line</groupId>
                <artifactId>linecenter-api</artifactId>
                <version>${linecenter.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.tuan</groupId>
                <artifactId>triptuan-client</artifactId>
                <version>1.9.89</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.hotel</groupId>
                <artifactId>tuan-workspace-client</artifactId>
                <version>1.0.14</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-logger-sdk</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.media</groupId>
                <artifactId>media-tools</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hta-client</artifactId>
                <version>${hta-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- TEST start -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>1.5.9.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>0.16</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>3.12.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.5.13</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.0</version>
                <scope>test</scope>
            </dependency>
            <!-- TEST end -->

            <!-- gpf框架升级需要引入的包 -->
            <dependency>
                <groupId>com.taobao.eagleeye</groupId>
                <artifactId>eagleeye-core</artifactId>
                <version>1.5.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.price3</groupId>
                <artifactId>price3-common</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.price3</groupId>
                <artifactId>price3-client</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava.hsf</artifactId>
                <version>18.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.kfc</groupId>
                <artifactId>kfc-client</artifactId>
                <version>4.1.4</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.kfc</groupId>
                <artifactId>util</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.icmp</groupId>
                <artifactId>icmp-idb</artifactId>
                <version>4.3.6</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hipe-client</artifactId>
                <version>1.0.30</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tmall.bbq</groupId>
                <artifactId>bbq-client</artifactId>
                <version>2.11.42</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel.operation</groupId>
                <artifactId>travel-operation-client</artifactId>
                <version>1.2.7</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.payment</groupId>
                <artifactId>finance-member-client</artifactId>
                <version>2.3.1-20221101</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-data-api</artifactId>
                <version>${tuan-data-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-item-api</artifactId>
                <version>${tuan-item-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-merchant-api</artifactId>
                <version>${tuan-merchant-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-item-api-hotel</artifactId>
                <version>${tuan-item-api-hotel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-item-model</artifactId>
                <version>${tuan-item-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.fliggy</groupId>
                        <artifactId>tuan-logger-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.travel</groupId>
                        <artifactId>travelitems-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-boot-starter-rule-engine</artifactId>
                <version>${tuan-boot-starter-rule-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>tuan-boot-starter-rule-engine</artifactId>
                <version>${tuan-boot-starter-rule-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelvc-client</artifactId>
                <version>${travelvc-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel</groupId>
                <artifactId>fliggy-publish-studio4dev-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.16</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.travel.good</groupId>
                <artifactId>travel-good-center-client</artifactId>
                <version>1.1.16</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel.good</groupId>
                <artifactId>travel-good-center-component-commons</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.travel</groupId>
                <artifactId>travel-switch-sandbox-client</artifactId>
                <version>1.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fliggy.switch</groupId>
                        <artifactId>travel-switch-sp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fliggy.switch</groupId>
                <artifactId>travel-switch-sp</artifactId>
                <version>1.0.63</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.fceadmin</groupId>
                <artifactId>fceadmin-client-static-resource</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy.travel</groupId>
                <artifactId>charter-car-client</artifactId>
                <version>1.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.github.nintha</groupId>
                <artifactId>webp-imageio-core</artifactId>
                <version>0.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>tablestore</artifactId>
                <version>${tablestore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-security-client-ots</artifactId>
                <version>${aliyun-security-client-ots.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>2.2</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.normandy.credential</groupId>
                <artifactId>normandy-credential-sdk</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo-shaded</artifactId>
                <version>4.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>3.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.javers</groupId>
                <artifactId>javers-core</artifactId>
                <version>6.6.5</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.9.3</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.fai</groupId>
                <artifactId>fai-client</artifactId>
                <version>1.1.40</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.3.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tmf</groupId>
                <artifactId>tmf2-client</artifactId>
<!--                <version>1.10.37</version>-->
                <version>1.8.9.4</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tmf</groupId>
                <artifactId>tmf2-model</artifactId>
                <version>1.8.9.4</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tmf</groupId>
                <artifactId>tmf2-core</artifactId>
                <version>1.8.9.4</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>2.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>aistudio-client</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy.travel</groupId>
                <artifactId>travelcc-common</artifactId>
                <version>1.0.16</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy.travel</groupId>
                <artifactId>travelcc-client</artifactId>
                <version>1.0.16</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.vpp</groupId>
                <artifactId>vpp-client</artifactId>
                <version>1.2.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.fastvalidator</groupId>
                        <artifactId>fastvalidator-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fliggy.vpp</groupId>
                        <artifactId>vpp-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fliggy.vic.common</groupId>
                        <artifactId>vic-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mapstruct</groupId>
                        <artifactId>mapstruct</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.5.2.Final</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.fmp.mms</groupId>
                <artifactId>mms-client</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.alitrip</groupId>
                <artifactId>trippm-client</artifactId>
                <version>5.4.2-holidayReservation</version>
                <exclusions>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log</artifactId>
                <version>0.8.07</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.2.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <downloadSources>true</downloadSources>
                    <downloadJavadocs>false</downloadJavadocs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <encoding>utf-8</encoding>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <argLine>-Xms1024m -Xmx2048m</argLine>
                    <forkCount>1</forkCount>
                    <reuseForks>true</reuseForks>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>


        </plugins>
    </build>
    <profiles>
        <profile>
            <id>gpf-fliggy</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>aone-publish</id>
            <activation>
                <property><!-- Aone正式发布时-Dcollect.dependency.deal.packageType -->
                    <name>collect.dependency.deal.packageType</name>
                    <value>publish</value> <!--预发是prepub 正式是publish 各个应用可能构建环境不同 -->
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.fliggy.maven.plugin</groupId>
                        <artifactId>fliggy-maven-release-plugin</artifactId>
                        <version>1.0.1-AONE-SNAPSHOT</version>
                        <configuration>
                            <isTerminatePackaging>true</isTerminatePackaging>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>install</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <contributors>
        <contributor>
            <properties>
                <provider><EMAIL></provider>
                <codeName>null</codeName>
                <codeUrl>**************************:sell-platform/gpf-fliggy.git commit:79b585f9</codeUrl>
                <description>contributors added by SCM Plugin, please don't modify it!</description>
            </properties>
        </contributor>
    </contributors>
</project>
