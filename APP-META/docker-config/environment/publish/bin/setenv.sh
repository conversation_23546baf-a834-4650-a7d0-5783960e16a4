#!/bin/bash
##
## Usage  :    sh bin/jbossctl pubstart
## Author :    <EMAIL>, <EMAIL>
## Docs   :    http://gitlab.alibaba-inc.com/spring-boot/docs/wikis/home
## =========================================================================================

# set ${APP_NAME}, if empty $(basename "${APP_HOME}") will be used.
NGINX_SKIP=0
# by default, we don't know the environment to run
# but when we know it, e.g. test environment, we can add special logic, like adding debug port
# value candidates: test, production
if [ -z ${APP_ENVIRONMENT+x} ]; then
    APP_ENVIRONMENT=""
fi
HOME="$(getent passwd "$UID" | awk -F":" '{print $6}')" # fix "$HOME" by "$UID"
MIDDLEWARE_LOGS="${HOME}/logs"


# os env
# NOTE: must edit LANG and JAVA_FILE_ENCODING together
## TODO: review and fix
export LANG=zh_CN.GB18030
export JAVA_FILE_ENCODING=GB18030
export NLS_LANG=AMERICAN_AMERICA.ZHS16GBK
export LD_LIBRARY_PATH=/opt/taobao/oracle/lib:/opt/taobao/lib:$LD_LIBRARY_PATH

export JAVA_HOME=/opt/taobao/java

if [ -z ${SIGMA_MAX_PROCESSORS_LIMIT} ]; then
    echo "warning: SIGMA_MAX_PROCESSORS_LIMIT is not set, gc thread count will set to 8"
fi

export CPU_COUNT=${SIGMA_MAX_PROCESSORS_LIMIT:-8}
ulimit -c unlimited

export JAVA_OUT=$APP_HOME/logs/java.log

# env check and calculate
#
if [ -z "$APP_NAME" ]; then
	APP_NAME=$(basename "${APP_HOME}")
fi

JAVA_OPTS="-server"

# use memory based on the available resources in the machine
let memTotal=`cat /proc/meminfo | grep MemTotal | awk '{printf "%d", $2/1024*0.75 }'`
if [ $memTotal -gt 8000 ]; then
    # 16g内存配置
    JAVA_OPTS="${JAVA_OPTS} -Xms10g -Xmx10g"
    JAVA_OPTS="${JAVA_OPTS} -Xmn3g"
    JAVA_OPTS="${JAVA_OPTS} -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m"
else
    # 8g 内存配置
    JAVA_OPTS="${JAVA_OPTS} -Xms4608m -Xmx4608m"
    JAVA_OPTS="${JAVA_OPTS} -XX:NewRatio=2"
    JAVA_OPTS="${JAVA_OPTS} -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=768m"
fi

JAVA_OPTS="${JAVA_OPTS} -Dautoconfig.skip=true"
JAVA_OPTS="${JAVA_OPTS} -XX:MaxDirectMemorySize=1g"
JAVA_OPTS="${JAVA_OPTS} -XX:SurvivorRatio=10"
JAVA_OPTS="${JAVA_OPTS} -XX:ReservedCodeCacheSize=512m -XX:InitialCodeCacheSize=256m"
JAVA_OPTS="${JAVA_OPTS} -XX:+UseConcMarkSweepGC -XX:CMSMaxAbortablePrecleanTime=5000"
JAVA_OPTS="${JAVA_OPTS} -XX:+CMSClassUnloadingEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:+UseCMSInitiatingOccupancyOnly"
JAVA_OPTS="${JAVA_OPTS} -XX:+ExplicitGCInvokesConcurrent -Dsun.rmi.dgc.server.gcInterval=2592000000 -Dsun.rmi.dgc.client.gcInterval=2592000000"
JAVA_OPTS="${JAVA_OPTS} -XX:ParallelGCThreads=${CPU_COUNT}"
JAVA_OPTS="${JAVA_OPTS} -Xloggc:${MIDDLEWARE_LOGS}/gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps"
JAVA_OPTS="${JAVA_OPTS} -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${MIDDLEWARE_LOGS}/java.hprof"
JAVA_OPTS="${JAVA_OPTS} -Djava.awt.headless=true"
JAVA_OPTS="${JAVA_OPTS} -Dsun.net.client.defaultConnectTimeout=10000"
JAVA_OPTS="${JAVA_OPTS} -Dsun.net.client.defaultReadTimeout=30000"
JAVA_OPTS="${JAVA_OPTS} -Dfile.encoding=GB18030"
#JAVA_OPTS="${JAVA_OPTS} -Dserver.tomcat.max-http-post-size=5MB"
JAVA_OPTS="${JAVA_OPTS} -Dproject.name=${APP_NAME}"
JAVA_OPTS="${JAVA_OPTS} -Dpandora.location=/home/<USER>/gpf-solution-deploy/target/taobao-hsf.sar"
#hsf 优雅上下线需要的延迟服务注册参数
JAVA_OPTS="${JAVA_OPTS} -Dhsf.publish.delayed=true"
JAVA_OPTS="${JAVA_OPTS} -Dhsf.online.segment.count=5"

[ -f "/home/<USER>/logs/extendFlags.txt" ] &&  JAVA_OPTS="${JAVA_OPTS} `head -1 -q /home/<USER>/logs/extendFlags.txt`"

SPRINGBOOT_OPTS="--server.port=7001 --management.port=7002"
SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --management.info.build.mode=full"
SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --spring.profiles.active=production"
SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --logging.path=${APP_HOME}/logs --logging.file=${APP_HOME}/logs/application.log"


echo "INFO: start add codeCoverage opts."
if [ "$Record_Code_Coverage" -eq 1 ]; then
  echo "start codeCoverage"
  wget -O flyhawk_coverage_shell.sh http://flyhawk-coverage.cn-zhangjiakou.oss-internal.aliyun-inc.com/shell/flyhawk_coverage_shell.sh && sh flyhawk_coverage_shell.sh
  response=$(curl -f -s -L -k "http://sorcerer.alitrip.com/open/api/codeCoverageAppOpts/includes?appName=${APP_NAME}") && [[ -n "$response" && "$response" =~ ^[a-zA-Z0-9._*]+(:[a-zA-Z0-9._*]+)*$ ]] && INCLUDES_CLASS_OPTS="$response" || INCLUDES_CLASS_OPTS="com.*"
  response=$(curl -f -s -L -k "http://sorcerer.alitrip.com/open/api/codeCoverageAppOpts/excludes?appName=${APP_NAME}") && [[ -n "$response" && "$response" =~ ^[a-zA-Z0-9._*]+(:[a-zA-Z0-9._*]+)*$ ]] && EXCLUDES_CLASS_OPTS="$response" || EXCLUDES_CLASS_OPTS="com.test"
  ##下面三行需要三选一！！！脚本中SERVICE_OPTS则选择第一行，有CATALINA_OPTS则选择第二行，有JAVA_OPTS则选择第三行
  ##SERVICE_OPTS="${SERVICE_OPTS} -javaagent:/home/<USER>/plugins/sorcerer_jacocoagent.jar=includes=${INCLUDES_CLASS_OPTS},excludes=${EXCLUDES_CLASS_OPTS},output=tcpserver,port=6300,address=${RequestedIP}"
  JAVA_OPTS="$JAVA_OPTS -javaagent:/home/<USER>/plugins/sorcerer_jacocoagent.jar=includes=${INCLUDES_CLASS_OPTS},excludes=${EXCLUDES_CLASS_OPTS},output=tcpserver,port=6300,address=${RequestedIP}"  ##若脚本有JAVA_OPTS变量，请用本行代替上面一行内容

  echo "codeCoverage successfully"
fi



export JAVA_OPTS
export SPRINGBOOT_OPTS


# nginx env
#
NGINX_HOME=/home/<USER>/cai
# if set to "1", skip start nginx.
test -z "$NGINX_SKIP" && NGINX_SKIP=1
NGINXCTL=$NGINX_HOME/bin/nginxctl
STATUSROOT_HOME="${APP_HOME}/target/htdocs"

# 是否更新pandora
UPDATE_PANDORA=true
export JAVA_OPTS