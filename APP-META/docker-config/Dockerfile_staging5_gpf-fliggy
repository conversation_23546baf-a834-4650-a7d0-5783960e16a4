FROM hub.docker.alibaba-inc.com/aone-base/gpf-fliggy_accurate:20250826215148
# 这里修改为solution的名字
ENV BUILD_APP_NAME gpf-fliggy

############ staging #################
#设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
ENV SERVICE_OPTS=-Dspring.profiles.active=staging5
ENV CONFIG_ENV=staging5
# 这里修改环境名称(project, testing, prepub, publish)
ENV ENVIRONMENT prepub
ENV JPDA_ENABLE=1
ENV  Record_Code_Coverage=1

# 将应用启动脚本和配置文件复制到镜像中
COPY environment/common/bin/ /home/<USER>/${BUILD_APP_NAME}/bin
COPY environment/common/bin/setenv.sh /home/<USER>/gpf-solution-deploy/common/bin/setenv.sh
COPY environment/common/cai/conf/nginx-proxy.conf /home/<USER>/cai/conf/nginx-proxy.conf

#自动修复添加 COPY 防御工程项目到 /home/<USER>/security_shield 目录
COPY --from=reg.docker.alibaba-inc.com/alisecurity/security_shield:1.1 /home/<USER>/security_shield /home/<USER>/security_shield


RUN sh /home/<USER>/security_shield/cli.sh vuln_defense jdwp && \
    echo $BUILD_APP_NAME > /home/<USER>/gpf-appname && \
    echo $ENVIRONMENT > /home/<USER>/gpf-env && \
    mkdir -p /etc/ilogtail/users && \
    touch /etc/ilogtail/users/1496653486717012 && \
    touch /etc/ilogtail/users/1310084623085893 && \
    echo gpf-fliggy > /etc/ilogtail/user_defined_id

VOLUME /home/<USER>/$BUILD_APP_NAME/logs \
       /home/<USER>/millau2liaoyuan \
       /home/<USER>/liaoyuan-out \
       /home/<USER>/catserver \
       /home/<USER>/ateye \
       /home/<USER>/forest

COPY $BUILD_APP_NAME.tgz /home/<USER>/gpf-solution-deploy/target/$BUILD_APP_NAME.tgz