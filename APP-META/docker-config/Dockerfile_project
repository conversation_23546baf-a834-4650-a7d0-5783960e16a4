FROM hub.docker.alibaba-inc.com/aone-base/gpf-fliggy_accurate:20250826215148

# 这里修改为solution的名字
ENV BUILD_APP_NAME gpf-fliggy

# 这里修改环境名称(project, daily, prepub, publish)
ENV ENVIRONMENT project

ENV JPDA_ENABLE=1
ENV  Record_Code_Coverage=0

VOLUME /home/<USER>/$BUILD_APP_NAME/logs \
       /home/<USER>/millau2liaoyuan \
       /home/<USER>/liaoyuan-out \
       /home/<USER>/catserver \
       /home/<USER>/ateye \
       /home/<USER>/forest

RUN echo $BUILD_APP_NAME > /home/<USER>/gpf-appname && echo $ENVIRONMENT > /home/<USER>/gpf-env
COPY environment/common/cai/conf/nginx-proxy.conf /home/<USER>/cai/conf/nginx-proxy.conf
COPY $BUILD_APP_NAME.tgz /home/<USER>/gpf-solution-deploy/target/$BUILD_APP_NAME.tgz