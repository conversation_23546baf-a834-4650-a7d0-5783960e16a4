# 基于基础镜像
FROM reg.docker.alibaba-inc.com/ali/os:7u2

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ENV GPF_SOLUTION_DEPLOY gpf-solution-deploy

# 指定pandora(hsf)的版本
ARG sar_version=2019-09-stable-rocketmq-optimize

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN rpm -ivh --nodeps "http://yum.corp.taobao.com/taobao/7/x86_64/current/ajdk/ajdk-8.5.9_fp1-20180730112611.alios7.x86_64.rpm" && \
rpm -ivh --nodeps "http://yum.taobao.net/taobao/7/x86_64/current/c_tbip/c_tbip-2.0.9-1580249.18bc121.el7.x86_64.rpm" && \
rpm -ivh --nodeps http://yum.tbsite.net/alios/7/os/x86_64/Packages/taobao-cronolog-1.6.2-15.alios7.x86_64.rpm && \
rpm -ivh --nodeps  http://yum.tbsite.net/taobao/7/x86_64/stable/ali-sls-ilogtail/ali-sls-ilogtail-0.14.10-1746302.alios7.x86_64.rpm && \
rpm -ivh --nodeps http://yum.tbsite.net/taobao/7/x86_64/current/tengine-proxy/tengine-proxy-2.1.23-20190625110117.el7u2.x86_64.rpm && \
# 最新的tengine可能会出现问题。需要制定版本
# yum install -b current tengine-proxy -y && \
yum install -b current t-security-xagent -y && \
yum install -b current ali-sls-ilogtail -y && \
mkdir -p /home/<USER>/$GPF_SOLUTION_DEPLOY/target/ && \
wget -c "http://ops.jm.taobao.org:9999/pandora-web/sar/$sar_version/taobao-hsf.tgz" -O /home/<USER>/$GPF_SOLUTION_DEPLOY/target/taobao-hsf.tgz && \
mkdir /home/<USER>/ilogtail

# logtail配置文件，解决容器重启偏移丢失的问题
COPY environment/common/conf/ilogtail_config.json      /usr/local/ilogtail

# 将应用启动脚本和nginx配置复制到镜像中
COPY environment/common/cai/    /home/<USER>/cai/
COPY environment/               /home/<USER>/$GPF_SOLUTION_DEPLOY/

# 加载字体到镜像中
COPY environment/common/font/Calibri.ttf    /usr/share/fonts/Calibri/
RUN cd /usr/share/fonts/Calibri/;fc-cache -fv

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/logs /home/<USER>/cai/logs /home/<USER>/ilogtail
VOLUME /home/<USER>/vmcommon
VOLUME /home/<USER>/doom

VOLUME /home/<USER>/forest
VOLUME /home/<USER>/amsdata_all
VOLUME /home/<USER>/liaoyuan-out
VOLUME /home/<USER>/catserver /home/<USER>/configclient/snapshot/ /home/<USER>/logs/configclient/snapshot/
VOLUME /home/<USER>/vipsrv-failover /home/<USER>/vipsrv-cache /home/<USER>/vipsrv-logs /home/<USER>/vipsrv-dns
VOLUME /home/<USER>/diamond

# 启动容器时进入的工作目录
WORKDIR /home/<USER>/

RUN chmod +x /home/<USER>/$GPF_SOLUTION_DEPLOY/common/bin/*.sh

#容器启动时自动执行的脚本，我们一般会将应用启动脚本放在这里，相当于系统自启应用
ENTRYPOINT ["/home/<USER>/$GPF_SOLUTION_DEPLOY/common/bin/container_bootstrap.sh"]