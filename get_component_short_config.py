#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
compconfig文件解析脚本
用于解析compconfig JSON文件，提取父组件信息
"""

import json
import sys
import os
import glob
from typing import Dict, List, Any, Optional


class CompConfigParser:
    def __init__(self, project_root: str = "."):
        self.project_root = project_root
        self.identity_config_file = os.path.join(project_root, "gpf.fliggy.domain", "src", "main", "resources", "identity-all-config.json")
        self.all_components = []
        self.child_components = set()
        
    def load_identity_configs(self) -> List[Dict[str, Any]]:
        """加载业务身份配置"""
        try:
            with open(self.identity_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"错误: 无法读取业务身份配置文件 {self.identity_config_file}: {e}")
            return []
    
    def find_business_identity(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """根据业务身份id查找对应的配置"""
        identities = self.load_identity_configs()
        for identity in identities:
            if identity.get('identity') == identity_id:
                return identity
        return None
    
    def extract_business_name_from_identity(self, identity_id: str) -> Optional[str]:
        """从业务身份id中提取业务名称"""
        # 格式通常是: fliggy-{business}-{id}
        if identity_id.startswith('fliggy-'):
            parts = identity_id.split('-')
            if len(parts) >= 3:
                # 处理可能的复合业务名称，如 line-abroad
                business_parts = parts[1:-1]  # 去掉 fliggy 和最后的数字
                return '-'.join(business_parts)
        return None
    
    def find_compconfig_file(self, identity_id: str) -> Optional[str]:
        """查找业务身份对应的compconfig配置文件，排除target目录"""
        # 提取业务名称
        business_name = self.extract_business_name_from_identity(identity_id)
        if not business_name:
            print(f"警告: 无法从业务身份id '{identity_id}' 中提取业务名称")
            return None
        
        # 查找业务身份特定的compconfig文件，排除target目录
        business_app_dir = os.path.join(self.project_root, "gpf.fliggy.apps", f"gpf.fliggy.app-{business_name}")
        if os.path.exists(business_app_dir):
            # 只查找src目录下的文件，排除target目录
            src_pattern = os.path.join(business_app_dir, "src", "**", f"compconfig-{identity_id}-config.json")
            business_files = glob.glob(src_pattern, recursive=True)
            if business_files:
                return business_files[0]  # 返回第一个找到的文件
            
            # 如果没找到精确匹配的，查找该业务下的所有compconfig文件
            src_pattern = os.path.join(business_app_dir, "src", "**", "compconfig-*.json")
            business_files = glob.glob(src_pattern, recursive=True)
            # 过滤出匹配的文件
            for file_path in business_files:
                if identity_id in os.path.basename(file_path):
                    return file_path
        
        # 如果在业务身份目录下没找到，尝试在domain目录下查找
        domain_pattern = os.path.join(self.project_root, "gpf.fliggy.domain", "src", "**", f"compconfig-{identity_id}-config.json")
        domain_files = glob.glob(domain_pattern, recursive=True)
        if domain_files:
            return domain_files[0]
        
        return None
        
    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """解析compconfig文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except FileNotFoundError:
            print(f"错误: 文件 {file_path} 不存在")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"错误: JSON解析失败 - {e}")
            sys.exit(1)
        except Exception as e:
            print(f"错误: 读取文件失败 - {e}")
            sys.exit(1)
    
    def extract_components(self, data: Dict[str, Any]) -> None:
        """提取所有组件信息"""
        # 从groupToComps.base中提取组件
        group_to_comps = data.get('groupToComps', {})
        # 兼容所有分组，不仅仅是 base
        for comp_list in group_to_comps.values():
            for comp in comp_list:
                self._process_component(comp)
    
    def _process_component(self, comp: Dict[str, Any], parent_name: str = None) -> None:
        """处理单个组件，递归处理子组件"""
        comp_info = self._extract_component_info(comp)
        if comp_info:
            self.all_components.append(comp_info)
            
            # 如果有子组件，递归处理
            children = comp.get('children', [])
            for child in children:
                # 记录子组件名称
                child_name = child.get('compname')
                if child_name:
                    self.child_components.add(child_name)
                self._process_component(child, comp_info['compname'])
    
    def _extract_component_info(self, comp: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取组件的关键信息"""
        compname = comp.get('compname')
        if not compname:
            return None
            
        pagemodel = comp.get('pagemodel', {})
        storemodel = comp.get('storemodel', {})
        
        return {
            'compname': compname,
            'label': pagemodel.get('label', ''),
            'uitype': pagemodel.get('uiType', pagemodel.get('uitype', '')),
            'storemodel_strategy': storemodel.get('strategy', ''),
            'has_children': bool(comp.get('children', [])),
            'children_count': len(comp.get('children', []))
        }

    # ============ 新增：按组件名查询完整配置（含父子） ============
    def _iter_all_components_lists(self, data: Dict[str, Any]) -> List[List[Dict[str, Any]]]:
        group_to_comps = data.get('groupToComps', {})
        return [lst for lst in group_to_comps.values() if isinstance(lst, list)]

    def _find_components_by_name(self, data: Dict[str, Any], target: str) -> List[Dict[str, Any]]:
        matches = []
        def walk(node: Dict[str, Any]):
            if not isinstance(node, dict):
                return
            if node.get('compname') == target:
                matches.append(node)
            for child in node.get('children', []) or []:
                walk(child)
        for lst in self._iter_all_components_lists(data):
            for root in lst:
                walk(root)
        return matches

    def _print_component_tree(self, node: Dict[str, Any], level: int = 0, path: str = "") -> None:
        indent = '  ' * level
        compname = node.get('compname', '')
        init_strategy = node.get('initStrategy') or node.get('initstrategy') or ''
        json_strategy = node.get('jsonStrategy') or node.get('jsonstrategy') or ''
        store_strategy = (node.get('storemodel') or {}).get('strategy', '')
        pagemodel = node.get('pagemodel') or {}
        ui_type = pagemodel.get('uiType') or pagemodel.get('uitype') or ''
        pm_type = pagemodel.get('type') or ''

        # 关系与路径
        relation = '父组件' if level == 0 else '子组件'
        current_path = compname if not path else f"{path} > {compname}"

        print(f"{indent}组件: {compname}  [{relation}]")
        print(f"{indent}  path: {current_path}")
        print(f"{indent}  initStrategy: {init_strategy or '无'}")
        print(f"{indent}  jsonStrategy: {json_strategy or '无'}")
        print(f"{indent}  storemodel.strategy: {store_strategy or '无'}")
        print(f"{indent}  uiType: {ui_type or '无'}")
        # 精简的 pagemodel 仅保留关键字段
        try:
            import json as _json
            pm_brief = {'type': pm_type or '无', 'uiType': ui_type or '无'}
            pm_text = _json.dumps(pm_brief, ensure_ascii=False)
        except Exception:
            pm_text = str({'type': pm_type or '无', 'uiType': ui_type or '无'})
        print(f"{indent}  pagemodel: {pm_text}")

        children = node.get('children', []) or []
        for child in children:
            self._print_component_tree(child, level + 1, current_path)
    
    def get_parent_components(self) -> List[Dict[str, Any]]:
        """获取父组件列表（不是其他组件的子组件的组件）"""
        parent_components = []
        
        for comp in self.all_components:
            # 如果组件名不在子组件集合中，则认为是父组件
            if comp['compname'] not in self.child_components:
                parent_components.append(comp)
        
        return parent_components
    
    def print_parent_components(self, parent_components: List[Dict[str, Any]]) -> None:
        """打印父组件信息"""
        print("=" * 80)
        print("父组件列表")
        print("=" * 80)
        print(f"{'序号':<4} {'组件名':<30} {'中文名称':<20} {'UI类型':<20} {'存储策略':<30}")
        print("-" * 80)
        
        for i, comp in enumerate(parent_components, 1):
            compname = comp['compname']
            label = comp['label'] or '无'
            uitype = comp['uitype'] or '无'
            strategy = comp['storemodel_strategy'] or '无'
            
            # 截断过长的字符串
            compname = (compname[:27] + '...') if len(compname) > 30 else compname
            label = (label[:17] + '...') if len(label) > 20 else label
            uitype = (uitype[:17] + '...') if len(uitype) > 20 else uitype
            strategy = (strategy[:27] + '...') if len(strategy) > 30 else strategy
            
            print(f"{i:<4} {compname:<30} {label:<20} {uitype:<20} {strategy:<30}")
        
        print("-" * 80)
        print(f"总计: {len(parent_components)} 个父组件")
        
        # 统计有子组件的父组件
        with_children = [comp for comp in parent_components if comp['has_children']]
        if with_children:
            print(f"其中有子组件的父组件: {len(with_children)} 个")
    
    def print_detailed_info(self, parent_components: List[Dict[str, Any]]) -> None:
        """打印详细信息"""
        print("\n" + "=" * 80)
        print("详细信息")
        print("=" * 80)
        
        for i, comp in enumerate(parent_components, 1):
            print(f"\n{i}. 组件名: {comp['compname']}")
            print(f"   中文名称: {comp['label'] or '无'}")
            print(f"   uiType: {comp['uitype'] or '无'}")
            print(f"   storemodel.strategy: {comp['storemodel_strategy'] or '无'}")
            if comp['has_children']:
                print(f"   子组件数量: {comp['children_count']}")

def main():
    """主函数
    用法:
      1) python3 get_component_short_config.py <业务身份id>
      2) python3 get_component_short_config.py <业务身份id> <组件名>
    """
    if len(sys.argv) not in (2, 3):
        print("使用方法: python3 get_component_short_config.py <业务身份id> [组件名]")
        print("\n示例:")
        print("  python3 get_component_short_config.py fliggy-daytour-108")
        print("  python3 get_component_short_config.py fliggy-daytour-108 dayTourPackageInfo")
        print("  python3 get_component_short_config.py fliggy-visa-101")
        print("  python3 get_component_short_config.py fliggy-line-106 corePoi")
        
        # 显示可用的业务身份
        parser = CompConfigParser()
        identities = parser.load_identity_configs()
        if identities:
            print(f"\n可用的业务身份id:")
            for identity in identities[:10]:  # 只显示前10个作为示例
                print(f"  - {identity.get('identity')} ({identity.get('name', '未知')})")
            if len(identities) > 10:
                print(f"  ... 还有 {len(identities) - 10} 个业务身份")
        
        sys.exit(1)

    identity_id = sys.argv[1]
    target_comp = sys.argv[2] if len(sys.argv) == 3 else None

    parser = CompConfigParser()

    # 查找业务身份信息
    identity_info = parser.find_business_identity(identity_id)
    if not identity_info:
        print(f"错误: 未找到业务身份 '{identity_id}' 的配置")
        sys.exit(1)

    print(f"业务身份: {identity_id}")
    print(f"业务名称: {identity_info.get('name', '未知')}")

    # 查找compconfig文件
    file_path = parser.find_compconfig_file(identity_id)
    if not file_path:
        print(f"错误: 未找到业务身份 '{identity_id}' 对应的compconfig配置文件")
        sys.exit(1)

    # 解析文件
    print(f"正在解析文件: {file_path}")
    data = parser.parse_file(file_path)

    # 若未指定组件名，走原有父组件摘要逻辑
    if not target_comp:
        parser.extract_components(data)
        parent_components = parser.get_parent_components()
        if not parent_components:
            print("未找到任何父组件")
            return
        parser.print_detailed_info(parent_components)
        return

    # 指定组件名：输出该组件完整配置（含子组件，逐层递归）
    matches = parser._find_components_by_name(data, target_comp)
    if not matches:
        print(f"未找到组件: {target_comp}")
        return
    print("\n查询结果 (包含自身与所有子组件，按层级展示父子关系):")
    print("-" * 80)
    for idx, node in enumerate(matches, 1):
        print(f"匹配#{idx}:")
        parser._print_component_tree(node, level=0)
        print("-" * 80)

if __name__ == "__main__":
    main()
