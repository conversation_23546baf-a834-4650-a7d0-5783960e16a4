#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
业务身份schemaStrategy查询脚本
根据业务身份id，输出该业务身份下所有已配置过schemaStrategy的父组件及对应的策略名称
"""

import json
import sys
import os
import glob
from typing import Dict, List, Any, Optional, Set

class BusinessIdentitySchemaQuery:
    def __init__(self, project_root: str = "."):
        self.project_root = project_root
        self.identity_config_file = os.path.join(project_root, "gpf.fliggy.domain", "src", "main", "resources", "identity-all-config.json")
        self.domain_adapter_file = os.path.join(project_root, "gpf.fliggy.domain", "src", "main", "resources", "fliggy-adaptertemplate-config.json")
        
    def load_identity_configs(self) -> List[Dict[str, Any]]:
        """加载业务身份配置"""
        try:
            with open(self.identity_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"错误: 无法读取业务身份配置文件 {self.identity_config_file}: {e}")
            return []
    
    def find_business_identity(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """根据业务身份id查找对应的配置"""
        identities = self.load_identity_configs()
        for identity in identities:
            if identity.get('identity') == identity_id:
                return identity
        return None
    
    def extract_business_name_from_identity(self, identity_id: str) -> Optional[str]:
        """从业务身份id中提取业务名称"""
        # 格式通常是: fliggy-{business}-{id}
        if identity_id.startswith('fliggy-'):
            parts = identity_id.split('-')
            if len(parts) >= 3:
                # 处理可能的复合业务名称，如 line-abroad
                business_parts = parts[1:-1]  # 去掉 fliggy 和最后的数字
                return '-'.join(business_parts)
        return None
    
    def find_adapter_files(self, identity_id: str) -> List[str]:
        """查找业务身份对应的adapter配置文件，排除target目录"""
        adapter_files = []
        
        # 提取业务名称
        business_name = self.extract_business_name_from_identity(identity_id)
        if not business_name:
            print(f"警告: 无法从业务身份id '{identity_id}' 中提取业务名称")
            return adapter_files
        
        # 查找业务身份特定的adapter文件，排除target目录
        business_app_dir = os.path.join(self.project_root, "gpf.fliggy.apps", f"gpf.fliggy.app-{business_name}")
        if os.path.exists(business_app_dir):
            # 只查找src目录下的文件，排除target目录
            src_pattern = os.path.join(business_app_dir, "src", "**", f"compadapter-{identity_id}-config.json")
            business_files = glob.glob(src_pattern, recursive=True)
            adapter_files.extend(business_files)
            
            # 如果没找到精确匹配的，查找该业务下的所有adapter文件
            if not business_files:
                src_pattern = os.path.join(business_app_dir, "src", "**", "compadapter-*.json")
                business_files = glob.glob(src_pattern, recursive=True)
                # 过滤出匹配的文件
                for file_path in business_files:
                    if identity_id in os.path.basename(file_path):
                        adapter_files.append(file_path)
        
        # 总是包含domain模板文件作为备选
        if os.path.exists(self.domain_adapter_file):
            adapter_files.append(self.domain_adapter_file)
        
        return adapter_files
    
    def parse_adapter_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """解析adapter配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"警告: 无法解析文件 {file_path}: {e}")
            return None
    
    def extract_schema_components(self, data: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """提取schema场景下的组件信息"""
        components = []
        child_components = set()
        
        # 获取scene.schema下的组件配置
        scene = data.get('scene', {})
        schema_scene = scene.get('schema', {})
        
        if not schema_scene:
            return components
        
        # 获取组件列表
        comps = schema_scene.get('comps', [])
        
        # 先收集所有子组件名称
        def collect_child_components(comp_list):
            for comp in comp_list:
                children = comp.get('children', [])
                for child in children:
                    child_name = child.get('compname')
                    if child_name:
                        child_components.add(child_name)
                    # 递归收集子组件的子组件
                    collect_child_components([child])
        
        collect_child_components(comps)
        
        # 提取组件信息
        def extract_components(comp_list, parent_name=None):
            for comp in comp_list:
                comp_info = self._extract_component_info(comp, file_path, parent_name)
                if comp_info:
                    components.append(comp_info)
                    
                    # 递归处理子组件
                    children = comp.get('children', [])
                    if children:
                        extract_components(children, comp_info['compname'])
        
        extract_components(comps)
        
        # 过滤出父组件（不是其他组件的子组件）
        parent_components = []
        for comp in components:
            if comp['compname'] not in child_components:
                parent_components.append(comp)
        
        return parent_components
    
    def _extract_component_info(self, comp: Dict[str, Any], file_path: str, parent_name: str = None) -> Optional[Dict[str, Any]]:
        """提取组件的关键信息，并标注策略来源（业务身份 or domain 模板）"""
        compname = comp.get('compname')
        strategy = comp.get('strategy')

        if not compname:
            return None

        # 只返回配置了strategy的组件
        if not strategy:
            return None

        # 识别来源：如果来源文件是 domain 模板，则判定为 domain，否则为业务身份
        file_name = os.path.basename(file_path)
        is_domain = 'fliggy-adaptertemplate-config.json' in file_name
        source = 'domain' if is_domain else 'app'

        return {
            'compname': compname,
            'strategy': strategy,
            'file_name': file_name,
            'file_path': file_path,
            'source': source,
        }
    
    def query_schema_strategy(self, identity_id: str) -> Dict[str, Any]:
        """查询指定业务身份的schemaStrategy配置"""
        result = {
            'identity_id': identity_id,
            'identity_info': None,
            'components': [],
            'total_count': 0
        }
        
        # 查找业务身份信息
        identity_info = self.find_business_identity(identity_id)
        if not identity_info:
            print(f"错误: 未找到业务身份 '{identity_id}' 的配置")
            return result
        
        result['identity_info'] = identity_info
        
        # 查找adapter配置文件
        adapter_files = self.find_adapter_files(identity_id)
        if not adapter_files:
            print(f"警告: 未找到业务身份 '{identity_id}' 对应的adapter配置文件")
            return result
        
        # 解析所有adapter文件
        all_components = []
        for file_path in adapter_files:
            data = self.parse_adapter_file(file_path)
            if data:
                components = self.extract_schema_components(data, file_path)
                all_components.extend(components)
        
        # 去重（可能多个文件中有相同组件）
        unique_components = {}
        for comp in all_components:
            comp_key = comp['compname']
            if comp_key not in unique_components:
                unique_components[comp_key] = comp
            else:
                # 如果有重复，优先使用业务身份特定的配置（非domain模板）
                existing = unique_components[comp_key]
                if 'fliggy-adaptertemplate-config.json' in existing['file_name'] and 'fliggy-adaptertemplate-config.json' not in comp['file_name']:
                    unique_components[comp_key] = comp
        
        result['components'] = list(unique_components.values())
        result['total_count'] = len(result['components'])
        
        return result
    
    def print_result(self, result: Dict[str, Any]) -> None:
        """打印查询结果"""
        identity_id = result['identity_id']
        identity_info = result['identity_info']
        components = result['components']
        total_count = result['total_count']
        
        print(f"业务身份: {identity_id}")
        if identity_info:
            print(f"业务名称: {identity_info.get('name', '未知')}")
        
        if total_count == 0:
            print("未找到任何配置了schemaStrategy的父组件")
            return
        
        print(f"找到 {total_count} 个配置了schemaStrategy的父组件:")
        print("-" * 80)
        
        # 按组件名排序
        sorted_components = sorted(components, key=lambda x: x['compname'])
        
        for comp in sorted_components:
            # 来源标注（中文）：业务身份 / domain模板
            source_label = '业务身份' if comp.get('source') == 'app' else 'domain模板'
            # 配置文件相对路径
            file_path = comp.get('file_path') or comp.get('file_name') or ''
            try:
                rel_path = os.path.relpath(file_path, self.project_root)
            except Exception:
                rel_path = file_path
            print(f"{comp['compname']} -> {comp['strategy']}  [来源: {source_label}]  [配置文件: {rel_path}]")

    def query_schema_strategy_for_component(self, identity_id: str, target_compname: str) -> Dict[str, Any]:
        """查询指定业务身份下，某组件在 scene.schema 中的策略（包含其所有父组件与子组件）"""
        result = {
            'identity_id': identity_id,
            'identity_info': None,
            'components': [],
            'total_count': 0
        }

        identity_info = self.find_business_identity(identity_id)
        if not identity_info:
            print(f"错误: 未找到业务身份 '{identity_id}' 的配置")
            return result
        result['identity_info'] = identity_info

        adapter_files = self.find_adapter_files(identity_id)
        if not adapter_files:
            print(f"警告: 未找到业务身份 '{identity_id}' 对应的adapter配置文件")
            return result

        collected = []

        def build_info(node, file_path, relation):
            info = self._extract_component_info(node, file_path)
            if info:
                info['relation'] = relation  # parent/child/self
            return info

        def collect_descendants(node, file_path):
            nodes = []
            for child in node.get('children', []) or []:
                ci = build_info(child, file_path, 'child')
                if ci:
                    nodes.append(ci)
                nodes.extend(collect_descendants(child, file_path))
            return nodes

        def dfs(node, stack, file_path):
            # 如果命中目标
            if node.get('compname') == target_compname:
                # parents
                for p in stack:
                    pi = build_info(p, file_path, 'parent')
                    if pi:
                        collected.append(pi)
                # self
                si = build_info(node, file_path, 'self')
                if si:
                    collected.append(si)
                # children (all descendants)
                collected.extend(collect_descendants(node, file_path))
                # 不 return，继续遍历以捕获其他文件或同名节点
            # 继续深入
            for child in node.get('children', []) or []:
                stack.append(node)
                dfs(child, stack, file_path)
                stack.pop()

        for file_path in adapter_files:
            data = self.parse_adapter_file(file_path)
            if not data:
                continue
            schema_scene = (data.get('scene') or {}).get('schema') or {}
            comps = schema_scene.get('comps') or []
            for root in comps:
                dfs(root, [], file_path)

        # 去重：优先业务身份(app)覆盖domain模板
        merged = {}
        order_priority = {'self': 3, 'child': 2, 'parent': 1}
        for item in collected:
            key = item['compname']
            prev = merged.get(key)
            if not prev:
                merged[key] = item
            else:
                # 来源优先级：app > domain；关系优先级：self > child > parent
                prev_app = 1 if prev.get('source') == 'app' else 0
                curr_app = 1 if item.get('source') == 'app' else 0
                prev_rel = order_priority.get(prev.get('relation'), 0)
                curr_rel = order_priority.get(item.get('relation'), 0)
                if (curr_app, curr_rel) > (prev_app, prev_rel):
                    merged[key] = item

        result['components'] = list(merged.values())
        result['total_count'] = len(result['components'])
        return result

    def print_result_for_component(self, result: Dict[str, Any], target_compname: str) -> None:
        identity_id = result['identity_id']
        identity_info = result['identity_info']
        components = result['components']
        total_count = result['total_count']

        print(f"业务身份: {identity_id}")
        if identity_info:
            print(f"业务名称: {identity_info.get('name', '未知')}")
        print(f"组件: {target_compname}")
        if total_count == 0:
            print("未在 scene.schema 中找到该组件或其父子组件的策略配置")
            return
        print(f"共找到 {total_count} 个相关组件(含父/子):")
        print("-" * 80)

        # 自身优先，然后子组件，再父组件；同类按名称排序
        rel_weight = {'self': 0, 'child': 1, 'parent': 2}
        components = sorted(components, key=lambda x: (rel_weight.get(x.get('relation'), 9), x['compname']))
        for comp in components:
            source_label = '业务身份' if comp.get('source') == 'app' else 'domain模板'
            relation_label = comp.get('relation', '')
            file_path = comp.get('file_path') or comp.get('file_name') or ''
            try:
                rel_path = os.path.relpath(file_path, self.project_root)
            except Exception:
                rel_path = file_path
            print(f"[{relation_label}] {comp['compname']} -> {comp['strategy']}  [来源: {source_label}]  [配置文件: {rel_path}]")

def main():
    """主函数
    用法:
      1) python query_schema_strategy.py <业务身份id>
      2) python query_schema_strategy.py <业务身份id> <组件名>
    """
    if len(sys.argv) not in (2, 3):
        print("用法: python query_schema_strategy.py <业务身份id> [组件名]")
        print("\n示例:")
        print("  python query_schema_strategy.py fliggy-visa-101")
        print("  python query_schema_strategy.py fliggy-line-106 visaDesc")
        print("  python query_schema_strategy.py fliggy-play-107 images")
        
        # 显示可用的业务身份
        query = BusinessIdentitySchemaQuery()
        identities = query.load_identity_configs()
        if identities:
            print(f"\n可用的业务身份id:")
            for identity in identities[:10]:  # 只显示前10个作为示例
                print(f"  - {identity.get('identity')} ({identity.get('name', '未知')})")
            if len(identities) > 10:
                print(f"  ... 还有 {len(identities) - 10} 个业务身份")
        
        sys.exit(1)

    identity_id = sys.argv[1]
    target_compname = sys.argv[2] if len(sys.argv) == 3 else None

    query = BusinessIdentitySchemaQuery()
    if target_compname:
        result = query.query_schema_strategy_for_component(identity_id, target_compname)
        query.print_result_for_component(result, target_compname)
    else:
        result = query.query_schema_strategy(identity_id)
        query.print_result(result)

if __name__ == "__main__":
    main()