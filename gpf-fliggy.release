# 1. 你可以直接编辑本文件的内容，或者通过工具来帮你校验合法性和自动生成，请点击：http=//aliwing.alibaba-inc.com/apprelease/home.htm
# 2. 更多关于Release文件的规范和约定，请点击= http=//docs.alibaba-inc.com/pages/viewpage.action?pageId=252891532

# 构建源码语言类型
code.language=java

# 构建打包使用jdk版本
baseline.jdk=ali-jdk-8.3.5

# 构建打包所用的maven版本
# build.tools.maven=maven3.3.9
build.tools.maven=amaven3.5.0

# 构建打包使用的maven settings文件
build.tools.maven.settings=tao

build.output=gpf.fliggy.web/target/gpf-fliggy.jar

# 编译方式
# mvn -T 4 clean install
# mvn -T 1C clean install
# -T 4 是直接指定4线程
# -T 1C 表示CPU线程的倍数
build.command = mvn -T 1C clean package -Dmaven.test.skip -Denv=release -DenableStable=true -Dcheck.parent.skip -U
